import request from '/@/utils/request'
export function getAlarmTypeAnalysis(query?: Object) {
	return request({
		url: '/report/alarm/getAlarmTypeAnalysis',
		method: 'get',
        params: query
	})
}
export function getCountAlarmByRegion(query?: Object) {
	return request({
		url: '/report/alarm/countAlarmByRegion',
		method: 'get',
        params: query
	})
}
export function getAlarmDayTrend(query?: Object) {
	return request({
		url: '/report/alarm/alarmDayTrend',
		method: 'get',
        params: query
	})
}
export function getAlarmMonthTrend(query?: Object) {
	return request({
		url: '/report/alarm/alarmMonthTrend',
		method: 'get',
        params: query
	})
}
export function getAlarmYearTrend(query?: Object) {
	return request({
		url: '/report/alarm/alarmYearTrend',
		method: 'get',
        params: query
	})
}
export function getAlarmResAnalysis(query?: Object) {
	return request({
		url: '/report/alarm/alarmResponse',
		method: 'get',
        params: query
	})
}
export function getAlarmTrendTotal (query?: Object) {
	return request({
		url: '/report/alarm/recordCount',
		method: 'get',
        params: query
	})
}

export function getEventTypeAnalysis(query?: Object) {
	return request({
		url: '/report/event/getEventTypeAnalysis',
		method: 'get',
        params: query
	})
}
export function getEventRegion(query?: Object) {
	return request({
		url: '/report/event/countEventByRegion',
		method: 'get',
        params: query
	})
}
export function getEventDayTrend (query?: Object) {
	return request({
		url: '/report/event/eventDayTrend',
		method: 'get',
        params: query
	})
}
export function getEventMonthTrend (query?: Object) {
	return request({
		url: '/report/event/eventMonthTrend',
		method: 'get',
        params: query
	})
}
export function getEventYearTrend (query?: Object) {
	return request({
		url: '/report/event/eventYearTrend',
		method: 'get',
        params: query
	})
}
export function getEventHandle (query?: Object) {
	return request({
		url: '/report/event/eventResponse',
		method: 'get',
        params: query
	})
}
export function getEventTrendTotal (query?: Object) {
	return request({
		url: '/report/event/recordCount',
		method: 'get',
        params: query
	})
}