<template>
  <div class="layout-padding resource-management">
    <MainPage ref="mainPageRef" :deptData="deptData" v-show="!isShowEditor" @openEditor="openEditor" />
    <EditPage :editCode="editCode" :editRecord="editRecord" :selectCatalog="selectCatalog" v-if="isShowEditor"
      @backTo="backTo" />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { deptTree } from "/@/api/admin/dept";
// 引入组件
const MainPage = defineAsyncComponent(() => import("./main/index.vue"));
const EditPage = defineAsyncComponent(() => import("./edit/index.vue"));
const deptData = ref<any[]>([]);
const isShowEditor = ref(false);
const editCode = ref("");
const mainPageRef = ref();
const editRecord: any = ref(null);
const selectCatalog: any = ref(null);

const openEditor = (record?: any, code?: any, select_catalog?: any) => {
  isShowEditor.value = true;
  editRecord.value = record;
  editCode.value = code;
  selectCatalog.value = select_catalog;
};

const backTo = () => {
  isShowEditor.value = false;
  editRecord.value = null;
  editCode.value = "";
  mainPageRef.value.getDataList();
  mainPageRef.value.getInfoData()
};
// 初始化部门数据
const getDeptData = () => {
  // 获取部门数据
  deptTree().then((res) => {
    deptData.value = res.data;
  });
};
onMounted(() => {
  getDeptData();
});
</script>

<style scoped lang="scss">
@font-face {
  font-family: yjsz;
  src: url("/@/assets/fonts/yjsz.ttf");
  /* IE9+,可以是具体的实际链接 */
}

@font-face {
  font-family: ast;
  src: url("/@/assets/fonts/ast.ttf");
  /* IE9+,可以是具体的实际链接 */
}

@font-face {
  font-family: cst;
  src: url("/@/assets/fonts/cst.ttf");
  /* IE9+,可以是具体的实际链接 */
}

@font-face {
  font-family: kjt;
  src: url("/@/assets/fonts/kjt.ttf");
  /* IE9+,可以是具体的实际链接 */
}

.resource-management {
  height: 100%;
  width: 100%;
  background-color: #f5f7fa;
}

.full-box {
  width: 100%;
  height: 100%;
}

::v-deep(.el-button--primary) {
  --el-color-primary: #A12F2F;
  --el-color-primary-dark-2: #8f2a2a;
  --el-color-primary-light-3: #c25a5a;
  --el-color-primary-light-5: #d78787;
  --el-color-primary-light-7: #ebbcbc;
  --el-color-primary-light-8: #f3d8d8;
  --el-color-primary-light-9: #f9ecec;
}
</style>
<style lang="scss">
.mt-cascader {
  max-height: 450px;

  .el-cascader-menu__wrap.el-scrollbar__wrap {
    height: auto !important;
  }
}
</style>