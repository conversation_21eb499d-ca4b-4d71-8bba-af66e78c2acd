import request from "/@/utils/request";

// 获取资源的数据统计
export function getResourceSummaryCount(params?: Object) {
  return request({
    url: "/datacenter/resource/summary/count",
    method: "GET",
    params,
  });
}


// 统计相关使用数、已借阅
export function getArchiveSummaryCount(params?: Object) {
    return request({
      url: "/datacenter/archive/summary/count",
      method: "GET",
      params,
    });
  }


// 资源的审核列表
export function getResourceApplyList(params?: Object) {
  return request({
    url: "/datacenter/resource/apply/my/audit/list",
    method: "GET",
    params,
  });
}


// 资源概览
export function resourceOverview(data?: Object) {
  return request({
    url: "/datacenter/data/resource/resourceOverview",
    method: "POST",
    data
  });
}

 // 消息统计
export function getMessageCount(params?: Object) {
  return request({
    url: "/admin/sysMessage/user/count",
    method: "GET",
    params,
  });
}

// 数据填报动态/数据使用动态
export function dynamicWrite(data?: Object) {
  return request({
    url: "/datacenter/operate/log/dynamicWrite",
    method: "POST",
    data,
  });
}


// 资源总量趋势图
export function getResourceTotalTrendChart(data?: Object) {
  return request({
    url: "/datacenter/data/resource/resourceTotalTrendChart",
    method: "POST",
    data,
  });
}
   
