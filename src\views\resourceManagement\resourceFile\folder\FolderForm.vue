<template>
	<el-dialog
		class=""
		v-model="visible"
		:width="600"
		:title="form.id ? '编辑' : '新增' + formLabelName"
		align-center
		destroy-on-close
		:show-close="true"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		@close="onCancel"
	>
		<el-form ref="dataFormRef" :model="form" :rules="dataRules" label-width="110px" v-loading="loading">
			<el-form-item :label="`${formLabelName}名称`" prop="catalogName">
				<el-input v-model="form.catalogName" :placeholder="`请输入${formLabelName}名称`" :maxlength="16" show-word-limit />
			</el-form-item>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="onCancel">取消</el-button>
				<el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { useMessage } from '/@/hooks/message';
import { addFolder, editFolder } from '/@/api/resource/resourceFile';
const props = defineProps({
	currentParentList: {
		type: <any>Array,
		default: () => [],
	},
});
const emit = defineEmits(['refresh']);
const formLabelName = computed(() => {
	return props.currentParentList?.length ? '文件夹' : '目录';
});

const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);

// 提交表单数据
const form = reactive({
	id: null,
	catalogName: '',
});

// 定义校验规则
const dataRules = ref({
	catalogName: [{ required: true, message: `请输入${formLabelName.value}名称`, trigger: 'blur' }],
});

// 打开弹窗，赋值
const openDialog = (record?: any) => {
	visible.value = true;
	form.id = null;
	form.catalogName = '';
	if (record?.id) {
		Object.assign(form, record);
	}
};

// 关闭
const onCancel = () => {
	visible.value = false;
};

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;
	let data: any = {};
	try {
		loading.value = true;
		if (form.id) {
			await editFolder(form);
		} else {
			let len = props.currentParentList.length;
			let parentId = len >= 1 ? props.currentParentList[len - 1].id : 0;
			data = {
				catalogState: 1,
				catalogParentId: parentId,
				catalogName: form.catalogName,
				catalogLevel: len + 1,
			};
			await addFolder(data);
		}
		useMessage().success(form.id ? '编辑成功' : '创建成功');
		visible.value = false;
		emit('refresh');
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
		loading.value = false;
	}
};

defineExpose({
	openDialog,
});
</script>

<style lang="scss" scoped></style>
