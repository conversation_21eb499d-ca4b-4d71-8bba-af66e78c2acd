<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑场景' : '新增场景'"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      class="scene-form"
    >
      <el-form-item label="场景名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入场景名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="场景描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入场景描述"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="封面图片" prop="coverImage">
        <div class="cover-upload">
          <el-upload
            class="cover-uploader"
            :show-file-list="false"
            :on-success="handleCoverSuccess"
            :before-upload="beforeCoverUpload"
            action="/api/upload"
          >
            <img v-if="formData.coverImage" :src="formData.coverImage" class="cover-image" />
            <div v-else class="cover-placeholder">
              <el-icon class="cover-icon"><Plus /></el-icon>
              <div class="cover-text">上传封面</div>
            </div>
          </el-upload>
          <div class="upload-tips">
            <p>建议尺寸：16:9，支持 JPG、PNG 格式，大小不超过 2MB</p>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio label="1">启用</el-radio>
          <el-radio label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="排序" prop="sort">
        <el-input-number
          v-model="formData.sort"
          :min="0"
          :max="9999"
          placeholder="请输入排序值"
          style="width: 200px"
        />
        <span class="form-tip">数值越小排序越靠前</span>
      </el-form-item>

      <el-form-item label="标签" prop="tags">
        <el-select
          v-model="formData.tags"
          multiple
          filterable
          allow-create
          placeholder="请选择或输入标签"
          style="width: 100%"
        >
          <el-option
            v-for="tag in tagOptions"
            :key="tag.value"
            :label="tag.label"
            :value="tag.value"
          />
        </el-select>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { useMessage } from '/@/hooks/message';
import { Plus } from '@element-plus/icons-vue';

interface Props {
  visible: boolean;
  editData?: any;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm'): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  editData: null,
});

const emit = defineEmits<Emits>();

const { success, error } = useMessage();

// 响应式数据
const formRef = ref();
const loading = ref(false);

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

const isEdit = computed(() => !!props.editData);

const formData = reactive({
  name: '',
  description: '',
  coverImage: '',
  status: '1',
  sort: 0,
  tags: [] as string[],
});

const formRules = {
  name: [
    { required: true, message: '请输入场景名称', trigger: 'blur' },
    { min: 2, max: 50, message: '场景名称长度在 2 到 50 个字符', trigger: 'blur' },
  ],
  description: [
    { max: 200, message: '描述长度不能超过 200 个字符', trigger: 'blur' },
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' },
  ],
  sort: [
    { required: true, message: '请输入排序值', trigger: 'blur' },
    { type: 'number', min: 0, max: 9999, message: '排序值范围为 0-9999', trigger: 'blur' },
  ],
};

const tagOptions = ref([
  { label: '道教文化', value: '道教文化' },
  { label: '古建筑', value: '古建筑' },
  { label: '自然风光', value: '自然风光' },
  { label: '历史遗迹', value: '历史遗迹' },
  { label: '宗教圣地', value: '宗教圣地' },
]);

// 方法
const handleClose = () => {
  dialogVisible.value = false;
  resetForm();
};

const resetForm = () => {
  formRef.value?.resetFields();
  Object.assign(formData, {
    name: '',
    description: '',
    coverImage: '',
    status: '1',
    sort: 0,
    tags: [],
  });
};

const handleConfirm = async () => {
  try {
    await formRef.value?.validate();
    loading.value = true;

    // TODO: 调用API保存数据
    await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟API调用

    success(isEdit.value ? '更新成功' : '创建成功');
    emit('confirm');
  } catch (err) {
    if (err !== false) { // 不是表单验证错误
      error(isEdit.value ? '更新失败' : '创建失败');
    }
  } finally {
    loading.value = false;
  }
};

const handleCoverSuccess = (response: any) => {
  if (response.code === 200) {
    formData.coverImage = response.data.url;
    success('封面上传成功');
  } else {
    error('封面上传失败');
  }
};

const beforeCoverUpload = (file: File) => {
  const isJPGOrPNG = file.type === 'image/jpeg' || file.type === 'image/png';
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isJPGOrPNG) {
    error('封面图片只能是 JPG 或 PNG 格式!');
    return false;
  }
  if (!isLt2M) {
    error('封面图片大小不能超过 2MB!');
    return false;
  }
  return true;
};

// 监听编辑数据变化
watch(
  () => props.editData,
  (newData) => {
    if (newData) {
      Object.assign(formData, {
        name: newData.name || '',
        description: newData.description || '',
        coverImage: newData.coverImage || '',
        status: newData.status || '1',
        sort: newData.sort || 0,
        tags: newData.tags || [],
      });
    }
  },
  { immediate: true }
);
</script>

<style scoped lang="scss">
.scene-form {
  .form-tip {
    margin-left: 12px;
    color: #909399;
    font-size: 12px;
  }
}

.cover-upload {
  .cover-uploader {
    :deep(.el-upload) {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: all 0.3s;
      width: 200px;
      height: 112px;

      &:hover {
        border-color: #409eff;
      }
    }
  }

  .cover-image {
    width: 200px;
    height: 112px;
    object-fit: cover;
    display: block;
  }

  .cover-placeholder {
    width: 200px;
    height: 112px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #8c939d;

    .cover-icon {
      font-size: 28px;
      margin-bottom: 8px;
    }

    .cover-text {
      font-size: 14px;
    }
  }

  .upload-tips {
    margin-top: 8px;

    p {
      margin: 0;
      color: #909399;
      font-size: 12px;
      line-height: 1.4;
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
