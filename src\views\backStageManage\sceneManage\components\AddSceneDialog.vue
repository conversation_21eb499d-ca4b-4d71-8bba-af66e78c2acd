<template>
	<el-dialog v-model="dialogVisible" :title="isEdit ? '编辑场景' : '新增场景'" width="600px" :close-on-click-modal="false" @close="handleClose">
		<el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px" class="scene-form">
			<el-form-item label="场景名称" prop="sceneName" required>
				<el-input v-model="formData.sceneName" placeholder="请输入" maxlength="50" show-word-limit />
			</el-form-item>

			<el-form-item label="场景类型" prop="sceneType" required>
				<el-radio-group v-model="formData.sceneType">
					<el-radio :label="1">主线场景</el-radio>
					<el-radio :label="2">专题场景</el-radio>
				</el-radio-group>
			</el-form-item>

			<el-form-item label="场景简介" prop="description">
				<el-input v-model="formData.description" type="textarea" :rows="5" placeholder="请输入内容" maxlength="200" show-word-limit />
			</el-form-item>

			<el-form-item label="场景分幕" prop="screenQuantity" required>
				<el-input-number
					v-model="formData.screenQuantity"
					:min="1"
					:max="999"
					placeholder="请输入数量"
					style="width: 200px; margin: 0 8px"
				/>
			</el-form-item>

			<el-form-item label="封面" prop="cover">
				<div class="cover-upload-section">
					<ImageUpload
						v-model:imageUrl="formData.cover"
						width="120px"
						height="120px"
						borderRadius="6px"
						uploadFileUrl="/exhibition/buildingResourceFile/cover"
					>
						<template #empty>
							<div class="upload-placeholder">
								<el-icon class="upload-icon"><Plus /></el-icon>
								<div class="upload-text">上传封面</div>
							</div>
						</template>
					</ImageUpload>
					<div class="upload-hint">
						<p>图片支持JPG/PNG格式且最大5M</p>
					</div>
				</div>
			</el-form-item>
		</el-form>

		<template #footer>
			<div class="dialog-footer">
				<el-button @click="handleClose">取消</el-button>
				<el-button type="primary" @click="handleConfirm" :loading="loading"> 确定 </el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, defineAsyncComponent } from 'vue';
import { useMessage } from '/@/hooks/message';
import { Plus } from '@element-plus/icons-vue';
import { addScene, updateScene } from '/@/api/backStageManage/scene';

// 异步组件
const ImageUpload = defineAsyncComponent(() => import('/@/components/Upload/Image.vue'));

interface Props {
	visible: boolean;
	editData?: any;
}

interface Emits {
	(e: 'update:visible', value: boolean): void;
	(e: 'confirm'): void;
}

const props = withDefaults(defineProps<Props>(), {
	visible: false,
	editData: null,
});

const emit = defineEmits<Emits>();

const { success, error } = useMessage();

// 响应式数据
const formRef = ref();
const loading = ref(false);

const dialogVisible = computed({
	get: () => props.visible,
	set: (value) => emit('update:visible', value),
});

const isEdit = computed(() => !!props.editData?.sceneId);

const formData = reactive({
	sceneName: '',
	sceneType: 1, // 1主线场景，2专题场景
	screenQuantity: 1, // 场景分幕数量
	cover: '',
	description: '',
	status: 1, // 1草稿，2已发布
});

const formRules = {
	sceneName: [
		{ required: true, message: '请输入场景名称', trigger: 'blur' },
		{ min: 2, max: 50, message: '场景名称长度在 2 到 50 个字符', trigger: 'blur' },
	],
	sceneType: [{ required: true, message: '请选择场景类型', trigger: 'change' }],
	screenQuantity: [
		{ required: true, message: '请输入场景分幕数量', trigger: 'blur' },
		{ type: 'number', min: 0, max: 999, message: '场景分幕数量范围为 0-999', trigger: 'blur' },
	],
	description: [{ max: 200, message: '描述长度不能超过 200 个字符', trigger: 'blur' }],
};

// 方法
const handleClose = () => {
	dialogVisible.value = false;
	resetForm();
};

const resetForm = () => {
	formRef.value?.resetFields();
	Object.assign(formData, {
		sceneName: '',
		sceneType: 1,
		screenQuantity: 0,
		cover: '',
		description: '',
		status: 1,
	});
};

const handleConfirm = async () => {
	try {
		await formRef.value?.validate();
		loading.value = true;

		const submitData: any = {
			...formData,
		};

		if (isEdit.value && props.editData?.sceneId) {
			submitData.sceneId = props.editData.sceneId;
			await updateScene(submitData);
		} else {
			await addScene(submitData);
		}

		success(isEdit.value ? '更新成功' : '创建成功');
		emit('confirm');
	} catch (err) {
		if (err !== false) {
			// 不是表单验证错误
			error(isEdit.value ? '更新失败' : '创建失败');
		}
	} finally {
		loading.value = false;
	}
};

// 监听编辑数据变化
watch(
	() => props.editData,
	(newData) => {
		if (newData) {
			Object.assign(formData, {
				sceneName: newData.sceneName || '',
				sceneType: newData.sceneType || 1,
				screenQuantity: newData.screenQuantity || 0,
				cover: newData.cover || '',
				description: newData.description || '',
				status: newData.status || 1,
			});
		}
	},
	{ immediate: true }
);
</script>

<style scoped lang="scss">
.scene-form {
	.form-tip {
		margin-left: 12px;
		color: #909399;
		font-size: 12px;
	}
}

.cover-upload-section {
	display: flex;
	flex-direction: column;
	gap: 8px;

	.upload-placeholder {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		color: #8c939d;
		height: 100%;

		.upload-icon {
			font-size: 28px;
			margin-bottom: 8px;
		}

		.upload-text {
			font-size: 14px;
		}
	}

	.upload-hint {
		p {
			margin: 0;
			color: #909399;
			font-size: 12px;
			line-height: 1.4;
		}
	}
}

.dialog-footer {
	text-align: right;
}
</style>
