<template>
  <div class="example-container">
    <h2>权限申请模态框使用示例</h2>
    
    <div class="demo-section">
      <el-button type="primary" @click="openApplyModal">
        申请权限
      </el-button>
    </div>

    <!-- 权限申请模态框 -->
    <ApplyModal
      v-model:visible="applyModalVisible"
      :resource-id="currentResourceId"
      :resource-title="currentResourceTitle"
      @close="handleApplyModalClose"
      @submit="handleApplySubmit"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import ApplyModal from './applyModal.vue'

// 响应式数据
const applyModalVisible = ref(false)
const currentResourceId = ref('resource-456')
const currentResourceTitle = ref('示例资源文档')

// 方法
const openApplyModal = () => {
  applyModalVisible.value = true
}

const handleApplyModalClose = () => {
  console.log('权限申请模态框已关闭')
}

const handleApplySubmit = (applyData: any) => {
  console.log('权限申请已提交:', applyData)
  ElMessage.success('权限申请已提交成功！')
  
  // 这里可以添加提交后的逻辑，比如：
  // 1. 刷新申请状态
  // 2. 更新UI显示
  // 3. 发送通知等
}
</script>

<style lang="scss" scoped>
.example-container {
  padding: 20px;
  
  h2 {
    margin-bottom: 20px;
    color: #303133;
  }
  
  .demo-section {
    padding: 20px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    background-color: #fafafa;
  }
}
</style>
