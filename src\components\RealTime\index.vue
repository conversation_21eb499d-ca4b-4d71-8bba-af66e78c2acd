<template>
	<div class="videoDiv">
		<video class="people-monitor" :id="props.id" autoplay></video>
	</div>
</template>

<script setup lang="ts">
import { useFlv } from '/@/hooks/flv';

interface Props {
	flvUrl: string;
	id: string;
}

const props = defineProps<Props>();

useFlv(props.id, props.flvUrl);
</script>

<style lang="scss" scoped>
.videoDiv {
	width: 100%;
	height: 100%;
	text-align: center;
	.people-monitor {
		background: #000;
		display: inline-block;
		height: 100%;
	}
}
</style>
