import request from '/@/utils/request';

/**
 * 添加档案
 *
 */
export function addArchive(data: any) {
  return request({
    url: '/datacenter/archive/addArchive',
    method: 'post',
    data
  });
}

/**
 * 列表
 *
 */
export function getList(data: any) {
  return request({
    url: '/datacenter/archive/pageArchive',
    method: 'post',
    data
  });
}

/**
 * 字段列表
 *
 */
export function getField(data: any) {
  return request({
    url: '/datacenter/archive/listColumns',
    method: 'post',
    data
  });
}


/**
 * 档案文件列表
 *
 */
export function getFileList(data: any) {
  return request({
    url: '/datacenter/archive/listArchiveFiles',
    method: 'post',
    data
  });
}

/**
 * 删除档案的文件
 *
 */
export function delFile(data: any) {
  return request({
    url: '/datacenter/archive/delArchiveFile',
    method: 'post',
    data
  });
}

/**
 * 编辑档案数据
 *
 */
export function editArchive(data: any) {
  return request({
    url: '/datacenter/archive/editArchive',
    method: 'post',
    data
  });
}

/**
 * 档案导出
 *
 */
export function exportArchive(data: any) {
  return request({
    url: '/datacenter/archive/exportArchive',
    method: 'post',
    data
  });
}

/**
 * 批量删除档案数据
 *
 */
export function batchDelArchive(data: any) {
  return request({
    url: '/datacenter/archive/batchDelArchive',
    method: 'post',
    data
  });
}

/**
 * 删除档案数据
 *
 */
export function delArchive(data: any) {
  return request({
    url: '/datacenter/archive/delArchive',
    method: 'post',
    data
  });
}

/**
 * 档案文件上传--只支持单个文件
 *
 */
export function uploadFile(data: any) {
  return request({
    url: '/datacenter/archive/uploadFile',
    method: 'post',
    data
  });
}

/**
 * 保存文件排序
 *
 */
export function saveFileSort(data: any) {
  return request({
    url: '/datacenter/archive/saveFileSort',
    method: 'post',
    data
  });
}

/**
 * 操作日志列表
 *
 */
export function operateLogList(params: any) {
  return request({
    url: '/datacenter/operate/log/page',
    method: 'get',
    params
  });
}

/**
 * 档案导出
 *
 */
export function exportFile(data: any) {
  return request({
		url: '/datacenter/archive/exportArchive',
		method: 'post',
		responseType: 'blob',
		data,
	})
}

/**
 * 批量下载
 *
 */
export function batchDownloadFile(data: any) {
  return request({
		url: '/datacenter/archive/batchDownloadFile',
		method: 'post',
		responseType: 'blob',
		data,
	})
}


/**
 * 下载
 *
 */
export function downloadFile(data: any) {
  return request({
		url: '/datacenter/archive/downloadFile',
		method: 'post',
		responseType: 'blob',
		data,
	})
}

/**
 * 批量归档
 *
 */
export function batchArchive(data: any) {
  return request({
		url: '/datacenter/archive/batchArchive',
		method: 'post',
		data,
	})
}

// 对档案进行鉴定
export function addIdentify(data?: Object) {
  return request({
      url: "/datacenter/archive/identify/add",
      method: "post",
      data: data,
  });
}

// 档案详情
export function queryArchiveDetail(data?: Object) {
  return request({
      url: "/datacenter/archive/archiveDetail",
      method: "post",
      data: data,
  });
}


// 文件下载权限
export function searchInfo(data?: Object) {
  return request({
      url: "/datacenter/archive/searchInfo",
      method: "post",
      data: data,
  });
}
