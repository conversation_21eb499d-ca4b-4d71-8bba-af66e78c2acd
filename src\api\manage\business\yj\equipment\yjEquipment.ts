import request from "/@/utils/request"

export function fetchList(query?: Object) {
  return request({
    url: '/business/yjEventInfo/byPage',
    method: 'get',
    params: query
  })
}

export function addObj(obj?: Object) {
  return request({
    url: '/business/yjEventInfoDynamic',
    method: 'post',
    data: obj
  })
}

export function getObj(id?: string) {
  return request({
    url: '/business/yjEventInfo/getEventInfoDynamic',
    method: 'get',
    params: {id:id}
  })
}

export function delObjs(ids?: Object) {
  return request({
    url: '/business/yjEventInfo',
    method: 'delete',
    data: ids
  })
}

export function putObj(obj?: Object) {
  return request({
    url: '/business/yjEventInfo',
    method: 'put',
    data: obj
  })
}

export function cancelObjs(ids?: Array<string>) {
  return request({
    url: '/business/yjEventInfo/updateByIds',
    method: 'post',
    data: ids
  })
}