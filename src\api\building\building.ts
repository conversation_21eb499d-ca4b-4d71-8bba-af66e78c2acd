import request from '/@/utils/request';

/**
 * 获取建筑空间树形数据
 * @param params 查询参数
 * @returns 
 */
export function getBuildingTree(params?: any) {
  return request({
    url: '/api/building/tree',
    method: 'get',
    params,
  });
}

/**
 * 新增或更新建筑空间
 * @param data 建筑空间数据
 * @returns 
 */
export function addorUpdateBuilding(data: any) {
  return request({
    url: '/api/building/addorUpdate',
    method: 'post',
    data,
  });
}

/**
 * 删除建筑空间
 * @param id 建筑空间ID
 * @returns 
 */
export function deleteBuilding(id: string) {
  return request({
    url: `/api/building/${id}`,
    method: 'delete',
  });
}

/**
 * 获取建筑类型列表
 * @returns
 */
export function getBuildingType() {
  return request({
    url: '/api/building/types',
    method: 'get',
  });
}

/**
 * 获取建筑空间详情
 * @param id 建筑空间ID
 * @returns
 */
export function getBuildingDetail(id: string) {
  return request({
    url: `/api/building/${id}`,
    method: 'get',
  });
}
