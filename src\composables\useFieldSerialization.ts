/**
 * 元数据字段序列化逻辑
 * 将字段规则序列化逻辑抽离为可复用的组合式函数
 */

export interface FieldData {
  id?: string | number;
  tableInfoId: string | number;
  groupId: string | number;
  name: string;
  cnName: string;
  typeCode: string;
  prompt?: string;
  remark?: string;
  columnRule?: string;
  columnCustomeRule?: string;
  // 基本规则
  mustFilee?: boolean;
  uniqueValue?: boolean;
  readOnly?: boolean;
  desensitization?: boolean;
  zljc?: string[];
  // 文本类型字段
  input_minLength?: number;
  input_maxLength?: number;
  input_defaultVal?: string;
  // 数字类型字段
  number_unit?: string;
  number_format?: string;
  number_point?: number;
  number_range?: number[];
  number_showUppercase?: boolean;
  // 日期类型字段
  date_format?: string;
  date_range?: string[];
  // 选项类型字段
  options_list?: any[];
  // 地区类型字段
  area_format?: string;
  area_province?: string;
  // 上传类型字段
  upload_fileCount?: number;
  upload_fileSize?: number;
}

export function useFieldSerialization() {
  /**
   * 序列化字段规则数据
   * @param formData 字段表单数据
   * @returns 序列化后的字段数据，准备发送到服务端
   */
  const serializeFieldData = (formData: FieldData): FieldData => {
    const serializedData = { ...formData };
    
    // 序列化基本规则
    serializedData.columnRule = JSON.stringify({
      mustFilee: formData.mustFilee,
      uniqueValue: formData.uniqueValue,
      readOnly: formData.readOnly,
      desensitization: formData.desensitization,
      zljc: formData.zljc,
    });

    // 根据字段类型序列化自定义规则
    if (["oneLine", "multiLine", "richText"].includes(formData.typeCode)) {
      serializedData.columnCustomeRule = JSON.stringify({
        minLength: formData.input_minLength,
        maxLength: formData.input_maxLength,
        defaultVal: formData.input_defaultVal,
      });
    } else if (["number"].includes(formData.typeCode)) {
      serializedData.columnCustomeRule = JSON.stringify({
        unit: formData.number_unit,
        format: formData.number_format,
        point: formData.number_point,
        range: formData.number_range,
      });
    } else if (["amount"].includes(formData.typeCode)) {
      serializedData.columnCustomeRule = JSON.stringify({
        unit: formData.number_unit,
        showUppercase: formData.number_showUppercase,
        point: formData.number_point,
        range: formData.number_range,
      });
    } else if (["date", "dateRange"].includes(formData.typeCode)) {
      serializedData.columnCustomeRule = JSON.stringify({
        format: formData.date_format,
        range: formData.date_range,
      });
    } else if (["ratioOption", "multiOption"].includes(formData.typeCode)) {
      serializedData.columnCustomeRule = JSON.stringify(formData.options_list);
    } else if (["province"].includes(formData.typeCode)) {
      serializedData.columnCustomeRule = JSON.stringify({
        format: formData.area_format,
        province: formData.area_province,
      });
    } else if (["image", "attach"].includes(formData.typeCode)) {
      serializedData.columnCustomeRule = JSON.stringify({
        fileCount: formData.upload_fileCount,
        fileSize: formData.upload_fileSize,
      });
    }

    return serializedData;
  };

  /**
   * 解析服务端返回的字段数据
   * @param fieldData 服务端字段数据
   * @returns 解析后的字段表单数据
   */
  const deserializeFieldData = (fieldData: any): FieldData => {
    const formData: FieldData = {
      ...fieldData,
      tableInfoId: fieldData.tableInfoId,
      groupId: fieldData.groupId,
      name: fieldData.name,
      cnName: fieldData.cnName,
      typeCode: fieldData.typeCode || "oneLine",
      prompt: fieldData.prompt,
      remark: fieldData.remark,
    };

    // 解析基本规则
    if (fieldData.columnRule) {
      try {
        const columnRule = JSON.parse(fieldData.columnRule);
        formData.mustFilee = columnRule.mustFilee;
        formData.uniqueValue = columnRule.uniqueValue;
        formData.readOnly = columnRule.readOnly;
        formData.desensitization = columnRule.desensitization;
        formData.zljc = columnRule.zljc || [];
      } catch (e) {
        // 解析失败时设置默认值
        formData.mustFilee = false;
        formData.uniqueValue = false;
        formData.readOnly = false;
        formData.desensitization = false;
        formData.zljc = [];
      }
    } else {
      formData.mustFilee = false;
      formData.uniqueValue = false;
      formData.readOnly = false;
      formData.desensitization = false;
      formData.zljc = [];
    }

    // 解析自定义规则
    if (fieldData.columnCustomeRule) {
      try {
        const customRule = JSON.parse(fieldData.columnCustomeRule);

        if (["oneLine", "multiLine", "richText"].includes(fieldData.typeCode)) {
          formData.input_minLength = customRule.minLength;
          formData.input_maxLength = customRule.maxLength;
          formData.input_defaultVal = customRule.defaultVal;
        } else if (["number"].includes(fieldData.typeCode)) {
          formData.number_unit = customRule.unit;
          formData.number_format = customRule.format;
          formData.number_point = customRule.point;
          formData.number_range = customRule.range;
        } else if (["amount"].includes(fieldData.typeCode)) {
          formData.number_unit = customRule.unit;
          formData.number_showUppercase = customRule.showUppercase;
          formData.number_point = customRule.point;
          formData.number_range = customRule.range;
        } else if (["date", "dateRange"].includes(fieldData.typeCode)) {
          formData.date_format = customRule.format;
          formData.date_range = customRule.range;
        } else if (["ratioOption", "multiOption"].includes(fieldData.typeCode)) {
          formData.options_list = customRule;
        } else if (["province"].includes(fieldData.typeCode)) {
          formData.area_format = customRule.format;
          formData.area_province = customRule.province;
        } else if (["image", "attach"].includes(fieldData.typeCode)) {
          formData.upload_fileCount = customRule.fileCount;
          formData.upload_fileSize = customRule.fileSize;
        }
      } catch (e) {
        // 解析失败时使用默认值，不输出错误信息
      }
    }

    return formData;
  };

  return {
    serializeFieldData,
    deserializeFieldData,
  };
}
