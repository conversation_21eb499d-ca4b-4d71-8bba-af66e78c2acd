<template>
  <div class="table-container">
    <div class="table-title-box">我的申请</div>
    <div class="table-bar-box">
      <div :class="`bar-item ${item.itemNo == activeTab ? 'active' : ''}`"
        :key="item.itemNo"
        v-for="item in barList"
        @click="handleTabChange(item.itemName, item.itemNo)"
      >
        <span class="bar-label">
          {{ item.itemName }}
        </span>
        <span class="bar-value ml-2">
          {{ item.itemCount }}
        </span>
      </div>
    </div>
    <div class="table-content-box">
      <el-table
        v-loading="state.loading"
        :data="state.dataList"
        class="custom-table-control"
        :cell-style="tableStyle.cellStyle"
        :header-cell-style="tableStyle.headerCellStyle"
      >
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="resourceName" label="资源名称" min-width="120" show-overflow-tooltip />
        <el-table-column prop="applyAccess" label="申请权限" width="80" align="center">
          <template #default="scope">
            {{ metaConfig.applyAccess[scope.row.applyAccess] || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="申请时间" width="160" align="center" />
        <el-table-column prop="useTo" label="资源用途" min-width="80" show-overflow-tooltip />
        <el-table-column prop="applyReason" label="申请事由" min-width="100" show-overflow-tooltip />
        <el-table-column prop="applyStatus" label="审批状态" width="80" align="center">
          <template #default="scope">
            <span :style="{color: metaConfig.applyStatus_color[scope.row.applyStatus]}">
              {{ metaConfig.applyStatus[scope.row.applyStatus] || '-' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" align="center">
          <template #default="scope">
            <el-button 
              link
              :type="scope.row.applyStatus != 2 ? 'default' : 'primary'"
              @click="onOperation('DoView', scope.row)"
              class="action-btn"
              :disabled="scope.row.applyStatus != 2"
            >
              查看资源
            </el-button>
            <el-button 
              link
              :type="scope.row.applyStatus != 1 ? 'default' : 'primary'"
              @click="onOperation('DoCancel', scope.row)"
              class="action-btn"
              :disabled="scope.row.applyStatus != 1"
            >
              取消申请
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <div class="pagination-wrapper">
      <el-pagination
        v-bind="state.pagination"
        @current-change="currentChangeHandle"
        @size-change="sizeChangeHandle"
        background
      >
      </el-pagination>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { BasicTableProps, useTable } from "/@/hooks/table";
import { useMessage, useMessageBox } from "/@/hooks/message";
import { queryList, applyCancel, queryApplyCount } from "/@/api/resourceManagement/applicationAndApproval/application";

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({
  queryForm: {
    resourceName: undefined,
    applyAccess: undefined,
    applyStatus: undefined,
  },
  pageList: queryList,
  props: {
    item: "records",
    totalCount: "total",
  },
  isPage: true,
  createdIsNeed: false,
});

const {
  getDataList,
  currentChangeHandle,
  sizeChangeHandle,
  tableStyle,
} = useTable(state);

const activeTab = ref("");
const barList = ref<any>([]);

const metaConfig: any = reactive({
  applyAccess: {
    1: "查看",
    2: "下载",
  },
  applyStatus: {
    1: "审批中",
    2: "已同意",
    3: "已拒绝",
    4: "已取消",
  },
  applyStatus_color: {
    1: "#FFA73A",
    2: "#19B500",
    3: "#E02727",
    4: "#a9a9a9",
  },
});

// 处理标签切换
const handleTabChange = (label: string, value: string) => {
  activeTab.value = value;
  state.queryForm.applyStatus = value;
  getDataList(1);
  updateTabCounts();
};

// 更新标签计数
const updateTabCounts = async () => {
  try {
    const res = await queryApplyCount();
    if (res.ok) {
      barList.value = (res.data || []).map((item: any) => {
        return {
          ...item,
          itemNo: item.itemNo || "",
        };
      });
    }
  } catch (err) {
    useMessage().error((err as any).msg || "操作失败");
  }
};

// 取消申请
const onCancel = async (ids: any) => {
  try {
    await useMessageBox().confirm("确认取消申请吗？");
  } catch {
    return;
  }
  
  try {
    await applyCancel({ids});
    useMessage().success("操作成功");
    getDataList(1);
    updateTabCounts();
  } catch (err) {
    useMessage().error((err as any).msg || "操作失败");
  }
};

// 操作处理
const onOperation = (type: string, record: any) => {
  switch (type) {
    case "DoView": {
      window.open(`/#/squareNew/resource/detail/${record.resourceId}?type=${record.tabName === 'lea_material' ? 1 : 2}&tabName=${record.tabName}`);
      break;
    }
    case "DoCancel":
      onCancel([record.id]);
      break;
  }
};

// 初始化数据
onMounted(() => {
  getDataList(1);
  updateTabCounts();
});
</script>

<style scoped lang="scss">
.table-container {
  .table-title-box {
    text-align: center;
    position: relative;
    font-weight: 900;
    font-size: 24px;
    line-height: 24.61px;
    letter-spacing: 0%;
    text-align: center;
    vertical-align: middle;
    color: #393939;
  }
  
  .table-bar-box {
    display: flex;
    justify-content: center;
    margin: 33px 0 33px;
    padding-bottom: 13px;
    border-bottom: 1px solid #eee;
    
    .bar-item {
      padding: 5px 16px;
      font-weight: 500;
      font-size: 14px;
      line-height: 24.61px;
      letter-spacing: 0%;
      color: #a1a1a1;
      margin-right: 16px;
      cursor: pointer;
      
      &.active {
        background-color: #862626;
        color: #ffffff;
        box-shadow: 0px 2px 0px 0px #0000000b;
        border-radius: 2px;
      }
    }
  }
  
  .table-content-box {
    position: relative;
    margin-bottom: 20px;
  }
  
  .pagination-wrapper {
    margin-top: 30px;
    margin-bottom: 20px;
    display: flex;
    justify-content: center;
    
    .custom-pagination-control {
      display: flex;
      justify-content: center;
    }
  }
}
</style>
