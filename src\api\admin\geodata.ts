import request from '/@/utils/request';

export const getArchitecturalSpaceTree = (params?: Object) => {
    return request({
        url: '/admin/sysArchitecturalSpace/getArchitecturalSpaceTree',
        method: 'get',
        params,
    });
};
export const getSysCodeListByField = (obj?: Object) => {
    return request({
        url: '/admin/sysCode/getSysCodeListByField',
        method: 'post',
        data: obj,
    });
};
export function fetchList(query?: Object) {
	return request({
		url: '/admin/sysArchitecturalSpace/page',
		method: 'get',
		params: query,
	});
}

export function addObj(obj?: Object) {
	return request({
		url: '/admin/sysArchitecturalSpace/add',
		method: 'post',
		data: obj,
	});
}

export function getObj(id?: string) {
	return request({
		url: '/admin/sysArchitecturalSpace/details/' + id,
		method: 'get',
	});
}

export function delObj(ids?: Object) {
	return request({
		url: '/admin/sysArchitecturalSpace',
		method: 'delete',
		data: ids,
	});
}

export function putObj(obj?: Object) {
	return request({
		url: '/admin/sysArchitecturalSpace/edit',
		method: 'put',
		data: obj,
	});
}

export function refreshCache() {
	return request({
		url: '/admin/sysArchitecturalSpace/sync',
		method: 'put',
	});
}

export function getObjDetails(obj?: object) {
	return request({
		url: '/admin/sysArchitecturalSpace/details',
		method: 'get',
		sysArchitecturalSpaces: obj,
	});
}

export function validatesysArchitecturalSpacesCode(rule: any, value: any, callback: any, isEdit: boolean) {
	if (isEdit) {
		return callback();
	}

	getObjDetails({ publicKey: value }).then((response) => {
		const result = response.data;
		if (result !== null) {
			callback(new Error('参数编码已经存在'));
		} else {
			callback();
		}
	});
}

export function validatesysArchitecturalSpacesName(rule: any, value: any, callback: any, isEdit: boolean) {
	if (isEdit) {
		return callback();
	}

	getObjDetails({ publicName: value }).then((response) => {
		const result = response.data;
		if (result !== null) {
			callback(new Error('参数名称已经存在'));
		} else {
			callback();
		}
	});
}
