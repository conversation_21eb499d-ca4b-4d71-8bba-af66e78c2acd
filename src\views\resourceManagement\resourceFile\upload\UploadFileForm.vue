<template>
	<el-dialog
		class="upload-form-dlg"
		v-model="visible"
		:width="610"
		:title="form.id ? '编辑资源文件' : '新增资源文件'"
		align-center
		destroy-on-close
		:show-close="form.fileList?.[0]?.stepCode == 'serverHanding' ? false : true"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		@close="onCancel"
	>
		<el-form ref="dataFormRef" :model="form" :rules="dataRules" label-width="110px" v-loading="loading">
			<div class="group-header flex items-center">
				<img class="group-title-icon" src="/@/assets/img/archives/icon_down_circle.png" />
				<div class="group-title">基本信息</div>
			</div>
			<el-form-item label="封面" prop="cover">
				<ImageUpload v-model:imageUrl="form.cover" height="80px" width="120px" borderRadius="0%" uploadFileUrl="/datacenter/learning/material/cover">
					<template #empty>
						<el-icon><Picture /></el-icon>
						<span>点击上传封面</span>
					</template>
				</ImageUpload>
				<div style="position: absolute; right: 0px; width: 300px; color: #999999">
					<div>图片支持JPG/PNG格式且最大5M</div>
				</div>
			</el-form-item>
			<el-form-item label="文件名称" prop="name">
				<el-input v-model="form.name" placeholder="请输入文件名称" :maxlength="16" show-word-limit />
			</el-form-item>
			<el-form-item label="文件类型" prop="type">
				<el-select v-model="form.type" clearable placeholder="请选择文件类型" :disabled="form.fileList.length > 0">
					<el-option v-for="item in fileTypeConfig" :key="Number(item.id)" :label="item.name" :value="Number(item.id)" />
				</el-select>
			</el-form-item>
			<el-form-item label="关联资源" prop="tabName">
				<el-tree-select
					v-model="form.tabName"
					:data="resourceCatalogTree"
					:render-after-expand="false"
					default-expand-all
					:props="{
						value: 'catalogNo',
						label: 'catalogName',
						children: 'childCatalogs',
						disabled: 'disabled',
					}"
					class="w100"
					clearable
					check-strictly
					filterable
					placeholder="请选择资源目录"
					@current-change="resourceCatalogSelectChangeHandle"
					@clear="form.resourceId = ''"
				>
					<template #default="{ data }">
						<el-icon v-if="data.catalogLevel == 4" style="margin-right: 3px; margin-top: -2px"><Document /></el-icon>
						<el-icon v-if="data.catalogLevel != 4" style="margin-right: 3px; margin-top: -2px"><FolderOpened /></el-icon>
						<span> {{ data.catalogName }}</span>
					</template>
				</el-tree-select>
			</el-form-item>
			<el-form-item label="" prop="resourceId">
				<el-select
					v-model="form.resourceId"
					:disabled="!form.tabName"
					clearable
					placeholder="请选择关联资源"
					filterable
				>
					<el-option v-for="item in resourceLsitData" :key="item.id" :label="item.assets_name" :value="item.id" />
				</el-select>
			</el-form-item>
			<el-form-item label="文件密级" prop="securityId">
				<el-radio-group v-model="form.securityId">
					<el-radio v-for="item in securityLevelOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="文件描述" prop="desc">
				<el-input type="textarea" maxlength="250" :rows="3" v-model="form.desc" placeholder="请输入文件描述" show-word-limit />
			</el-form-item>
			<el-form-item label="文件标签" prop="labels">
				<el-input maxlength="50" v-model="form.labels" placeholder="输入内容，多个标签用“，”隔开" show-word-limit />
			</el-form-item>
			<div class="group-header flex items-center">
				<img class="group-title-icon" src="/@/assets/img/archives/icon_down_circle.png" />
				<div class="group-title">管理信息</div>
			</div>
			<el-form-item label="来源单位" prop="deptId">
				<el-tree-select
					:data="props.deptData"
					:props="{ value: 'id', label: 'name', children: 'children' }"
					check-strictly
					class="w100"
					clearable
					placeholder="请选择来源单位"
					v-model="form.deptId"
				>
				</el-tree-select>
			</el-form-item>
			<div style="margin-bottom: 15px">
				<div class="group-header flex items-center">
					<img class="group-title-icon" src="/@/assets/img/archives/icon_down_circle.png" />
					<div class="group-title">文件上传</div>
				</div>
				<el-form-item label="" prop="fileOBJ" class="mt-form-item">
					<CustomUpload
						ref="customUploadRef"
						v-show="![8, 9].includes(form.type)"
						:uploadResType="form.type"
						:fileType="selectFileTypeOBJ?.fileType"
						:fileSize="selectFileTypeOBJ?.fileSize"
						:fileTypeOBJ="selectFileTypeOBJ"
						v-model="form.fileList"
						style="margin-left: -90px; width: calc(100% + 90px)"
						@fileUploadSuccess="fileUploadSuccessHandle"
						:format="getFormat()"
					/>
				</el-form-item>
			</div>
		</el-form>
		<template #footer>
			<span>
				<el-button @click="onCancel" :disabled="form.fileList?.[0]?.stepCode == 'serverHanding'">取消</el-button>
				<el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useMessage } from '/@/hooks/message';
import { fileTypeConfig, securityLevelOptions } from '/@/config/resourceConfig';
import { addObj, updateObj, getObj } from '/@/api/resource/filelist/file';
import { resourceFileUploaderStore } from '/@/stores/resourceFileUploader';
import { fetchListMaterial } from '/@/api/resource/data/resource';
const rfuStore = resourceFileUploaderStore();
const CustomUpload = defineAsyncComponent(() => import('./CustomUpload.vue'));
const ImageUpload = defineAsyncComponent(() => import('/@/components/Upload/Image.vue'));
const props = defineProps({
	deptData: {
		type: Array as any,
		default: () => [],
	},
	resourceCatalogTree: {
		type: Array as any,
		default: () => [],
	},
	currentParentList: {
		type: <any>Array,
		default: () => [],
	},
});
const emit = defineEmits(['refresh']);
const dataFormRef = ref();
const customUploadRef = ref();
const resourceLsitData = ref<any[]>([]);
const visible = ref(false);
const loading = ref(false);
const isSubmit = ref(false);

// 提交表单数据
const form: any = reactive({
	id: null,
	timeID: null,
	name: '',
	type: 6,
	tabName: '',
	resourceId: '',
	securityId: null,
	desc: '',
	cover: '',
	labels: '',
	lng: null,
	lat: null,
	fileOBJ: null,
	url: null,
	fileList: [] as any,
	fileRequestList: [] as any,
	file: '',
	objectKey: '',
	totalSize: '',
	format: 'jpg',
	mainFile: '',
	fileIdentifier: '',
});

// 定义校验规则
const dataRules = ref({
	name: [{ required: true, message: '文件名称不能为空', trigger: 'blur' }],
	type: [{ required: true, message: '请选择文件类型', trigger: 'blur' }],
	securityId: [{ required: true, message: '请选择文件密级', trigger: 'blur' }],
	fileOBJ: [{ required: true, message: '上传内容不能为空', trigger: 'blur' }],
	deptId: [{ required: true, message: '来源单位不能为空', trigger: 'blur' }],
});
const getFormat = () => {
	let format = '';
	if (form.type == 3) {
		format = 'shp';
	} else if (form.type == 1) {
		format = '3dtiles';
	} else if (form.type == 11) {
		format = 'obj';
	} else {
		form.fileList[0]?.name && (format = form.fileList[0].name.split('.')[1]);
	}
	return format;
};
// 打开弹窗，赋值
const openDialog = async (record?: any, isReupload = false, type?: any) => {
	isSubmit.value = false;
	dataFormRef.value?.resetFields();
	Object.assign(form, {
		id: null,
		timeID: null,
		name: '',
		type: 6,
		tabName: '',
		resourceId: '',
		securityId: '1',
		desc: '',
		cover: '',
		labels: '',
		lng: null,
		lat: null,
		fileOBJ: [],
		url: null,
		fileList: [] as any,
		fileRequestList: [] as any,
		file: '',
		objectKey: '',
		totalSize: '',
		format: 'jpg',
		mainFile: '',
		fileIdentifier: '',
		deptId: null,
	});
	if (isReupload) {
		// 重新上传
		Object.assign(form, record);
	} else if (record?.id) {
		const res = await getObj(record.id);
		res.data.type = Number(res.data.type);
		Object.assign(form, res.data);
		if (![8, 9].includes(res.data.type)) {
			form.fileList = res.data.fileRequest.map((obj: any) => {
				obj.name = obj.fileName;
				obj.result = { objectKey: obj.objectKey, fileId: obj.fileId };
				obj.url = res.data.url;
				obj.stepCode = 'success';
				obj.size = res.data.totalSize;
				obj.md5Percent = 100;
				obj.uploadingPercent = 100;
				obj.serverPercent = 100;
				return obj;
			});
		}
	}
	form.type && (form.type = Number(form.type));
	type && (form.type = Number(type));
	if (form?.tabName) {
		resourceLsitData.value = [];
		nextTick(async () => {
			let resourceParams: any = { tabName: form?.tabName, current: 1, size: 10000 };
			if (form.id) {
				resourceParams.byPassEs = 1;
			}
			if (form.resourceId) {
				resourceParams.resourceId = form.resourceId;
			}
			let res = await fetchListMaterial(resourceParams);
			resourceLsitData.value = res.data?.records || [];
		});
	}
	visible.value = true;
};
const selectFileTypeOBJ = computed(() => {
	return fileTypeConfig.find((obj: any) => {
		return obj.id == Number(form.type);
	});
});

const resourceCatalogSelectChangeHandle = (obj: any) => {
	if (obj.catalogRemark !== obj.catalogNo) {
		form.tabName = '';
		form.resourceId = '';
		resourceLsitData.value = [];
		return;
	}
	form.tabName = obj.catalogNo;
	form.resourceId = '';
	resourceLsitData.value = [];
	nextTick(async () => {
		let resourceParams: any = { tabName: form.tabName, current: 1, size: 10000 };
		if (form.id) {
			resourceParams.byPassEs = 1;
		}
		if (form.resourceId) {
			resourceParams.resourceId = form.resourceId;
		}
		let res = await fetchListMaterial(resourceParams);
		resourceLsitData.value = res.data?.records || [];
	});
};
// 关闭
const onCancel = () => {
	if (['md5', 'uploading'].includes(form.fileList?.[0]?.stepCode) && !isSubmit.value) {
		form.fileList?.[0]?.cancelFile?.();
	}
	visible.value = false;
};
const fileUploadSuccessHandle = (file: any) => {
	if ([6].includes(form.type) && !form.cover) {
		form.cover = file.result.url;
	}
};
// 提交
const onSubmit = async () => {
	if ([8, 9].includes(form.type)) {
		// 在线文档，直接保存即可
		form.fileOBJ = form.url;
	} else {
		form.fileOBJ = form.fileList;
	}
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;
	isSubmit.value = true;
	// 文件数据，需要考虑大数量下的后台上传情况
	if (form.fileList.length > 0 && form.fileList[0].stepCode != 'success') {
		let timeID = form.fileList[0].name + new Date().getTime();
		form.fileList[0].timeID = timeID;
		form.timeID = timeID;
		rfuStore.addFileList(form);
		customUploadRef.value?.submit();
		useMessage().success('后台数据上传中');
		visible.value = false;
	} else {
		return await doSave();
	}
};
const doSave = async () => {
	if (![8, 9].includes(form.type)) {
		form.fileRequestList = form.fileList.map((obj: any) => {
			return { fileId: obj.result.fileId, fileName: obj.name };
		});
		form.fileList[0].result.objectKey && (form.objectKey = form.fileList[0].result.objectKey);
		form.fileList[0].size && (form.totalSize = form.fileList[0].size);
		form.format = form.format || getFormat();
		form.fileList[0].fileIdentifier && (form.fileIdentifier = form.fileList[0].fileIdentifier);
		form.fileName = form.fileList[0].name;
	}
	let len = props.currentParentList.length;
	form.folderId = len >= 1 ? props.currentParentList[len - 1].id : 0;
	form.fileType = selectFileTypeOBJ.value?.name || '';
	// form.security = securityLevelOptions.find((obj: any) => {
	// 	return obj.value == form.securityId;
	// })?.label || '';
	try {
		loading.value = true;
		let form_obj = JSON.parse(JSON.stringify(form));
		delete form_obj.fileOBJ; // true
		delete form_obj.fileList; // true
		form.id ? await updateObj(form_obj) : await addObj(form_obj);
		useMessage().success(form.id ? '编辑成功' : '新增成功');
		visible.value = false;
		emit('refresh');
	} catch (err) {
		useMessage().error((err as any).msg);
	} finally {
		loading.value = false;
	}
};
onMounted(() => {});

defineExpose({
	openDialog,
});
</script>

<style lang="scss" scoped>
.upload-form-dlg {
	.group-header {
		margin-bottom: 10px;
		.group-title-icon {
			width: 16px;
			margin-top: -2px;
		}
		.group-title {
			font-size: 14px;
			font-weight: 600;
			padding-left: 7px;
			position: relative;
			color: #554242;
			letter-spacing: 0.1em;
		}

		.group-add {
			cursor: pointer;
			color: var(--el-color-primary);
		}
	}
	.mt-form-item {
		::v-deep(.el-form-item__content) {
			padding-bottom: 15px;
		}
	}
}
</style>

<style lang="scss">
.upload-form-dlg {
	.el-dialog__body {
		height: 75vh;
		overflow-y: auto;
	}
}
.mt-cascader {
	max-height: 450px;
	.el-cascader-menu__wrap.el-scrollbar__wrap {
		height: auto !important;
	}
}
</style>
