import request from "/@/utils/request"


// 提交ocr识别任务
export function submitOcr(obj?: Object) {
  return request({
    url: '/business/yjResponsePlan/submitTask',
    method: 'post',
    data: obj,
    headers: {
      'Content-Type': 'multipart/form-data' 
    }  
  })
}

// 获取ocr识别状态
export function getOcrstatus(query?: string) {
  return request({
    url: '/business/yjResponsePlan/getResult',
    method: 'get',
    params: query
  })
}
// 获取ocr识别状态
export function getOcrResult(query?: string) {
  return request({
    url: '/business/yjResponsePlan/getResultFile',
    method: 'get',
    params: query
  })
}