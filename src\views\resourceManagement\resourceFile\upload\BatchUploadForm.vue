<template>
	<el-dialog
		v-model="visible"
		title="批量导入"
		width="1000px"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:before-close="onCancel"
		class="batch-upload-form-dlg"
		destroy-on-close
	>
		<!-- 步骤条 -->
		<div class="custom-steps">
			<div 
				v-for="(step, index) in steps" 
				:key="index" 
				class="custom-step"
				:class="{ 
					'is-active': activeStep === index,
					'is-completed': activeStep >= index 
				}"
			>
				<div class="custom-step-line left" v-if="index > 0"></div>
				<div class="custom-step-dot"></div>
				<div class="custom-step-line right" v-if="index < steps.length - 1"></div>
				<div class="custom-step-title">{{ step }}</div>
			</div>
		</div>

		<!-- 第一步：上传文件 -->
		<div v-if="activeStep === 0" class="step-content">
			<div class="step-container">
				<!-- 左侧面板 -->
				<div class="left-panel">
					<!-- 基础信息 -->
					<div class="section-title">
						<div class="section-title-bar"></div>
						<span>基础信息</span>
					</div>
					
					<el-form ref="dataFormRef" :model="form" :rules="dataRules" label-width="80px" class="form-content">
						<el-form-item label="文件类型" prop="type" required>
							<el-select 
								v-model="form.type" 
								placeholder="请选择文件类型" 
								style="width: 100%" 
								@change="handleFormTypeChange"
								:disabled="fileList.length > 0"
							>
								<el-option
									v-for="item in fileTypeConfig"
									:key="Number(item.id)"
									:label="item.name"
									:value="Number(item.id)"
								/>
							</el-select>
						</el-form-item>
						
						<el-form-item label="文件密级" prop="securityId" required>
							<el-select v-model="form.securityId" placeholder="请选择" style="width: 100%">
								<el-option
									v-for="item in securityLevelOptions"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
						</el-form-item>
						
						<el-form-item label="标签" prop="labels">
							<el-input
								v-model="form.labels"
								placeholder="输入内容，多个标签用“，”隔开"
								show-word-limit
								maxlength="50"
							/>
						</el-form-item>
					
					<!-- 管理信息 -->
					<div class="section-title">
						<div class="section-title-bar"></div>
						<span>管理信息</span>
					</div>
					
					
						<el-form-item label="来源单位" prop="deptId" required>
							<el-tree-select
								v-model="form.deptId"
								:data="props.deptData"
								:props="{ value: 'id', label: 'name', children: 'children' }"
								check-strictly
								class="w100"
								clearable
								placeholder="请选择来源单位"
							>
							</el-tree-select>
						</el-form-item>
					
					</el-form>
					<!-- 上传说明 -->
					<div class="upload-tips">
						<div class="tips-title">批量上传说明</div>
						<div class="tips-item">1. 批量上传文件将以文件名命名，且被分别识别为独立资源</div>
						<div class="tips-item">2. 本步骤为全局配置，文件差异性配置可在下一步编辑更新</div>
					</div>
				</div>
				
				<div class="right-panel">
					<!-- 上传区域 -->
					<div class="upload-area">
						<div class="upload-section-title">
							<span>上传文件</span>
						</div>
						
						<div class="upload-content">
							<div class="upload-requirements-box">
								<div class="upload-requirements-title">上传要求: {{ getFileTypeText }}</div>
								<div class="upload-requirements-items">
									<div class="requirement-item">1. {{ getFileTypeExtensions }}</div>
									<div class="requirement-item">2. 文件大小不超过1GB</div>
								</div>
							</div>
							
							<div class="upload-buttons">
								<el-upload
									ref="uploadRef"
									:auto-upload="false"
									:multiple="true"
									:limit="10"
									:on-exceed="handleExceed"
									:on-change="handleFileChange"
									:before-upload="beforeUpload"
									:http-request="uploadFile"
									:show-file-list="false"
									:accept="acceptType"
								>
									<el-button class="upload-button">
										<el-icon><Document /></el-icon>选择文件
									</el-button>
								</el-upload>
								<el-button type="primary"
									@click="startUpload" 
									:disabled="fileList.length === 0 || allFilesUploaded"
									class="upload-button"
								>
									开始上传
								</el-button>
							</div>
						</div>
						
						<div class="file-list-container">
							<div v-for="(file, index) in fileList" :key="index" class="file-item">
								<div class="file-info-row">
									<div class="file-name-container">
										<el-icon class="file-icon"><Document /></el-icon>
										<span class="file-name">{{ file.originalName }}</span>
									</div>
									<div class="file-meta">
										<span class="file-size">{{ formatFileSize(file.size) }}</span>
										<span class="file-status" :class="{'status-success': file.status === 'success'}">
											{{ getFileStatusText(file) }}
										</span>
									</div>
								</div>
								
								<div class="file-progress-row">
									<div class="progress-container">
										<el-progress 
											:percentage="file.progress || 0" 
											:status="file.status === 'fail' ? 'exception' : ''"
											:stroke-width="8"
											:color="'var(--el-color-success)'"
										/>
									</div>
									<div class="file-actions">
										<span class="progress-text" v-if="['uploading', 'md5', 'serverHanding'].includes(file.status)">
											{{ Math.floor(file.progress) }}%
										</span>
										<el-icon 
											class="delete-icon" 
											@click="removeFile(index)"
											v-if="!['uploading', 'md5', 'serverHanding'].includes(file.status)"
										>
											<Delete />
										</el-icon>
									</div>
								</div>
							</div>
							
							<div v-if="fileList.length === 0" class="empty-file-list">
								<el-icon><Upload /></el-icon>
								<span>请选择文件上传</span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- 第二步：批量编辑 -->
		<div v-if="activeStep === 1" class="step-content">
			<el-table :data="fileList" style="width: 100%" border height="450px">
				<el-table-column label="文件名称" min-width="180">
					<template #header>
						<span class="required-field">*</span> 文件名称
					</template>
					<template #default="scope">
						<el-input 
							v-model="scope.row.name" 
							placeholder="请输入文件名称" 
							show-word-limit
							maxlength="16"
						/>
					</template>
				</el-table-column>
				<el-table-column label="封面" width="150">
					<template #default="scope">
						<ImageUpload
							v-model:imageUrl="scope.row.cover"
							height="80px"
							width="120px"
							borderRadius="0"
							uploadFileUrl="/datacenter/learning/material/cover"
							@uploadSuccess="handleCoverUploadSuccess($event, scope.row)"
						>
							<template #empty>
								<el-icon><Picture /></el-icon>
								<span>点击上传封面</span>
							</template>
						</ImageUpload>
					</template>
				</el-table-column>
				<el-table-column label="文件类型" width="120">
					<template #default="scope">
						<el-select v-model="scope.row.type" disabled>
							<el-option
								v-for="item in fileTypeConfig"
								:key="Number(item.id)"
								:label="item.name"
								:value="Number(item.id)"
							/>
						</el-select>
					</template>
				</el-table-column>
				<el-table-column label="文件密级" width="120">
					<template #header>
						<span class="required-field">*</span> 文件密级
					</template>
					<template #default="scope">
						<el-select 
							v-model="scope.row.securityId"
						>
							<el-option
								v-for="item in securityLevelOptions"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</template>
				</el-table-column>
				<el-table-column label="文件标签" min-width="150">
					<template #default="scope">
						<el-input v-model="scope.row.labels" placeholder="输入标签" />
					</template>
				</el-table-column>
				<el-table-column label="来源单位" min-width="150">
					<template #header>
						<span class="required-field">*</span> 来源单位
					</template>
					<template #default="scope">
						<el-tree-select
							:data="props.deptData"
							:props="{ value: 'id', label: 'name', children: 'children' }"
							check-strictly
							class="w100"
							clearable
							placeholder="请选择来源单位"
							v-model="scope.row.deptId"
						>
						</el-tree-select>
					</template>
				</el-table-column>
				<el-table-column label="状态" width="180" v-if="fileList.some(file => file.importError)">
					<template #default="scope">
						<div v-if="scope.row.importError" class="error-message">
							<el-icon><Warning /></el-icon>
							<span>{{ scope.row.importError }}</span>
						</div>
					</template>
				</el-table-column>
			</el-table>
		</div>

		<!-- 第三步：导入数据 -->
		<div v-if="activeStep === 2" class="step-content import-progress">
			<div class="progress-container">
				<div class="progress-value">{{ importProgress }}%</div>
				<el-progress :percentage="importProgress" :stroke-width="10" color="var(--el-color-primary)" />
				<div class="progress-text">{{ importing ? '正在导入数据' : '导入已暂停' }}</div>
			</div>
			
			<div class="import-tips">
				<div class="tips-title">温馨提示：</div>
				<div class="tips-item">1. 文件导入过程中请勿关闭或刷新页面以免导出系统</div>
				<div class="tips-item">2. 可关闭对话框（等待后台导入）</div>
				<div class="tips-item" v-if="!importing">3. 部分文件导入失败，点击"上一步"可返回修改</div>
			</div>
		</div>

		<!-- 第四步：导入完成 -->
		<div v-if="activeStep === 3" class="step-content import-success">
			<div class="success-container">
				<div class="success-icon">
					<el-icon class="el-icon-success"><CircleCheck /></el-icon>
				</div>
				<div class="success-title">数据导入成功</div>
				<div class="success-desc">您已成功上传{{ importedFiles.length }}个文件</div>
			</div>
		</div>

		<!-- 底部按钮 -->
		<template #footer>
			<div class="dialog-footer">
				<el-button v-if="activeStep < 3" @click="onCancel">取 消</el-button>
				<el-button v-if="[1, 2].includes(activeStep)" @click="prevStep" :loading="importing">上一步</el-button>
				<el-button 
					type="primary" 
					@click="nextStep" 
					:loading="importing"
          v-if="activeStep !== 2"
				>
					{{ activeStep === 3 ? '完成' : '下一步' }}
				</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Session } from "/@/utils/storage";
import axios from 'axios';
import BMF from "browser-md5-file";
import { checkFileUnique, addObj } from "/@/api/resource/filelist/file";
import { fileTypeConfig, securityLevelOptions } from '/@/config/resourceConfig';
import { CircleCheck, Document, Delete, Picture, Upload, Warning } from '@element-plus/icons-vue';
import { resourceFileUploaderStore } from "/@/stores/resourceFileUploader";
import ImageUpload from "/@/components/Upload/Image.vue";

const rfuStore = resourceFileUploaderStore();
const props = defineProps({
	deptData: {
		type: Array as any,
		default: () => [],
	},
	currentParentList: {
		type: Array as any,
		default: () => [],
	},
});

const emit = defineEmits(['refresh', 'close']);
const visible = ref(true);
const activeStep = ref(0);
const dataFormRef = ref();
const uploadRef = ref();
const fileList = ref<any[]>([]);
const importProgress = ref(0);
const importing = ref(false);
const allFilesUploaded = ref(false);
const uploading = ref(false);
const isSubmit = ref(false);
const serverTimer_1 = ref<any>(null);
const serverTimer_2 = ref<any>(null);
const importedFiles = ref<any[]>([]);

// 步骤数据
const steps = ['上传文件', '数据编辑', '导入数据', '导入完成'];

// 文件接受类型
const acceptType = ref('');

// 表单数据
const form = reactive({
	type: 6, // 默认为文档类型，与UploadFileForm保持一致
	securityId: '1', // 默认密级
	labels: '',
	deptId: '',
});

// 表单验证规则
const dataRules = ref({
	type: [{ required: true, message: '请选择文件类型', trigger: 'change' }],
	securityId: [{ required: true, message: '请选择文件密级', trigger: 'change' }],
	deptId: [{ required: true, message: '请选择来源单位', trigger: 'change' }],
});

// 获取文件类型文本
const getFileTypeText = computed(() => {
	const selectedType = fileTypeConfig.find((item: any) => item.id === String(form.type));
	return selectedType ? selectedType.name : '';
});

// 获取文件类型支持的扩展名
const getFileTypeExtensions = computed(() => {
	const selectedType = fileTypeConfig.find((item: any) => item.id === String(form.type));
	if (selectedType && selectedType.fileType && selectedType.fileType.length > 0) {
		return '支持' + selectedType.fileType.join('、') + '格式';
	}
	return '请按文件类型上传文件';
});

// 监听文件类型变化，更新可接受的文件类型
const handleFormTypeChange = (val: number) => {
	// 更新可接受的文件类型
	const fileTypeObj = fileTypeConfig.find((obj: any) => obj.id == String(val));
	if (fileTypeObj && fileTypeObj.fileType) {
		const extensions = fileTypeObj.fileType;
		acceptType.value = extensions.map((ext: string) => `.${ext}`).join(',');
	} else {
		acceptType.value = '';
	}
};

// 初始化时设置默认接受类型
onMounted(() => {
	if (form.type) {
		handleFormTypeChange(form.type);
	}
});

// 获取文件状态文本
const getFileStatusText = (file: any) => {
	if (file.status === 'ready') return '等待上传';
	if (file.status === 'uploading') {
		if (file.stepCode === 'md5') return '计算MD5';
		if (file.stepCode === 'uploading') return '上传中';
		if (file.stepCode === 'serverHanding') return '处理中';
		return '上传中';
	}
	if (file.status === 'success') return '上传成功';
	if (file.status === 'fail') return '上传失败';
	return file.status;
};

// 关闭弹窗
const onCancel = async () => {
	// 取消上传中的文件
	fileList.value.forEach(file => {
		if (["md5", "uploading", "serverHanding"].includes(file.status) && !isSubmit.value) {
			file.cancelFile?.();
		}
	});
  emit('close');
};

// 下一步
const nextStep = async () => {
	if (activeStep.value === 0) {
		try {
			// 使用更明确的验证方式
			const valid = await dataFormRef.value.validate();
			if (!valid) {
				return;
			}
		} catch (error) {
			return;
		}
		
		// 检查是否有文件上传
		if (fileList.value.length === 0) {
			ElMessage.warning('请选择至少一个文件');
			return;
		}
		
		// 检查文件是否全部上传完成
		if (!allFilesUploaded.value) {
			ElMessage.warning('请等待文件上传完成');
			return;
		}
		
		// 为每个文件设置基本信息
		fileList.value.forEach(file => {
			file.type = form.type;
			file.securityId = form.securityId;
			file.labels = form.labels;
			file.deptId = form.deptId;
			file.name = file.name || file.fileName;
		});
		
		activeStep.value++;
	} else if (activeStep.value === 1) {
		// 验证第二步的必填项
		let hasError = false;
		
		// 检查每个文件是否都有名称
		fileList.value.forEach((file: any) => {
			if (!file.name || file.name.trim() === '') {
				hasError = true;
			}
		});
		
		// 检查每个文件是否都选择了密级
		fileList.value.forEach((file: any) => {
			if (!file.securityId) {
				hasError = true;
			}
		});
		
		// 检查每个文件是否都选择了来源单位
		fileList.value.forEach((file: any) => {
			if (!file.deptId) {
				hasError = true;
			}
		});
		
		if (hasError) {
			return ElMessage.warning(`请完成必填项`);
		}

    if (fileList.value.some((file) => file.name.length > 16)) {
      return ElMessage.warning(`文件名长度不能超过16个字符`);
    }
		
		// 进入导入数据步骤
		activeStep.value++;
		startImport();
	} else if (activeStep.value === 2) {
		// 导入完成
		activeStep.value++;
	} else if (activeStep.value === 3) {
    emit('refresh')
    emit('close')
	}
};

// 上一步
const prevStep = () => {
	if (activeStep.value > 0) {
		// 如果从第三步返回第二步，需要过滤已导入成功的文件
		if (activeStep.value === 2) {
			// 过滤文件列表，只保留未成功导入的文件
			fileList.value = fileList.value.filter(f => !importedFiles.value.includes(f.uid));
		}
		
		activeStep.value--;
	}
};

// 文件大小格式化
const formatFileSize = (size: number) => {
	if (size < 1024) {
		return size + ' B';
	} else if (size < 1024 * 1024) {
		return (size / 1024).toFixed(1) + ' KB';
	} else {
		return (size / (1024 * 1024)).toFixed(1) + ' MB';
	}
};

// 文件超出限制
const handleExceed = () => {
	ElMessage.warning('最多只能上传10个文件');
};

// 文件变更 - 只进行基本检查，不重复验证
const handleFileChange = (uploadFile: any) => {
	if (uploadFile.status === 'ready') {
		// 添加到文件列表
		const fileObj = {
			uid: uploadFile.uid,
			name: uploadFile.name.split('.')[0], // 默认名称为文件名（不含后缀）
			originalName: uploadFile.name, // 保存原始文件名（含后缀）
			size: uploadFile.size,
			type: form.type,
			securityId: form.securityId,
			labels: form.labels,
			deptId: form.deptId,
			status: 'ready',
			progress: 0,
			uploaded: false,
			file: uploadFile.raw,
			stepCode: 'ready',
			md5Percent: 0,
			uploadingPercent: 0,
			serverPercent: 0,
			cover: '' // 初始化封面为空
		};
		
		fileList.value.push(fileObj);
    checkAllUploaded();
	}
};

// 移除文件
const removeFile = (index: number) => {
	fileList.value[index].cancelFile?.(true);
	fileList.value.splice(index, 1);
	checkAllUploaded();
};

// 上传前检查 - 只检查文件类型和大小，不进行MD5计算和唯一性检查
const beforeUpload = (file: File) => {
	// 检查文件大小
	const isLt100M = file.size / 1024 / 1024 < 1024;
	if (!isLt100M) {
		ElMessage.error('文件大小不能超过1GB!');
		return false;
	}
	
	// 检查文件类型
	const fileExtension = getFileExtension(file.name);
	const fileTypeObj = fileTypeConfig.find((obj: any) => obj.id == Number(form.type));
	
	if (fileTypeObj && fileTypeObj.fileType) {
		const validExtensions = fileTypeObj.fileType;
		if (!validExtensions.includes(fileExtension)) {
			ElMessage.warning(`文件类型不匹配，该文件不符合${fileTypeObj.name}类型的要求`);
			return false;
		}
	}
	
	return true;
};

// 获取文件扩展名
const getFileExtension = (filename: string) => {
	return filename.slice((filename.lastIndexOf(".") - 1 >>> 0) + 2).toLowerCase();
};

// 开始上传
const startUpload = () => {
	if (fileList.value.length === 0) {
		ElMessage.warning('请先选择文件');
		return;
	}
	
	uploading.value = true;
	
	// 创建一个Map来存储已经计算过MD5的文件
	const md5Map = new Map();
	
	// 开始上传所有文件
	const uploadPromises = fileList.value
		.filter(file => file.status !== 'success')
		.map(file => {
			return new Promise((resolve) => {
				// 如果文件已经有MD5值，并且已经有相同MD5的文件上传成功，可以直接复用结果
				if (file.fileIdentifier && md5Map.has(file.fileIdentifier)) {
					const existingResult = md5Map.get(file.fileIdentifier);
					file.uploadingPercent = 100;
					file.stepCode = 'uploading';
					file.serverPercent = 100;
					file.progress = 100;
					file.status = 'success';
					file.uploaded = true;
					file.result = existingResult;
					
					// 如果是图片类型，自动设置封面
					if ([6].includes(file.type) && existingResult.url) {
						file.cover = existingResult.url;
					}
					
					rfuStore.updateFileOBJ(file);
					resolve(null);
					return;
				}
				
				// 否则正常上传
				uploadRef.value.submit();
				resolve(null);
			});
		});
	
	Promise.all(uploadPromises).then(() => {
		// 所有文件上传完成后的处理
    uploading.value = false;
		checkAllUploaded();
	});
};

// 上传文件 - 在这里进行MD5计算和唯一性检查
const uploadFile = async (options: any) => {
  const file = options.file;
  const fileIndex = fileList.value.findIndex(f => f.uid === file.uid);
  if (fileIndex === -1) return;
  
  // 如果文件已经上传成功或正在上传中，则跳过
  if (fileList.value[fileIndex].status === 'success' || 
      fileList.value[fileIndex].status === 'uploading' ||
      fileList.value[fileIndex].uploaded) {
    options.onSuccess();
    return;
  }
  
  // 更新文件状态
  fileList.value[fileIndex].status = 'uploading';
  fileList.value[fileIndex].progress = 0;
  
  try {
    // 第一步：进行MD5加密
    const bmf = new BMF();
    fileList.value[fileIndex].stepCode = 'md5';
    
    // 添加取消功能
    fileList.value[fileIndex].cancelFile = (isHideTip?: any) => {
      !isHideTip && ElMessage.warning("已取消上传");
      bmf && bmf.abort();
      clearInterval(serverTimer_1.value);
      clearInterval(serverTimer_2.value);
    };
    
    const md5: any = await new Promise((resolve, reject) => {
      bmf.md5(
        file,
        (err: any, md5: string) => {
          if (err && err !== "aborted") {
            reject(err);
            if (fileList.value[fileIndex]) {
              fileList.value[fileIndex].error = err;
              rfuStore.updateFileOBJ(fileList.value[fileIndex]);
            }
          }
          resolve(md5);
        },
        (progress: number) => {
          if (fileList.value[fileIndex]) {
            fileList.value[fileIndex].md5Percent = progress * 100;
            fileList.value[fileIndex].progress = Math.floor(progress * 30);
            rfuStore.updateFileOBJ(fileList.value[fileIndex]);
          }
        }
      );
    });
    
    // 存储MD5值，避免重复计算
    fileList.value[fileIndex].fileIdentifier = md5;
    
    await sleep(300);
    
    // 校验文件是否存在，存在则无需重新上传
    // 检查是否已经有相同MD5的文件在上传或已上传
    const existingFile = fileList.value.find(f => 
      f.uid !== fileList.value[fileIndex].uid && 
      f.fileIdentifier === md5 && 
      f.status === 'success'
    );
    
    if (existingFile && existingFile.result) {
      // 如果已有相同文件上传成功，直接使用其结果
      fileList.value[fileIndex].uploadingPercent = 100;
      fileList.value[fileIndex].stepCode = 'uploading';
      fileList.value[fileIndex].serverPercent = 100;
      fileList.value[fileIndex].progress = 100;
      fileList.value[fileIndex].status = 'success';
      fileList.value[fileIndex].uploaded = true;
      fileList.value[fileIndex].result = existingFile.result;
      
      // 如果是图片类型，自动设置封面
      if ([6].includes(fileList.value[fileIndex].type) && existingFile.result.url) {
        fileList.value[fileIndex].cover = existingFile.result.url;
      }
      
      rfuStore.updateFileOBJ(fileList.value[fileIndex]);
      options.onSuccess({ data: existingFile.result });
      checkAllUploaded();
      return;
    }
    
    try {
      // 调用API检查文件是否已存在于服务器
      const res_check = await checkFileUnique({ fileIdentifier: md5, totalSize: file.size });
      if (res_check.data) {
        fileList.value[fileIndex].uploadingPercent = 100;
        fileList.value[fileIndex].stepCode = 'uploading';
        fileList.value[fileIndex].serverPercent = 100;
        fileList.value[fileIndex].progress = 100;
        fileList.value[fileIndex].status = 'success';
        fileList.value[fileIndex].uploaded = true;
        fileList.value[fileIndex].result = res_check.data;
        
        // 如果是图片类型，自动设置封面
        if ([6].includes(fileList.value[fileIndex].type) && res_check.data.url) {
          fileList.value[fileIndex].cover = res_check.data.url;
        }
        
        rfuStore.updateFileOBJ(fileList.value[fileIndex]);
        options.onSuccess(res_check);
        checkAllUploaded();
        return;
      }
    } catch (error) {
      // 检查失败时继续上传流程，不中断
    }
    
    // 第二步：文件上传后端
    fileList.value[fileIndex].fileIdentifier = md5;
    fileList.value[fileIndex].stepCode = 'uploading';
    fileList.value[fileIndex].uploadingPercent = 0;
    fileList.value[fileIndex].progress = 40;
    rfuStore.updateFileOBJ(fileList.value[fileIndex]);
    
    let formdata = new FormData();
    formdata.append("folderId", props.currentParentList[props.currentParentList.length - 1]?.id || "1");
    formdata.append("fileType", form.type.toString());
    formdata.append("fileFlag", "true");
    formdata.append("fileIdentifier", md5);
    formdata.append("totalSize", file.size.toString());
    
    // 确保文件名存在后再获取扩展名
    const fileExtension = fileList.value[fileIndex].originalName ? 
      getFileExtension(fileList.value[fileIndex].originalName) : 
      getFileExtension(file.name);
    
    formdata.append("format", fileExtension);
    formdata.append("file", file);
    
    await sleep(300);
    
    const CancelToken = axios.CancelToken;
    axios({
      method: "post",
      url: "/datacenter/learning/material/upload",
      data: formdata,
      headers: {
        Authorization: `Bearer ${Session.getToken()}`,
        "TENANT-ID": Session.getTenant(),
      },
      baseURL: import.meta.env.VITE_API_URL,
      timeout: 0,
      onUploadProgress: async (progressEvent: any) => {
        if (!fileList.value[fileIndex]) return;
        fileList.value[fileIndex].uploadingPercent = Number(
          (progressEvent.loaded / progressEvent.total) * 100 || 0
        );
        fileList.value[fileIndex].progress = 40 + Math.floor((progressEvent.loaded / progressEvent.total) * 30);
        rfuStore.updateFileOBJ(fileList.value[fileIndex]);
        
        // 上传完成后，模拟服务器处理进度
        if (progressEvent.loaded === progressEvent.total) {
          fileList.value[fileIndex].stepCode = "serverHanding";
          fileList.value[fileIndex].serverPercent = 0;
          rfuStore.updateFileOBJ(fileList.value[fileIndex]);
          
          // 模拟服务器处理进度
          serverTimer_1.value = setInterval(() => {
            if (!fileList.value[fileIndex]) {
              clearInterval(serverTimer_1.value);
              return;
            }
            
            if (fileList.value[fileIndex].serverPercent < 90) {
              fileList.value[fileIndex].serverPercent += 10;
              fileList.value[fileIndex].progress = 70 + Math.floor(fileList.value[fileIndex].serverPercent / 10) * 3;
              rfuStore.updateFileOBJ(fileList.value[fileIndex]);
            } else {
              clearInterval(serverTimer_1.value);
              
              // 模拟最后10%的进度
              serverTimer_2.value = setInterval(() => {
                if (!fileList.value[fileIndex]) {
                  clearInterval(serverTimer_2.value);
                  return;
                }
                
                if (fileList.value[fileIndex].serverPercent < 99) {
                  fileList.value[fileIndex].serverPercent += 1;
                  fileList.value[fileIndex].progress = 97 + Math.floor(fileList.value[fileIndex].serverPercent / 100) * 3;
                  rfuStore.updateFileOBJ(fileList.value[fileIndex]);
                } else {
                  clearInterval(serverTimer_2.value);
                }
              }, 3000);
            }
          }, 1000);
        }
      },
      cancelToken: new CancelToken(function executor(cancel) {
        fileList.value[fileIndex].cancelFile = (isHideTip?: any) => {
          !isHideTip && ElMessage.warning("已取消上传");
          clearInterval(serverTimer_1.value);
          clearInterval(serverTimer_2.value);
          bmf && bmf.abort();
          cancel && cancel();
        };
      }),
    })
      .then(async (res: any) => {
        clearInterval(serverTimer_1.value);
        clearInterval(serverTimer_2.value);
        
        fileList.value[fileIndex].progress = 100;
        fileList.value[fileIndex].status = 'success';
        fileList.value[fileIndex].uploaded = true;
        fileList.value[fileIndex].stepCode = "success";
        fileList.value[fileIndex].serverPercent = 100;
        fileList.value[fileIndex].result = res.data.data;
        
        // 如果是图片类型，自动设置封面
        if ([6].includes(fileList.value[fileIndex].type) && res.data.data.url) {
          fileList.value[fileIndex].cover = res.data.data.url;
        }
        
        rfuStore.updateFileOBJ(fileList.value[fileIndex]);
        
        options.onSuccess(res.data);
        checkAllUploaded();
      })
      .catch((err: any) => {
        fileList.value[fileIndex].cancelFile(true);
        clearInterval(serverTimer_1.value);
        clearInterval(serverTimer_2.value);
        
        if (err.message !== "canceled") {
          fileList.value[fileIndex].status = 'fail';
          fileList.value[fileIndex].stepCode = "fail";
          fileList.value[fileIndex].error = "上传文件失败";
          rfuStore.updateFileOBJ(fileList.value[fileIndex]);
          
          options.onError(err);
          ElMessage.error('文件上传失败，请重试');
        }
      });
  } catch (error) {
    clearInterval(serverTimer_1.value);
    clearInterval(serverTimer_2.value);
    
    fileList.value[fileIndex].status = 'fail';
    fileList.value[fileIndex].stepCode = "fail";
    fileList.value[fileIndex].error = "上传文件失败";
    rfuStore.updateFileOBJ(fileList.value[fileIndex]);
    
    options.onError(error);
    ElMessage.error('文件上传失败，请重试');
  }
};

// 延时函数
const sleep = (ms: number) => {
	return new Promise(resolve => setTimeout(resolve, ms));
};

// 检查所有文件是否上传完成
const checkAllUploaded = () => {
	if (fileList.value.length === 0) {
		allFilesUploaded.value = false;
		return;
	}
	
	const allUploaded = fileList.value.every(file => file.status === 'success');
	allFilesUploaded.value = allUploaded;
};

// 处理封面上传成功
const handleCoverUploadSuccess = (response: any, file: any) => {
  // 确保封面URL正确保存到文件对象中
  if (response && response.url) {
    file.cover = response.url;
  } else if (response && response.data) {
    file.cover = response.data;
  }
};

// 开始导入
const startImport = async () => {
	importing.value = true;
	importProgress.value = 0;
  
  // 模拟进度
  const progressInterval = setInterval(() => {
    if (importProgress.value < 90) {
      importProgress.value += 5;
    }
  }, 300);
	
	try {
		// 逐个保存文件
		for (let i = 0; i < fileList.value.length; i++) {
			const file = fileList.value[i];
			
			// 准备保存的数据，参照UploadFileForm中的doSave方法
			const saveData = {
				name: file.name, // 用户输入的名称（不含后缀）
				type: file.type,
				securityId: file.securityId,
				labels: file.labels,
				deptId: file.deptId,
				folderId: props.currentParentList.length >= 1 ? props.currentParentList[props.currentParentList.length - 1].id : 0,
				fileType: fileTypeConfig.find((obj: any) => obj.id == file.type)?.name || '',
				fileRequestList: [{ 
					fileId: file.result.fileId || file.result.id, 
					fileName: file.originalName // 使用原始文件名（含后缀）
				}],
				objectKey: file.result.objectKey,
				totalSize: file.size,
				format: getFileExtension(file.originalName),
				fileIdentifier: file.fileIdentifier,
				fileName: file.originalName, // 使用原始文件名（含后缀）
				cover: file.cover || '' // 封面
			};
			
			// 调用保存接口
			await addObj(saveData);
			importedFiles.value.push(file.uid);
			// 更新进度
			importProgress.value = Math.min(90, Math.floor((i + 1) / fileList.value.length * 90));
		}
		
		progressInterval && clearInterval(progressInterval);
		importProgress.value = 100;
		await new Promise(resolve => setTimeout(resolve, 500));
		importing.value = false;
		activeStep.value++;
		
	} catch (error: any) {
		ElMessage.error(error?.msg || '导入失败，请重试');
		importing.value = false;
		clearInterval(progressInterval);
	}
};
</script>

<style lang="scss" scoped>
.batch-upload-form-dlg {
  :deep(.el-dialog__header) {
    padding: 20px;
    margin-right: 0;
    border-bottom: 1px solid #EBEEF5;
    
    .el-dialog__title {
      font-size: 18px;
      font-weight: 500;
    }
  }
  
  :deep(.el-dialog__body) {
    padding: 20px;
  }
  
  // 自定义步骤条样式
  .custom-steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: 40px;
    width: 750px;
    margin: 0 auto 40px auto;
    
    .custom-step {
      position: relative;
      width: 250px;
      display: flex;
      align-items: center;
      
      .custom-step-line {
        height: 2px;
        background-color: #E4E7ED;
        flex: 1;
      }
      
      .custom-step-dot {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: #C0C4CC;
        z-index: 1;
        flex-shrink: 0;
      }
      
      .custom-step-title {
        position: absolute;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 14px;
        color: #909399;
        white-space: nowrap;
      }
      
      &:first-child {
        width: 125px;
        .custom-step-line.left {
          flex: 0;
          width: 0;
        }
        .custom-step-title {
          left: 0;
        }
      }
      
      &:last-child {
        width: 125px;
        .custom-step-line.right {
          flex: 0;
          width: 0;
        }
        .custom-step-title {
          left: 95%;
        }
      }
      
      &.is-active {
        .custom-step-dot {
          background-color: var(--el-color-primary);
        }
        
        .custom-step-title {
          color: #000;
        }
      }
      
      &.is-completed {
        .custom-step-dot {
          background-color: var(--el-color-primary);
        }
        
        .custom-step-line {
          background-color: var(--el-color-primary);
        }
      }
    }
  }
  
  .step-content {
    min-height: 500px;
  }
  
  .step-container {
    display: flex;
    gap: 20px;
    height: 500px;
    
    .left-panel {
      width: 50%;
      padding-right: 20px;
      border-right: 1px solid #EBEEF5;
      
      .section-title {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        font-size: 16px;
        font-weight: 500;
        color: #303133;
        
        .section-title-bar {
          width: 3px;
          height: 16px;
          background-color: var(--el-color-primary);
          margin-right: 8px;
          border-radius: 1px;
        }
      }
      
      .form-content {
        margin-bottom: 20px;
        
        :deep(.el-form-item) {
          margin-bottom: 18px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .el-form-item__label {
            color: #606266;
          }
        }
      }
      
      .upload-tips {
        margin-top: 125px;
        background-color: var(--el-color-primary-light-9);
        padding: 15px;
        border-radius: 4px;
        
        .tips-title {
          font-weight: 500;
          color: var(--el-color-primary);
          margin-bottom: 10px;
        }
        
        .tips-item {
          color: #606266;
          font-size: 13px;
          line-height: 1.8;
        }
      }
    }
    
    .right-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      
      .upload-area {
        flex: 1;
        display: flex;
        flex-direction: column;
        
        .upload-section-title {
          font-size: 16px;
          font-weight: 500;
          color: #303133;
          margin-bottom: 15px;
        }
        
        .upload-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
          
          .upload-requirements-box {
            border-radius: 4px;
            padding: 15px;
            background-color: var(--el-color-primary-light-9);
            flex: 1;
            margin-right: 10px;
            
            .upload-requirements-title {
              color: var(--el-color-primary);
              font-size: 14px;
              margin-bottom: 8px;
            }
            
            .upload-requirements-items {
              color: #606266;
              font-size: 13px;
              
              .requirement-item {
                line-height: 1.6;
              }
            }
          }
          
          .upload-buttons {
            display: flex;
            flex-direction: column;
            height: 100%;
            justify-content: space-between;
            
            .upload-button {
              display: flex;
              align-items: center;
              justify-content: center;
              height: 45px;
              
              .el-icon {
                margin-right: 5px;
              }
            }
          }
        }
        
        .file-list-container {
          flex: 1;
          overflow-y: auto;
          border: 1px solid #EBEEF5;
          border-radius: 4px;
          
          .file-item {
            padding: 12px;
            border-bottom: 1px solid #EBEEF5;
            
            &:last-child {
              border-bottom: none;
            }
            
            .file-info-row {
              display: flex;
              justify-content: space-between;
              margin-bottom: 8px;
              
              .file-name-container {
                display: flex;
                align-items: center;
                flex: 1;
                overflow: hidden;
                
                .file-icon {
                  color: #909399;
                  margin-right: 8px;
                  flex-shrink: 0;
                }
                
                .file-name {
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }
              }
              
              .file-meta {
                display: flex;
                align-items: center;
                margin-left: 15px;
                
                .file-size {
                  color: #909399;
                  margin-right: 15px;
                  font-size: 13px;
                }
                
                .file-status {
                  font-size: 13px;
                  
                  &.status-success {
                    color: #67C23A;
                  }
                }
              }
            }
            
            .file-progress-row {
              display: flex;
              align-items: center;
              
              .progress-container {
                flex: 1;
              }
              
              .file-actions {
                display: flex;
                align-items: center;
                margin-left: 10px;
                
                .progress-text {
                  font-size: 13px;
                  color: #606266;
                  margin-right: 5px;
                }
                
                .delete-icon {
                  color: #F56C6C;
                  cursor: pointer;
                  font-size: 16px;
                }
              }
            }
          }
          
          .empty-file-list {
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: #909399;
            
            .el-icon {
              font-size: 40px;
              margin-bottom: 10px;
            }
          }
        }
      }
    }
  }
  
  // 第三步：导入进度样式
  .import-progress {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 500px;
    
    .progress-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 80%;
      max-width: 600px;
      margin-bottom: 80px;
      
      .progress-value {
        font-size: 24px;
        font-weight: bold;
        color: #333;
        margin-bottom: 15px;
      }
      
      :deep(.el-progress) {
        width: 100%;
        margin-bottom: 15px;
        
        .el-progress-bar__outer {
          background-color: #E9E9E9;
          border-radius: 4px;
        }
        
        .el-progress-bar__inner {
          border-radius: 4px;
        }
      }
      
      .progress-text {
        font-size: 16px;
        color: #606266;
      }
    }
    
    .import-tips {
      width: 80%;
      max-width: 600px;
      background-color: var(--el-color-primary-light-9);
      border-radius: 4px;
      padding: 15px 20px;
      
      .tips-title {
        color: var(--el-color-primary);
        margin-bottom: 10px;
      }
      
      .tips-item {
        color: #606266;
        line-height: 1.8;
        font-size: 14px;
      }
    }
  }
  
  // 第四步：导入成功样式
  .import-success {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 500px;
    
    .success-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .success-icon {
        margin-bottom: 20px;
        
        .el-icon-success {
          font-size: 80px;
          color: #67C23A;
        }
      }
      
      .success-title {
        font-size: 24px;
        font-weight: bold;
        color: #333;
        margin-bottom: 15px;
      }
      
      .success-desc {
        font-size: 16px;
        color: #606266;
      }
    }
  }
  
  // 错误信息样式
  .error-message {
    display: flex;
    align-items: center;
    color: #F56C6C;
    
    .el-icon {
      margin-right: 5px;
    }
  }
  
  :deep(.el-dialog__footer) {
    padding: 15px 20px;
    border-top: 1px solid #EBEEF5;
    
    .dialog-footer {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
    }
  }
}

.w100 {
  width: 100%;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-20 {
  margin-top: 20px;
}

.cover-preview {
  width: 80px;
  height: 45px;
  display: flex;
}

// 添加必填项标识样式
.required-field {
  color: #F56C6C;
  margin-right: 4px;
}
</style>
