<template>
  <div class="pdf-viewer" v-loading="loading">
    <div class="pdf-box" ref="pdfBoxRef">
      <canvas id="pdfContainer" />
    </div>
    <div class="preview-pdf-btns">
      <div class="preview-scale-icon" @click="scaleDown">缩小</div>
      <div class="preview-scale-icon" @click="prev">上一页</div>
      <div className="preview-page">{{ currentPage }} / {{ pdfPages }}</div>
      <div class="preview-scale-icon" @click="next">下一页</div>
      <div class="preview-scale-icon" @click="scaleUp">放大</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { nextTick, ref, onMounted } from 'vue'
import * as PDFJS from 'pdfjs-dist/legacy/build/pdf.mjs'
import * as PdfWorker from 'pdfjs-dist/legacy/build/pdf.worker.mjs'

const props = defineProps({
  fileUrl: {
    type: String,
    default: ''
  }
})

let pdfObj: any = null
const currentPage = ref(1)
const pdfPages = ref(0)
const pdfScale = ref(1)
const loading = ref(false)
const pdfBoxRef = ref({} as any)

onMounted(() => {
  loadFile(props.fileUrl)
})

const loadFile = async (url: any) => {
  PDFJS.GlobalWorkerOptions.workerSrc = PdfWorker
  let CMAP_URL = 'https://cdn.jsdelivr.net/npm/pdfjs-dist@2.0.943/cmaps/'
  const loadingTask = PDFJS.getDocument({
    url: url,
    cMapUrl: CMAP_URL,
    cMapPacked: true,
  })
  loadingTask.promise.then(async (pdf: any) => {
    pdfObj = pdf // 保存加载的pdf文件流
    pdfPages.value = pdfObj.numPages // 获取pdf文件的总页数
    await nextTick(() => {
      renderPage(1) // 将pdf文件内容渲染到canvas
    })
  })
}

const renderPage = (num: any) => {
  pdfObj.getPage(num).then((page: any) => {
    page.cleanup()
    const container: any = document.querySelector(`.pdf-viewer`)
    const width = container.clientWidth - 5
    let canvas = container.querySelector('#pdfContainer')
    const ctx = canvas.getContext('2d')
    if (canvas) {
      let viewportOrigin = page.getViewport({ scale: pdfScale.value })
      let scalNum = (width / viewportOrigin.width).toFixed(2)
      const viewport = page.getViewport({ scale: scalNum })
      canvas.width = viewport.width
      canvas.height = viewport.height
      const renderContext = {
        canvasContext: ctx,
        viewport: viewport
      }
      currentPage.value = num
      page.render(renderContext)
    }
  })
}

// 缩放
const scaleDown = () => {
  pdfScale.value = pdfScale.value * 2
  renderPage(currentPage.value)
}
const scaleUp = () => {
  pdfScale.value = pdfScale.value / 2
  renderPage(currentPage.value)
}
// 翻页
const prev = () => {
  if (!loading.value && currentPage.value && currentPage.value > 1) {
    renderPage(currentPage.value - 1)
    pdfBoxRef.value.scrollTo({ top: 0, behavior: 'smooth' })
  }
}
const next = () => {
  if (!loading.value && pdfPages.value && currentPage.value && currentPage.value < pdfPages.value) {
    renderPage(currentPage.value + 1)
    pdfBoxRef.value.scrollTo({ top: 0, behavior: 'smooth' })
  }
}
</script>

<style scoped lang="scss">
.pdf-viewer {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .pdf-box {
    width: 100%;
    height: calc(100%);
    text-align: center;
    overflow: auto;

    #pdfContainer {
      margin: 0 auto;
    }
  }

  .preview-pdf-btns {
    display: flex;
    align-items: center;
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.6);
    color: #fff;
    border-radius: 20px;
    padding: 7px 25px;

    .preview-page {
      margin: 0 25px;
    }

    .preview-scale-icon {
      cursor: pointer;

      &+.preview-scale-icon {
        margin-left: 25px;
      }
    }
  }
}
</style>