<template>
  <div class="resource-detail-container">
    <!-- 左侧表单tab导航 -->
    <div class="form-tabs-sidebar">
      <div class="tabs-list">
        <!-- 基础信息tab -->
        <div class="tab-item" :class="{ 'active': activeFormTab === 'basic' }" @click="activeFormTab = 'basic'">
          <div class="tab-icon">
            <el-icon>
              <Document />
            </el-icon>
          </div>
          <div class="tab-info">
            <div class="tab-name">资源信息</div>
          </div>
        </div>

        <!-- 文件资源tab -->
        <div class="tab-item" :class="{ 'active': activeFormTab === 'files' }" @click="activeFormTab = 'files'">
          <div class="tab-icon">
            <el-icon>
              <FolderOpened />
            </el-icon>
          </div>
          <div class="tab-info">
            <div class="tab-name">文件资源</div>
          </div>
        </div>

        <!-- 动态表单tab -->
        <!-- <div v-if="dynamicFormGroups.length > 0" class="tab-item" :class="{ 'active': activeFormTab === 'dynamic' }"
          @click="activeFormTab = 'dynamic'">
          <div class="tab-icon">
            <el-icon>
              <Grid />
            </el-icon>
          </div>
          <div class="tab-info">
            <div class="tab-name">业务属性</div>
          </div>
        </div> -->
        
        <!-- 动态表单 -->
        <div class="tab-item" v-for="group in dynamicFormGroups" :key="group.id"
          :class="{ 'active': activeDynamicSection === group.id && activeFormTab === 'dynamic' }" @click="selectDynamicSection(group.id)">
          <div class="tab-icon">
            <el-icon>
              <Memo />
            </el-icon>
          </div>
          <div class="tab-info">
            <div class="tab-name">{{ group.groupName }}</div>
          </div>
        </div>
      </div>

      <!-- 调试信息 -->
      <div v-if="dynamicFormGroups.length === 0" class="debug-info">
        <el-empty description="暂无动态表单数据" />
      </div>
    </div>

    <!-- 右侧表单内容区域 -->
    <div class="form-content-area">
      <div class="content-header">
        <div class="header-title">
          {{ currentTabTitle }}
        </div>
        <div class="header-actions" v-if="viewMode === 'edit'">
          <el-button v-if="activeFormTab === 'basic'" type="primary" @click="saveBasicInfo" :loading="saving.basic">
            保存
          </el-button>
          <el-button v-else-if="activeFormTab === 'files'" type="primary" @click="handleAddFile">
            新增文件
          </el-button>
          <el-button v-else-if="activeFormTab === 'dynamic'" type="primary" @click="saveAllDynamicForms"
            :loading="saving.dynamic">
            保存
          </el-button>
        </div>
      </div>

      <div class="content-body">
        <!-- 基础信息tab内容 -->
        <div v-if="activeFormTab === 'basic'" class="form-section">
          <!-- 查看模式 -->
          <div v-if="viewMode === 'view'" class="basic-info-content">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="资源名称">
                {{ resourceData?.assets_name || '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="公开状态">
                <el-tag :type="resourceData?.public_state === '公开' ? 'success' : 'info'">
                  {{ resourceData?.public_state }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="资源密级">
                {{ resourceData?.security_id || '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="资源标签">
                {{ resourceData?.assets_tag || '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="经度">
                {{ resourceData?.longitude || '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="纬度">
                {{ resourceData?.latitude || '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="位置描述" :span="2">
                {{ resourceData?.location_remark || '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="描述" :span="2">
                <div class="description-content">
                  {{ resourceData?.assets_remark || '--' }}
                </div>
              </el-descriptions-item>
              <el-descriptions-item label="资源封面" :span="2">
                <div class="cover-image">
                  <el-image v-if="resourceData?.assets_cover" :src="resourceData.assets_cover"
                    style="width: 200px; height: 150px" fit="cover" :preview-src-list="[resourceData.assets_cover]" />
                  <span v-else class="no-cover">暂无封面</span>
                </div>
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <!-- 编辑模式 - 完全按照AddResourceDialog样式 -->
          <div v-else class="basic-info-edit">
            <div class="resource-form">
              <!-- 基础信息 -->
              <div class="form-section">
                <div class="section-header">
                  <div class="section-line"></div>
                  <div class="section-title">基础信息</div>
                </div>
                <div class="form-content">
                  <el-form ref="basicFormRef" :model="basicForm" :rules="basicRules" label-width="100px">
                    <el-row :gutter="20">
                      <!-- 左侧封面上传 -->
                      <el-col :span="6">
                        <div class="cover-upload-area">
                          <el-form-item label="资源封面" prop="assets_cover" class="cover-form-item">
                            <div class="upload-container">
                              <ImageUpload v-model:imageUrl="basicForm.assets_cover" borderRadius="4px" width="120px"
                                height="100px" uploadFileUrl="/datacenter/learning/material/cover"
                                class="cover-uploader">
                                <template #empty>
                                  <div class="upload-placeholder">
                                    <el-icon class="upload-icon">
                                      <Picture />
                                    </el-icon>
                                    <div class="upload-text">请上传封面</div>
                                  </div>
                                </template>
                              </ImageUpload>
                              <div class="upload-tip">图片支持JPG、PNG且小于5M</div>
                            </div>
                          </el-form-item>
                        </div>
                      </el-col>
                      <!-- 右侧表单字段 -->
                      <el-col :span="18">
                        <div class="form-fields">
                          <el-row :gutter="16">
                            <el-col :span="12">
                              <el-form-item label="资源名称" prop="assets_name">
                                <el-input v-model="basicForm.assets_name" maxlength="20" placeholder="请输入资源名称"
                                  class="form-input" />
                              </el-form-item>
                            </el-col>
                            <el-col :span="12">
                              <el-form-item label="公开状态" prop="public_state">
                                <el-select v-model="basicForm.public_state" placeholder="请选择公开状态" clearable
                                  class="form-select">
                                  <el-option label="公开" value="公开" />
                                  <el-option label="私有" value="私有" />
                                </el-select>
                              </el-form-item>
                            </el-col>
                          </el-row>
                          <el-row :gutter="16" style="margin-top: 18px;">
                            <el-col :span="12">
                              <el-form-item label="资源密级" prop="security_id">
                                <el-select v-model="basicForm.security_id" placeholder="请选择文件密级" clearable
                                  class="form-select">
                                  <el-option v-for="item in securityList" :key="item.id" :label="item.title"
                                    :value="item.id" />
                                </el-select>
                              </el-form-item>
                            </el-col>
                            <el-col :span="12">
                              <el-form-item label="资源标签" prop="assets_tag">
                                <el-input v-model="basicForm.assets_tag" maxlength="16" placeholder="多个标签用 ，隔开"
                                  class="form-input" />
                              </el-form-item>
                            </el-col>
                          </el-row>
                          <el-row style="margin-top: 18px;">
                            <el-col :span="24">
                              <el-form-item label="描述" prop="assets_remark">
                                <el-input type="textarea" v-model="basicForm.assets_remark" maxlength="250" :rows="3"
                                  show-word-limit placeholder="请输入描述" class="form-textarea" />
                              </el-form-item>
                            </el-col>
                          </el-row>
                        </div>
                      </el-col>
                    </el-row>
                  </el-form>
                </div>
              </div>

              <!-- 位置信息 -->
              <div class="form-section">
                <div class="section-header">
                  <div class="section-line"></div>
                  <div class="section-title">位置信息</div>
                  <el-button @click="mapMakerVisible = true" class="map-picker-btn" type="primary" plain size="small"
                    style="position: absolute !important; right: 40px !important;">
                    <el-icon>
                      <Location />
                    </el-icon>
                    地图拾取
                  </el-button>
                </div>
                <div class="form-content location-content">
                  <el-form ref="basicFormRef" :model="basicForm" label-width="100px">
                    <el-row :gutter="24">
                      <el-col :span="12">
                        <el-form-item label="经度" prop="longitude">
                          <el-input v-model="basicForm.longitude" maxlength="20" placeholder="请输入经度"
                            class="form-input" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="纬度" prop="latitude">
                          <el-input v-model="basicForm.latitude" maxlength="20" placeholder="请输入纬度"
                            class="form-input" />
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-row :gutter="24" style="margin-top: 18px;">
                      <el-col :span="24">
                        <el-form-item label="位置描述" prop="location_remark">
                          <el-input type="textarea" v-model="basicForm.location_remark" maxlength="250" :rows="3"
                            show-word-limit placeholder="请输入位置描述" class="form-input" />
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </el-form>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 文件资源tab内容 -->
        <div v-else-if="activeFormTab === 'files'" class="form-section">
          <FileResourceTable ref="fileResourceRef" :resource-data="resourceData" :view-mode="viewMode"
            @save="handleFileResourceSave" />
        </div>

        <!-- 动态表单tab内容 -->
        <div v-else-if="activeFormTab === 'dynamic'" class="dynamic-forms-container">
          <!-- 左侧锚点导航 -->
          <!-- <div class="dynamic-nav-sidebar">
            <div class="nav-list">
              <div v-for="group in dynamicFormGroups" :key="group.id" class="nav-item"
                :class="{ 'active': activeDynamicSection === group.id }" @click="scrollToDynamicSection(group.id)">
                <div class="nav-icon">
                  <el-icon v-if="group.groupType === 2">
                    <Document />
                  </el-icon>
                  <el-icon v-else>
                    <Grid />
                  </el-icon>
                </div>
                <div class="nav-info">
                  <div class="nav-name">{{ group.groupName }}</div>
                </div>
              </div>
            </div>
          </div> -->

          <!-- 右侧动态表单内容 -->
          <div class="dynamic-forms-content" ref="dynamicContentRef" v-if="currentDynamicGroup">
            <div class="dynamic-form-section">
              <div class="section-header">
                <div class="section-line"></div>
                <div class="section-title">{{ currentDynamicGroup.groupName }}</div>
                <div class="section-type">{{ currentDynamicGroup.groupType === 2 ? '表单类' : '列表类' }}</div>
              </div>
              <div class="section-content">
                <!-- 表单类分组 - 支持二级分组 -->
                <div v-if="currentDynamicGroup.groupType === 2" class="form-type-container">
                  <!-- 如果有二级分组 -->
                  <div v-if="currentDynamicGroup.subGroups && currentDynamicGroup.subGroups.length > 0" class="sub-groups-container">
                    <div v-for="subGroup in currentDynamicGroup.subGroups" :key="subGroup.name" class="sub-group-section">
                      <div class="sub-group-header">
                        <div class="sub-group-line"></div>
                        <div class="sub-group-title">{{ subGroup.name }}</div>
                      </div>
                      <div class="sub-group-content">
                        <CustomFieldForm :ref="el => setDynamicFormRef(`${currentDynamicGroup.id}_${subGroup.name}`, el)"
                          :columnList="subGroup.fields || []" :view-mode="viewMode" :initial-data="subGroup.formData" />
                      </div>
                    </div>
                  </div>
                  <!-- 如果没有二级分组，直接显示表单 -->
                  <div v-if="currentDynamicGroup.columnList && currentDynamicGroup.columnList.length > 0" class="sub-groups-container">
                    <div class="sub-group-section">
                      <div class="sub-group-header">
                        <div class="sub-group-line"></div>
                        <div class="sub-group-title">未分组字段</div>
                      </div>
                      <div class="sub-group-content">
                        <CustomFieldForm :ref="el => setDynamicFormRef(currentDynamicGroup.id, el)" :columnList="currentDynamicGroup.columnList || []"
                          :view-mode="viewMode" :initial-data="currentDynamicGroup.formData" />
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 列表类分组 - 保持原样 -->
                <CustomFieldTable v-else-if="currentDynamicGroup.groupType === 1" :ref="el => setDynamicFormRef(currentDynamicGroup.id, el)"
                  :columnList="currentDynamicGroup.columnList || []" :dataList="currentDynamicGroup.tableData || []" :view-mode="viewMode" />
                <el-empty v-else description="该分组暂无字段数据" />
              </div>
            </div>

            <!-- 空状态提示 -->
            <div v-if="dynamicFormGroups.length === 0" class="empty-dynamic-forms">
              <el-empty description="暂无动态表单数据" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 地图拾取弹窗 -->
  <el-dialog v-model="mapMakerVisible" title="地图拾取" width="1200px" align-center destroy-on-close
    :close-on-click-modal="false" :close-on-press-escape="false" class="map-dialog">
    <LeafletMap @mapClick="onMapClick" style="height: 650px" />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, defineAsyncComponent, onMounted } from 'vue';
import { useMessage } from '/@/hooks/message';
import { getAllColumns, getColumnInfo, updateModelData } from '/@/api/resource/data/resource';
import { fetchList as fetchList_security } from '/@/api/resource/security/level';
import { securityLevelConfig } from '/@/config/resourceConfig';

const CustomFieldForm = defineAsyncComponent(() => import('../edit/CustomFieldForm.vue'));
const CustomFieldTable = defineAsyncComponent(() => import('../edit/CustomFieldTable.vue'));
const ImageUpload = defineAsyncComponent(() => import('/@/components/Upload/Image.vue'));
const FileResourceTable = defineAsyncComponent(() => import('./FileResourceTable.vue'));
const LeafletMap = defineAsyncComponent(() => import('/@/components/LeafletMap/index.vue'));

interface Props {
  resourceData: any;
  viewMode: 'view' | 'edit';
}

const props = withDefaults(defineProps<Props>(), {
  resourceData: null,
  viewMode: 'view',
});

const emit = defineEmits<{
  refresh: [];
}>();

// 响应式数据
const activeFormTab = ref('basic');
const activeDynamicSection = ref('');
const dynamicFormGroups = ref<any[]>([]);
const dynamicContentRef = ref();
const basicFormRef = ref();
const fileResourceRef = ref();
const securityList = ref<any[]>([]);
const dynamicFormRefs = ref<Record<string, any>>({});
const mapMakerVisible = ref(false);

// 保存状态管理
const saving = reactive({
  basic: false,
  files: false,
  dynamic: false,
});

// 计算属性
const currentTabTitle = computed(() => {
  switch (activeFormTab.value) {
    case 'basic':
      return '资源信息';
    case 'files':
      return '文件资源';
    case 'dynamic':
      return '业务属性';
    default:
      return '资源详情';
  }
});

const currentDynamicGroup = computed(() => {
  return dynamicFormGroups.value.find(item => item.id === activeDynamicSection.value);
});

// 基础信息表单数据
const basicForm = reactive({
  assets_cover: '',
  assets_name: '',
  assets_num: '',
  public_state: '',
  security_id: '',
  assets_tag: '',
  assets_remark: '',
  longitude: '',
  latitude: '',
  location_remark: '',
});

// 基础信息表单验证规则
const basicRules = ref({
  assets_name: [{ required: true, message: '资源名称不能为空', trigger: 'blur' }],
  public_state: [{ required: true, message: '公开状态不能为空', trigger: 'change' }],
});

// 设置动态表单引用
const setDynamicFormRef = (groupId: string, el: any) => {
  if (el) {
    dynamicFormRefs.value[groupId] = el;
  }
};

// 滚动到动态表单指定区域
// const scrollToDynamicSection = (sectionId: string) => {
//   activeDynamicSection.value = sectionId;
//   const element = document.getElementById(`dynamic-section-${sectionId}`);
//   if (element && dynamicContentRef.value) {
//     const container = dynamicContentRef.value;
//     const elementTop = element.offsetTop - container.offsetTop;
//     container.scrollTo({
//       top: elementTop - 20, // 留一些间距
//       behavior: 'smooth'
//     });
//   }
// };

const selectDynamicSection = (sectionId: string) => {
  activeDynamicSection.value = sectionId;
  activeFormTab.value = 'dynamic';
};

// 初始化动态表单分组
const initDynamicFormGroups = async () => {
  // 如果没有资源数据，清空动态表单
  if (!props.resourceData?.id) {
    dynamicFormGroups.value = [];
    return;
  }

  try {
    let groups: any[] = [];

    // 尝试获取动态元数据分组
    if (props.resourceData.tableId || props.resourceData.table_id) {
      const res_AllColumns = await getAllColumns({
        tableId: props.resourceData.tableId || props.resourceData.table_id
      });

      groups = (res_AllColumns?.data || []).filter((item: any) => item.defaultGroup !== 1);

      // 为每个分组获取具体的字段数据
      for (const group of groups) {
        try {
          const res = await getColumnInfo({
            resourceId: props.resourceData.id,
            tabName: props.resourceData.tableName || props.resourceData.table_name,
            groupId: group.groupId,
          });

          group.columnList = res.data || [];
          group.id = `group_${group.groupId}`;

          // 处理字段数据格式
          if (group.groupType === 2) {
            // 表单类型 - 处理单条数据和二级分组
            const formData: any = {};
            const subGroupsMap: Record<string, any> = {};

            group.columnList.forEach((field: any) => {
              // 处理字段数据
              let fieldValue = field.object;
              if (['dateRange', 'multiOption', 'image', 'attach'].includes(field.typeCode) &&
                field.object && typeof field.object === 'string') {
                try {
                  fieldValue = JSON.parse(field.object);
                } catch {
                  fieldValue = field.object;
                }
              }

              // 如果字段有subGroupId，按二级分组处理
              if (field.subGroupId) {
                // 使用subGroupId作为分组标识
                const subGroupKey = field.subGroupName || `subgroup_${field.subGroupId}`;

                if (!subGroupsMap[subGroupKey]) {
                  subGroupsMap[subGroupKey] = {
                    id: field.subGroupId,
                    name: subGroupKey,
                    fields: [],
                    formData: {}
                  };
                }
                subGroupsMap[subGroupKey].fields.push(field);
                subGroupsMap[subGroupKey].formData[field.name] = fieldValue;
              } else {
                // 没有二级分组的字段直接添加到主表单
                formData[field.name] = fieldValue;
              }
            });

            // 设置表单数据
            group.formData = formData;

            // 如果有二级分组，设置二级分组数据
            const subGroups = Object.values(subGroupsMap);
            if (subGroups.length > 0) {
              group.subGroups = subGroups;
              // 如果所有字段都有二级分组，清空主表单数据
              if (Object.keys(formData).length === 0) {
                group.columnList = [];
              } else {
                // 过滤掉已分组的字段
                group.columnList = group.columnList.filter((field: any) => !field.subGroupName);
              }
            }
          } else {
            // 列表类型 - 处理多条数据
            const tableData: any[] = [];
            group.columnList.forEach((field: any) => {
              if (field.object && Array.isArray(field.object)) {
                field.object.forEach((item: any, index: number) => {
                  if (!tableData[index]) tableData[index] = {};

                  if (['dateRange', 'multiOption', 'image', 'attach'].includes(field.typeCode) &&
                    typeof item === 'string') {
                    try {
                      tableData[index][field.name] = JSON.parse(item);
                    } catch {
                      tableData[index][field.name] = item;
                    }
                  } else {
                    tableData[index][field.name] = item;
                  }
                });
              }
            });
            group.tableData = tableData;
          }
        } catch (groupError) {
          // 单个分组加载失败不影响其他分组
          useMessage().wraning(`加载分组 ${group.groupId} 失败`);
        }
      }
    }

    // 设置动态表单分组
    dynamicFormGroups.value = groups;
  } catch (error) {
    // 即使动态数据加载失败，也要清空动态表单
    dynamicFormGroups.value = [];
    useMessage().error('加载动态表单数据失败');
  }
};

// 初始化基础信息表单
const initBasicForm = async () => {
  await initSecurityList();
  if (props.resourceData) {
    Object.assign(basicForm, {
      assets_cover: props.resourceData.assets_cover || '',
      assets_name: props.resourceData.assets_name || '',
      assets_num: props.resourceData.assets_num || '',
      public_state: props.resourceData.public_state || '',
      security_id: props.resourceData.security_id || '',
      assets_tag: props.resourceData.assets_tag || '',
      assets_remark: props.resourceData.assets_remark || '',
      longitude: props.resourceData.longitude || '',
      latitude: props.resourceData.latitude || '',
      location_remark: props.resourceData.location_remark || '',
    });
    if (props.viewMode == "edit") {
      basicForm.security_id = securityList.value.find((item: any) => item.securityName == props.resourceData.security_id)?.id;
    }
  }
};

// 初始化安全级别列表
const initSecurityList = async () => {
  try {
    const res_security = await fetchList_security({ enabled: 1 });
    res_security.data.forEach((item: any) => {
      item.title = item.securityName + '（' + securityLevelConfig[item.sortOrder] + '）';
    });
    securityList.value = res_security.data;
  } catch (error) {
    useMessage().error('获取安全级别失败');
  }
};

// 保存基础信息
const saveBasicInfo = async () => {
  try {
    const valid = await basicFormRef.value?.validate();
    if (!valid) return;

    saving.basic = true;

    // 构建更新数据
    const updateData: any = {
      tableName: props.resourceData.tableName || props.resourceData.table_name,
      id: props.resourceData.id,
      datas: {
        ...basicForm,
        longitude: String(basicForm.longitude),
        latitude: String(basicForm.latitude),
        public_state: basicForm.public_state == "公开" ? 1 : 0
      },
      extraData: {
        "assets_num_name": props.resourceData.assets_num_name,
        "update_user": props.resourceData.create_user_id,
        "create_dept": props.resourceData.dept_id,
        "create_user": props.resourceData.create_user_id,
        "security_name": props.resourceData.security_id,
        "public_state": props.resourceData.public_state,
      },
      groupMap: {},
    };

    await updateModelData(updateData);
    useMessage().success('资源信息更新成功');
    emit('refresh');
  } catch (error) {
    useMessage().error('资源信息更新失败');
  } finally {
    saving.basic = false;
  }
};

// 保存文件资源
const saveFileResources = async () => {
  try {
    saving.files = true;

    // 从组件获取文件数据
    const fileList = fileResourceRef.value?.getData() || [];

    // 构建更新数据
    const updateData: any = {
      tableName: props.resourceData.tableName || props.resourceData.table_name,
      id: props.resourceData.id,
      datas: {},
      extraMap: {
        fileList: JSON.stringify(fileList),
      },
      groupMap: {},
    };

    await updateModelData(updateData);
    useMessage().success('文件资源保存成功');
    emit('refresh');
  } catch (error) {
    useMessage().error('文件资源保存失败');
  } finally {
    saving.files = false;
  }
};

// 处理文件资源保存（兼容旧接口）
const handleFileResourceSave = async () => {
  await saveFileResources();
};

// 地图点击事件
const onMapClick = (coord: number[]) => {
  basicForm.longitude = String(coord[0]);
  basicForm.latitude = String(coord[1]);
  mapMakerVisible.value = false;
};

// 处理新增文件
const handleAddFile = () => {
  fileResourceRef.value?.openAddDialog();
};

// 保存所有动态表单
const saveAllDynamicForms = async () => {
  try {
    saving.dynamic = true;

    if (dynamicFormGroups.value.length === 0) {
      useMessage().wraning('暂无动态表单数据需要保存');
      return;
    }

    // 收集所有动态表单数据
    const groupMap: Record<string, any> = {};
    let hasValidationError = false;
    let errorGroupName = '';

      try {
        if (currentDynamicGroup.value.groupType === 2) {
          // 表单类型 - 处理主表单和二级分组
          let allFormData: any = {};

          // 处理主表单数据（如果有）
          if (currentDynamicGroup.value.columnList && currentDynamicGroup.value.columnList.length > 0) {
            const mainFormRef = dynamicFormRefs.value[currentDynamicGroup.value.id];
            if (mainFormRef) {
              const isValid = await mainFormRef.validate();
              if (!isValid) {
                hasValidationError = true;
                errorGroupName = currentDynamicGroup.value.groupName;
                // return;
              }
              const mainFormData = mainFormRef.getData();
              Object.assign(allFormData, mainFormData);
            }
          }

          // 处理二级分组数据（如果有）
          if (currentDynamicGroup.value.subGroups && currentDynamicGroup.value.subGroups.length > 0) {
            for (const subGroup of currentDynamicGroup.value.subGroups) {
              const subFormRef = dynamicFormRefs.value[`${currentDynamicGroup.value.id}_${subGroup.name}`];
              if (subFormRef) {
                const isValid = await subFormRef.validate();
                if (!isValid) {
                  hasValidationError = true;
                  errorGroupName = `${currentDynamicGroup.value.groupName} - ${subGroup.name}`;
                  break;
                }
                const subFormData = subFormRef.getData();
                Object.assign(allFormData, subFormData);
              }
            }

            // if (hasValidationError) return;
          }

          // 处理数组字段
          for (const k in allFormData) {
            if (Array.isArray(allFormData[k])) {
              allFormData[k] = JSON.stringify(allFormData[k]);
            }
          }

          // 表单类型数据直接合并到datas
          groupMap[`form_${currentDynamicGroup.value.groupId}`] = allFormData;
        } else {
          // 列表类型数据 - 保持原有逻辑
          const formRef = dynamicFormRefs.value[currentDynamicGroup.value.id];
          if (!formRef) return;

          const isValid = await formRef.validate();
          if (!isValid) {
            hasValidationError = true;
            errorGroupName = currentDynamicGroup.value.groupName;
            return;
          }

          const formData = formRef.getData();

          // 列表类型数据 - 处理每行的数组字段
          if (Array.isArray(formData)) {
            formData.forEach((obj: any) => {
              for (const k in obj) {
                if (Array.isArray(obj[k])) {
                  obj[k] = JSON.stringify(obj[k]);
                }
              }
            });
          }
          groupMap[currentDynamicGroup.value.groupId] = formData;
        }
      } catch (error) {
        hasValidationError = true;
        errorGroupName = currentDynamicGroup.value.groupName;
        // return;
      }

    if (hasValidationError) {
      useMessage().error(`表单验证失败：${errorGroupName}，请检查输入`);
      return;
    }

    // 构建更新数据
    const updateData: any = {
      tableName: props.resourceData.tableName || props.resourceData.table_name,
      id: props.resourceData.id,
      datas: {
        ...basicForm,
        longitude: String(basicForm.longitude),
        latitude: String(basicForm.latitude),
        public_state: basicForm.public_state == "公开" ? 1 : 0
      },
      extraData: {
        "assets_num_name": props.resourceData.assets_num_name,
        "update_user": props.resourceData.create_user_id,
        "create_dept": props.resourceData.dept_id,
        "create_user": props.resourceData.create_user_id,
        "security_name": props.resourceData.security_id,
        "public_state": props.resourceData.public_state,
      },
      groupMap: {}
    };

    // 分离表单类型和列表类型数据
    for (const key in groupMap) {
      if (key.startsWith('form_')) {
        // 表单类型数据合并到datas
        Object.assign(updateData.datas, groupMap[key]);
      } else {
        // 列表类型数据保持在groupMap
        updateData.groupMap[key] = groupMap[key];
      }
    }

    await updateModelData(updateData);
    useMessage().success('动态表单保存成功');
    initDynamicFormGroups();
    emit('refresh');
  } catch (error) {
    useMessage().error('动态表单保存失败');
  } finally {
    saving.dynamic = false;
  }
};

// 监听资源数据变化
watch(() => props.resourceData, (newData) => {
  if (newData) {
    initDynamicFormGroups();
    initBasicForm();
  }
}, { immediate: true });

// 监听编辑模式变化
watch(() => props.viewMode, (newMode) => {
  if (newMode === 'edit') {
    initBasicForm();
  }
});

onMounted(() => {
  // if (props.resourceData) {
  //   initDynamicFormGroups();
  // }
});
</script>

<style scoped lang="scss">
.resource-detail-container {
  display: flex;
  height: 100%;
  background-color: #f5f7fa;
}

// 左侧表单tabs导航
.form-tabs-sidebar {
  width: 240px; // 从280px调整为240px
  background-color: white;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;

  .sidebar-title {
    padding: 20px;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    border-bottom: 1px solid #e4e7ed;
  }

  .tabs-list {
    flex: 1;
    overflow-y: auto;
    padding: 10px;

    .tab-item {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      margin-bottom: 8px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s;
      border: 1px solid transparent;

      &:hover {
        background-color: #f5f7fa;
      }

      &.active {
        background-color: var(--el-color-primary-light-9);
        border-color: var(--el-color-primary);

        .tab-icon {
          color: var(--el-color-primary); // 修复图标颜色
        }

        .tab-name {
          color: var(--el-color-primary); // 确保文字颜色应用主题色
        }
      }

      .tab-icon {
        margin-right: 12px;
        font-size: 18px;
        color: #909399;
        transition: color 0.3s; // 添加颜色过渡效果
      }

      .tab-info {
        flex: 1;

        .tab-name {
          font-size: 14px;
          font-weight: 500;
          color: #303133;
          margin-bottom: 4px;
          transition: color 0.3s; // 添加颜色过渡效果
        }

        .tab-type {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }

  .debug-info {
    padding: 20px;
    text-align: center;
  }
}

// 右侧表单内容区域
.form-content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: white;
  margin-left: 15px;
  border-radius: 8px;
  overflow: hidden;

  .content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 24px;
    border-bottom: 1px solid #e4e7ed;
    background-color: #fafafa;

    .header-title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }
  }

  .content-body {
    flex: 1;
    overflow-y: auto;
    padding: 10px;

    .form-section {
      height: 100%;
    }
  }
}

// 动态表单容器
.dynamic-forms-container {
  display: flex;
  height: 100%;

  // 左侧动态表单导航
  .dynamic-nav-sidebar {
    width: 180px; // 从200px调整为180px
    background-color: #fafafa;
    border-right: 1px solid #e4e7ed;
    padding: 16px;

    .nav-title {
      font-size: 14px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e4e7ed;
    }

    .nav-list {
      .nav-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        margin-bottom: 4px;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          background-color: #f0f2f5;
        }

        &.active {
          background-color: var(--el-color-primary-light-9);
          border: 1px solid var(--el-color-primary-light-7);

          .nav-icon {
            color: var(--el-color-primary); // 确保图标应用主题色
          }

          .nav-name {
            color: var(--el-color-primary); // 修复文字颜色应用主题色
          }
        }

        .nav-icon {
          margin-right: 8px;
          font-size: 16px;
          color: #909399;
          transition: color 0.3s;
        }

        .nav-info {
          flex: 1;

          .nav-name {
            font-size: 13px;
            font-weight: 500;
            color: #303133;
            margin-bottom: 2px;
            transition: color 0.3s;
          }

          .nav-type {
            font-size: 11px;
            color: #909399;
          }
        }
      }
    }
  }

  // 右侧动态表单内容
  .dynamic-forms-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    scroll-behavior: smooth;

    .dynamic-form-section {
      margin-bottom: 32px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;

        .section-line {
          width: 4px;
          height: 18px;
          background-color: var(--el-color-primary, #A12F2F);
          border-radius: 2px;
          margin-right: 15px;
        }

        .section-title {
          font-size: 18px;
          font-weight: 600;
          color: #303133;
          letter-spacing: 0.5px;
        }

        .section-type {
          margin-left: 12px;
          padding: 2px 8px;
          background-color: var(--el-color-primary-light-9);
          color: var(--el-color-primary);
          font-size: 12px;
          border-radius: 4px;
        }
      }

      .section-content {
        background-color: #ffffff;
        border-radius: 8px;
        padding: 20px;
        border: 1px solid #e4e7ed;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      }
    }

    .empty-dynamic-forms {
      text-align: center;
      padding: 40px 20px;
      color: #909399;
    }

    // 表单类型容器
    .form-type-container {
      .sub-groups-container {
        .sub-group-section {
          margin-bottom: 24px;

          &:last-child {
            margin-bottom: 0;
          }

          .sub-group-header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 1px solid #f0f2f5;

            .sub-group-line {
              width: 3px;
              height: 14px;
              background-color: var(--el-color-primary-light-3);
              border-radius: 2px;
              margin-right: 12px;
            }

            .sub-group-title {
              font-size: 15px;
              font-weight: 600;
              color: #606266;
              letter-spacing: 0.3px;
            }
          }

          .sub-group-content {
            padding-left: 15px;
            border-left: 2px solid #f0f2f5;
          }
        }
      }
    }
  }
}

// 基础信息样式
.basic-info-content {
  .description-content {
    max-height: 100px;
    overflow-y: auto;
    line-height: 1.6;
    color: #606266;
  }

  .cover-image {
    .no-cover {
      color: #909399;
      font-style: italic;
    }
  }
}

// 基础信息编辑样式 - 复用AddResourceDialog样式
.basic-info-edit {
  .resource-form {
    .form-section {
      margin-bottom: 32px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-header {
        display: flex;
        align-items: center;
        margin-bottom: 15px;

        .section-line {
          width: 4px;
          height: 16px;
          background-color: var(--el-color-primary, #A12F2F);
          border-radius: 2px;
          margin-right: 15px;
          margin-bottom: 5px;
        }

        .section-title {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          letter-spacing: 0.5px;
        }
      }

      .form-content {
        background-color: #ffffff;
        border-radius: 8px;
        padding: 20px 0px;
        padding-right: 15px;
        border: 1px solid #e4e7ed;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      }
    }
  }

  // 封面上传区域
  .cover-upload-area {
    .cover-form-item {
      :deep(.el-form-item__label) {
        font-weight: 500;
        color: #606266;
      }
    }

    .upload-container {
      .cover-uploader {
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        transition: all 0.3s;

        &:hover {
          border-color: #409eff;
        }

        .upload-placeholder {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100px;

          .upload-icon {
            font-size: 28px;
            color: #c0c4cc;
            margin-bottom: 8px;
          }

          .upload-text {
            font-size: 14px;
            color: #606266;
          }
        }
      }

      .upload-tip {
        font-size: 12px;
        color: #909399;
        text-align: left;
      }
    }
  }

  // 表单字段区域
  .form-fields {

    .form-input,
    .form-select,
    .form-textarea {
      width: 100%;

      :deep(.el-input__wrapper) {
        border-radius: 6px;
        box-shadow: 0 0 0 1px #dcdfe6 inset;
        transition: all 0.3s;

        &:hover {
          box-shadow: 0 0 0 1px #c0c4cc inset;
        }

        &.is-focus {
          box-shadow: 0 0 0 1px var(--el-color-primary, #A12F2F) inset;
        }
      }
    }

    :deep(.el-form-item__label) {
      font-weight: 500;
      color: #606266;
    }
  }

  // 位置信息区域
  .location-content {
    position: relative;
  }

  :deep(textarea) {
    resize: none !important;
  }
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-descriptions) {
  .el-descriptions__label {
    font-weight: 600;
    color: #303133;
    background-color: #fafafa;
  }

  .el-descriptions__content {
    color: #606266;
  }
}

// 基础信息编辑模式样式 - 参考AddResourceDialog
.basic-info-edit {
  .resource-form {
    .form-section {
      margin-bottom: 32px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        position: relative;

        .section-line {
          width: 4px;
          height: 18px;
          background-color: var(--el-color-primary, #A12F2F);
          border-radius: 2px;
          margin-right: 15px;
        }

        .section-title {
          font-size: 18px;
          font-weight: 600;
          color: #303133;
          letter-spacing: 0.5px;
        }

        .map-picker-btn {
          font-size: 14px;
          padding: 8px 16px;
          border-radius: 6px;
          transition: all 0.3s;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
          }
        }
      }

      .form-content {
        background-color: #ffffff;
        border-radius: 8px;
        padding: 24px;
        border: 1px solid #e4e7ed;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      }
    }

    // 封面上传区域
    .cover-upload-area {
      display: flex;
      flex-direction: column;
      align-items: center;

      .cover-form-item {
        :deep(.el-form-item__label) {
          font-weight: 500;
          color: #606266;
        }
      }

      .upload-container {
        display: flex;
        flex-direction: column;
        align-items: center;

        .cover-uploader {
          border: 2px dashed #dcdfe6;
          border-radius: 8px;
          cursor: pointer;
          position: relative;
          overflow: hidden;
          transition: all 0.3s;

          &:hover {
            border-color: #409eff;
          }

          .upload-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100px;

            .upload-icon {
              font-size: 28px;
              color: #c0c4cc;
              margin-bottom: 8px;
            }

            .upload-text {
              font-size: 14px;
              color: #606266;
            }
          }
        }

        .upload-tip {
          font-size: 12px;
          color: #909399;
          text-align: center;
          margin-top: 8px;
        }
      }
    }

    // 表单字段区域
    .form-fields {

      .form-input,
      .form-select,
      .form-textarea {
        width: 100%;

        :deep(.el-input__wrapper) {
          border-radius: 6px;
          box-shadow: 0 0 0 1px #dcdfe6 inset;
          transition: all 0.3s;

          &:hover {
            box-shadow: 0 0 0 1px #c0c4cc inset;
          }

          &.is-focus {
            box-shadow: 0 0 0 1px var(--el-color-primary, #A12F2F) inset;
          }
        }
      }

      :deep(.el-form-item__label) {
        font-weight: 500;
        color: #606266;
      }
    }

    // 位置信息区域
    .location-content {
      position: relative;
    }

    // 表单项间距调整
    :deep(.el-form-item) {
      margin-bottom: 18px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    // textarea禁止拖拽
    :deep(textarea) {
      resize: none !important;
    }
  }
}

// 基础信息查看模式样式
.basic-info-content {
  .description-content {
    max-height: 100px;
    overflow-y: auto;
    line-height: 1.6;
  }

  .cover-image {
    .no-cover {
      color: #909399;
      font-style: italic;
    }
  }
}

// 地图弹窗样式
.map-dialog {
  :deep(.el-dialog__body) {
    padding: 0;
  }
}
</style>
