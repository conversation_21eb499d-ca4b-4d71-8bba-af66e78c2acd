<template>
  <el-dialog
    v-model="dialogVisible"
    title="新增"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="node-dialog">
      <div class="dialog-content">
        <!-- 左侧：可选资源列表 -->
        <div class="left-panel">
          <div class="panel-header">
            <el-input
              v-model="searchKeyword"
              placeholder="请输入内容"
              clearable
              @input="handleSearch"
            >
              <template #suffix>
                <el-icon class="search-icon">
                  <Search />
                </el-icon>
              </template>
            </el-input>
          </div>
          <div class="panel-content">
            <div class="resource-tree">
              <div class="tree-group" v-for="group in filteredResourceData" :key="group.id">
                <div class="group-header" @click="toggleGroup(group)">
                  <el-icon class="expand-icon" :class="{ 'expanded': group.expanded }">
                    <CaretRight />
                  </el-icon>
                  <span>{{ group.name }}</span>
                </div>
                <div class="group-content" v-show="group.expanded">
                  <div
                    class="resource-item"
                    v-for="resource in group.children"
                    :key="resource.id"
                    @click="selectResource(resource)"
                  >
                    <el-checkbox
                      :model-value="isSelected(resource.id)"
                      @change="toggleSelection(resource)"
                    />
                    <span class="resource-name">{{ resource.name }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 中间：操作按钮 -->
        <div class="center-panel">
          <div class="transfer-buttons">
            <el-button
              type="primary"
              :icon="ArrowRight"
              @click="addSelected"
              :disabled="selectedResources.length === 0"
            />
            <el-button
              :icon="ArrowLeft"
              @click="removeSelected"
              :disabled="selectedNodes.length === 0"
            />
          </div>
        </div>

        <!-- 右侧：已选节点列表 -->
        <div class="right-panel">
          <div class="panel-header">
            <span class="panel-title">已选</span>
            <span class="count-text">暂无数据</span>
          </div>
          <div class="panel-content">
            <div class="selected-list">
              <div
                class="selected-item"
                v-for="node in nodeList"
                :key="node.id"
                @click="selectNode(node)"
              >
                <el-checkbox
                  :model-value="isNodeSelected(node.id)"
                  @change="toggleNodeSelection(node)"
                />
                <span class="node-name">{{ node.name }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Search, CaretRight, ArrowRight, ArrowLeft } from '@element-plus/icons-vue';
import { getResourceList } from '/@/api/backStageManage/resource';
import { useMessage } from '/@/hooks/message';

interface ResourceItem {
  id: string;
  name: string;
  children?: ResourceItem[];
  expanded?: boolean;
}

interface NodeItem {
  id: string;
  name: string;
  resourceId: string;
}

interface Emits {
  (e: 'confirm', nodes: NodeItem[]): void;
}

const emit = defineEmits<Emits>();
const { error } = useMessage();

// 响应式数据
const dialogVisible = ref(false);
const searchKeyword = ref('');
const resourceData = ref<ResourceItem[]>([]);
const selectedResources = ref<string[]>([]);
const selectedNodes = ref<string[]>([]);
const nodeList = ref<NodeItem[]>([]);
const currentBuildingId = ref('');

// 计算属性
const filteredResourceData = computed(() => {
  if (!searchKeyword.value) {
    return resourceData.value;
  }

  return resourceData.value.map(group => ({
    ...group,
    children: group.children?.filter(resource =>
      resource.name.includes(searchKeyword.value)
    ) || []
  })).filter(group => group.children.length > 0);
});

// 获取资源数据
const fetchResourceData = async (buildingId: string) => {
  try {
    const response = await getResourceList({
      buildingId: buildingId,
      pageNum: 1,
      pageSize: 1000
    });

    // 模拟分组数据结构，实际应根据API返回的数据结构调整
    const resources = response?.data?.records || [];
    resourceData.value = resources.map((item: any) => ({
      ...item,
      name: item.resourceName,
    }));
  } catch (err) {
    error('获取资源数据失败');
  }
};

// 搜索处理
const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
};

// 切换分组展开状态
const toggleGroup = (group: ResourceItem) => {
  group.expanded = !group.expanded;
};

// 选择资源
const selectResource = (resource: ResourceItem) => {
  toggleSelection(resource);
};

// 切换资源选择状态
const toggleSelection = (resource: ResourceItem) => {
  const index = selectedResources.value.indexOf(resource.id);
  if (index > -1) {
    selectedResources.value.splice(index, 1);
  } else {
    selectedResources.value.push(resource.id);
  }
};

// 检查资源是否被选中
const isSelected = (resourceId: string) => {
  return selectedResources.value.includes(resourceId);
};

// 添加选中的资源到节点列表
const addSelected = () => {
  selectedResources.value.forEach(resourceId => {
    // 查找资源信息
    let resource: ResourceItem | undefined;
    for (const group of resourceData.value) {
      resource = group.children?.find(r => r.id === resourceId);
      if (resource) break;
    }

    if (resource && !nodeList.value.find(n => n.resourceId === resourceId)) {
      nodeList.value.push({
        id: `node_${resourceId}`,
        name: resource.name,
        resourceId: resourceId
      });
    }
  });

  // 清空选中状态
  selectedResources.value = [];
};

// 移除选中的节点
const removeSelected = () => {
  selectedNodes.value.forEach(nodeId => {
    const index = nodeList.value.findIndex(n => n.id === nodeId);
    if (index > -1) {
      nodeList.value.splice(index, 1);
    }
  });

  // 清空选中状态
  selectedNodes.value = [];
};

// 选择节点
const selectNode = (node: NodeItem) => {
  toggleNodeSelection(node);
};

// 切换节点选择状态
const toggleNodeSelection = (node: NodeItem) => {
  const index = selectedNodes.value.indexOf(node.id);
  if (index > -1) {
    selectedNodes.value.splice(index, 1);
  } else {
    selectedNodes.value.push(node.id);
  }
};

// 检查节点是否被选中
const isNodeSelected = (nodeId: string) => {
  return selectedNodes.value.includes(nodeId);
};

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false;
  // 重置数据
  selectedResources.value = [];
  selectedNodes.value = [];
  nodeList.value = [];
  searchKeyword.value = '';
};

// 确认选择
const handleConfirm = () => {
  emit('confirm', nodeList.value);
  handleClose();
};

// 打开弹窗
const openDialog = (buildingId: string) => {
  currentBuildingId.value = buildingId;
  dialogVisible.value = true;
  fetchResourceData(buildingId);
};

// 暴露方法
defineExpose({
  openDialog
});

onMounted(() => {
  // 组件挂载时可以进行初始化
});
</script>

<style lang="scss" scoped>
.node-dialog {
  .dialog-content {
    display: flex;
    height: 400px;
    gap: 16px;
  }

  .left-panel,
  .right-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    border: 1px solid #e4e7ed;
    border-radius: 6px;

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid #e4e7ed;
      background-color: #f5f7fa;

      .panel-title {
        font-weight: 600;
        color: #303133;
      }

      .count-text {
        font-size: 12px;
        color: #909399;
      }
    }

    .panel-content {
      flex: 1;
      overflow-y: auto;
      padding: 8px;
    }
  }

  .center-panel {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;

    .transfer-buttons {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .el-button {
        width: 40px;
        height: 32px;
        padding: 0;
        margin-left: 0;
      }
    }
  }

  .resource-tree {
    .tree-group {
      margin-bottom: 8px;

      .group-header {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        cursor: pointer;
        border-radius: 4px;
        transition: background-color 0.3s;

        &:hover {
          background-color: #f5f7fa;
        }

        .expand-icon {
          margin-right: 8px;
          transition: transform 0.3s;

          &.expanded {
            transform: rotate(90deg);
          }
        }
      }

      .group-content {
        padding-left: 24px;

        .resource-item {
          display: flex;
          align-items: center;
          padding: 6px 12px;
          cursor: pointer;
          border-radius: 4px;
          transition: background-color 0.3s;

          &:hover {
            background-color: #f0f9ff;
          }

          .resource-name {
            margin-left: 8px;
            font-size: 14px;
            color: #606266;
          }
        }
      }
    }
  }

  .selected-list {
    .selected-item {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      cursor: pointer;
      border-radius: 4px;
      transition: background-color 0.3s;

      &:hover {
        background-color: #f0f9ff;
      }

      .node-name {
        margin-left: 8px;
        font-size: 14px;
        color: #606266;
      }
    }
  }
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-input__wrapper) {
  border-radius: 4px;
}
</style>