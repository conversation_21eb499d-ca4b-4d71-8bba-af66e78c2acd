import request from "/@/utils/request"

export function fetchList(query?: Object) {
  return request({
    url: '/business/yjEventInfo/byPage',
    method: 'get',
    params: query
  })
}

// 动态上报
export function addObj(obj?: Object) {
  return request({
    url: '/business/yjEventInfoDynamic',
    method: 'post',
    data: obj
  })
}

export function getObj(id?: string) {
  return request({
    url: '/business/yjEventInfo/getEventInfoDynamic',
    method: 'get',
    params: {id:id}
  })
}

export function delObjs(ids?: Object) {
  return request({
    url: '/business/yjEventInfo',
    method: 'delete',
    data: ids
  })
}

// 事件上报
export function putObj(obj?: Object) {
  return request({
    url: '/business/yjEventInfo',
    method: 'post',
    data: obj
  })
}

export function cancelObjs(ids?: Array<string>) {
  return request({
    url: '/business/yjEventInfo/updateByIds',
    method: 'post',
    data: ids
  })
}