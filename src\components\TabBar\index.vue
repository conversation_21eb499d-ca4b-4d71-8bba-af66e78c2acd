<template>
	<div class="tab-bar" :style="{ justifyContent: center ? 'center' : 'flex-start' }">
		<div
			:class="[currentComIndex === index ? 'active-tab-bar-item' : 'tab-bar-item']"
			v-for="(item, index) in props.tabBar"
			:key="item.name"
			@click="onClickTabBar(item, index)"
		>
			<span>{{ item.title }}</span>
		</div>
	</div>
	<div class="render-component">
		<component :is="currentComponent"></component>
	</div>
</template>

<script setup lang="ts" name="systemWbPointHand">
import type { ComponentPublicInstance } from 'vue'

interface tabBarItem {
	name: string
	title: string
	component: ComponentPublicInstance
}

interface Props {
	tabBar: Array<tabBarItem>
	center?: boolean
}

const props = withDefaults(defineProps<Props>(), {
	center: false,
})

const currentComponent = ref(props.tabBar[0].component) // 当前渲染组件
const currentComIndex = ref(0) // 当前渲染组件下标

const onClickTabBar = (item: tabBarItem, index: number) => {
	currentComIndex.value = index
	currentComponent.value = item.component
}
</script>

<style lang="scss" scoped>
.tab-bar {
	width: 100%;
	display: flex;
	align-items: center;
	background: #fff;
	padding: 0 20px;
	margin-bottom: 20px;

	.tab-bar-item {
		font-size: 16px;
		padding: 10px 15px;
		cursor: pointer;
		height: 44px;
	}

	.active-tab-bar-item {
		@extend .tab-bar-item;
		font-weight: bold;
		border-bottom: 4px solid var(--el-color-primary);
	}
}

.render-component {
	width: 100%;
	flex-grow: 1;
	height: 100px;
}
</style>
