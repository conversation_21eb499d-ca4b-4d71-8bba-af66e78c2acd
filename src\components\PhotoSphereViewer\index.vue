<template>
  <div style="position: relative; width: 100%; height: 100%;">
    <div :id="props.photoSphereViewerContainerId" class="m-panorama"></div>
    <div class="m-img" style="z-index: 9999;" v-if="ewmurl">
      <img :src="ewmurl" width="120px" height="120px" />
    </div>
  </div>
</template>
<script setup lang="ts">
  import { Viewer as PhotoSphereViewer, utils } from "photo-sphere-viewer"; // 引入组件
  import { CubemapAdapter } from "photo-sphere-viewer/dist/adapters/cubemap.js";
  import "photo-sphere-viewer/dist/photo-sphere-viewer.css"; // 引入样式
  const props = defineProps({
    url: {
      type: String,
      default: "",
    },
    ewmurl: {
      type: String,
      default: null,
    },
    type: {
      type: String,
      default: "default",
    },
    photoSphereViewerContainerId: {
      type: String,
      default: "photoSphereViewerContainer",
    },
  });
  const panoramaViewer = ref();
  onMounted(() => {
    // 重置表单数据
    nextTick(() => {
      initPanoramaViewer();
    });
  });
  onBeforeUnmount(() => {
    closePanoramaViewer();
  });
  const initPanoramaViewer = () => {
    closePanoramaViewer();
    panoramaViewer.value = new PhotoSphereViewer({
      // （必需的） 将包含全景图或元素标识符的 HTML 元素
      container: props.photoSphereViewerContainerId,
      // （必需的） 全景路径。必须是默认 equirectangular 适配器的单个 URL。其他适配器支持其他值。
      // photo-sphere-viewer支持多种适配器，这里讲到的是默认的 equirectangular 适配器，使用起来最为简单，但是要求图片必须为全景图
      adapter: props.type == "cubemap" ? CubemapAdapter : undefined,
      panorama:
        props.type == "cubemap"
          ? {
              left: props.url + "/left.jpg",
              front: props.url + "/front.jpg",
              right: props.url + "/right.jpg",
              back: props.url + "/back.jpg",
              top: props.url + "/top.jpg",
              bottom: props.url + "/bottom.jpg",
            }
          : props.url,
      // 这里定义展示视图的大小，也可以通过CSS在样式中定义
      // size: {
      //   width: panorama.value.offsetWidth + "px",
      //   height: panorama.value.offsetHeight + "px",
      //   // width: "1237px",
      //   // height: "550px",
      // },
      minFov: 0, // 最小视野（对应于最大缩放），介于 1 和 179 之间。
      maxFov: 140, // 最大视野（对应于最小缩放），介于 1 和 179 之间
      touchmoveTwoFingers: true, // 支持手势
      loadingTxt: "加载中...", // 加载中文字
      fisheye: true, // 使用 true 启用鱼眼效果或指定效果强度 (true = 1.0)。
      lang: {
        // 语言
        autorotate: "自动旋转",
        zoom: "缩放",
        zoomOut: "缩小",
        zoomIn: "放大",
        move: "移动",
        download: "下载",
        fullscreen: "全屏",
        menu: "菜单",
        twoFingers: "双指导航",
        ctrlZoom: "ctrl + scroll缩放图像",
        loadError: "无法加载全景图",
      },
    });
    // window.aaa = panoramaViewer.value;
    // viewer.exitFullscreen();
    panoramaViewer.value.animate({
      longitude: "-90deg",
      latitude: "-90deg",
      zoom: 0,
      speed: "50000rpm",
    });
    startAnimation();
  };
  const startAnimation = () => {
    new utils.Animation({
      properties: {
        lat: { start: -Math.PI * 0.5, end: 0 },
        long: { start: -Math.PI * 0.5, end: 0 },
        zoom: { start: 0, end: 50 },
        fisheye: { start: true, end: false },
      },
      delay: 1500,
      duration: 2000,
      easing: "inOutQuad",
      onTick: (properties) => {
        panoramaViewer.value.setOption("fisheye", properties.fisheye);
        panoramaViewer.value.rotate({
          longitude: properties.long,
          latitude: properties.lat,
        });
        panoramaViewer.value.zoom(properties.zoom);
      },
    }).then(function () {
      panoramaViewer.value.setOptions({
        autorotateIdle: true, // 默认是否自动旋转
        autorotateDelay: 100, // 自动旋转开始后的延迟，以毫秒为单位
        autorotateSpeed: "1rpm", // 自动旋转速度
        minFov: 30, // 最小视野（对应于最大缩放），介于 1 和 179 之间。
        maxFov: 90, // 最大视野（对应于最小缩放），介于 1 和 179 之间
      });
    });
  };
  const closePanoramaViewer = () => {
    if (panoramaViewer.value) {
      panoramaViewer.value.__stopAll()
      panoramaViewer.value.destroy();
      panoramaViewer.value = null;
    }
  };
  const refreshUi = () => {
    panoramaViewer.value.resize({
      width: "100%",
      height: "100%",
    });
  };
  defineExpose({
    refreshUi,
  });
</script>
<style lang="scss" scoped>
  .m-panorama {
    // width: 100%;
    // height: 100%;
    width: 100% !important;
    height: 100% !important;
    top: 0;
    left: 0;
    margin: 0;
  }
  .m-img {
    position: fixed;
    right: 15px;
    top: 58px;
  }
</style>
