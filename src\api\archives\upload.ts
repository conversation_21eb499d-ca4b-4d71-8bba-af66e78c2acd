import request from "/@/utils/request"

// 获取我上传的档案列表
export function queryMyUploadList(query?: Object) {
  return request({
    url: '/archive/wb/resource/queryMyUploadList',
    method: 'get',
    params: query,
  })
}

// 获取所有档案列表
export function queryArchiveList(query?: Object) {
  return request({
    url: '/archive/wb/resource/queryArchiveList',
    method: 'get',
    params: query,
  })
}

// 查询OCR识别处理列表
export function queryOcrDealList(query?: Object) {
  return request({
    url: '/archive/wb/resource/queryOcrDealList',
    method: 'get',
    params: query,
  })
}

// OCR识别-按状态统计
export function StatisticsOcrNum(query?: Object) {
  return request({
    url: '/archive/wb/resource/StatisticsOcrNum',
    method: 'get',
    params: query,
  })
}

// OCR识别 重新识别
export function restartOcr(query?: Object) {
  return request({
    url: '/archive/wb/resource/restartOcr',
    method: 'get',
    params: query,
  })
}