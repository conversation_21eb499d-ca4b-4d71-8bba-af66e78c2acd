<template>
	<div class="file-detail-box">
		<div class="file-detail-header">
			<el-button class="file-detail-back" @click="emit('close')">
				<el-icon><ArrowLeftBold /></el-icon>返回
			</el-button>
			<div class="action-buttons">
				<el-button type="primary" @click="isShowGraph = !isShowGraph">{{ isShowGraph ? '详情展开' : '知识图谱' }}</el-button>
				<el-button type="primary" @click="onDownload">数据下载</el-button>
			</div>
		</div>
		<div class="file-detail-content" v-loading="isLoading">
			<div class="file-detail-infos">
				<div class="result-item">
					<div class="item-info flex" style="width: 450px">
						<div class="item-cover">
							<el-image v-if="detailInfo.cover" :src="detailInfo.cover" fit="cover" :preview-src-list="[detailInfo.cover]" />
							<div class="file-type-label">{{ getFileType(detailInfo.type)?.typeName || '-' }}</div>
						</div>
						<div class="item-file-message flex flex-col justify-between">
							<div>{{ detailInfo.name }}</div>
							<div class="text-info">
								<div class="text-label">目录：</div>
								<div class="text-value">{{ detailInfo.folderPath?.split(',').join(' > ') || '-' }}</div>
							</div>
						</div>
					</div>
					<div class="item-info flex flex-col justify-end" style="width: 450px">
						<div class="text-info">
							<div class="text-label">来源单位：</div>
							<div class="text-value">{{ detailInfo.deptName || '-' }}</div>
						</div>
						<div class="text-info mt-2">
							<div class="text-label">关键词：</div>
							<div class="text-value">{{ detailInfo.labels || '-' }}</div>
						</div>
					</div>
					<div class="item-info flex flex-col justify-end">
						<div class="text-info">
							<div class="text-label">上传人：</div>
							<div class="text-value">{{ detailInfo.createUser || '-' }}</div>
						</div>
						<div class="text-info mt-2">
							<div class="text-label">上传时间：</div>
							<div class="text-value">{{ detailInfo.createTime || '-' }}</div>
						</div>
					</div>
				</div>
			</div>
			<div class="file-detail-bot">
				<FileInfoTab :record="detailInfo" v-if="!isShowGraph" />
				<FileGraph :record="detailInfo" v-if="isShowGraph" />
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { getObj } from '/@/api/resource/filelist/file';
import FileInfoTab from '/@/views/resourceManagement/resourceFile/detail/FileInfoTab.vue';
import FileGraph from '/@/views/resourceManagement/resourceFile/detail/FileGraph.vue';
import { getFileType } from '/@/config/resourceConfig';

const emit = defineEmits(['close']);
const detailInfo = ref<any>({});
const isShowGraph = ref(false);
const isLoading = ref(false);
const props = defineProps({
	detailId: {
		type: String,
		default: () => '',
	},
});
onMounted(() => {
	initInfo(props.detailId);
});

// 初始化文件基本信息
const initInfo = (id: String) => {
	isLoading.value = true;
	getObj(id)
		.then((res) => {
			detailInfo.value = res.data;
		})
		.finally(() => {
			isLoading.value = false;
		});
};

// 文件下载
const onDownload = async () => {
	//   let params = {
	//     materialId: detailInfo.value.id,
	//     tabName: detailInfo.value.tabName,
	//     resourceId: detailInfo.value.resourceId,
	//   };
	//   await actionDownload(params);
	downloadUrlFile(
		detailInfo.value.downloadUrl,
		detailInfo.value.name + '.' + (detailInfo.value.downloadUrl?.split('.')?.pop() || detailInfo.value.format)
	);
};
// 下载含有url的文件
function downloadUrlFile(url: any, fileName: any) {
	const url2 = url?.replace(/\\/g, '/');
	const xhr = new XMLHttpRequest();
	xhr.open('GET', url2, true);
	xhr.responseType = 'blob';
	//xhr.setRequestHeader('Authorization', 'Basic a2VybWl0Omtlcm1pdA==');
	// 为了避免大文件影响用户体验，建议加loading
	xhr.onload = () => {
		if (xhr.status === 200) {
			// 获取文件blob数据并保存
			saveAs(xhr.response, fileName);
		}
	};
	xhr.send();
}
function saveAs(data: any, name: any) {
	const urlObject = window.URL || window.webkitURL || window;
	const export_blob = new Blob([data]);
	//createElementNS() 方法可创建带有指定命名空间的元素节点。
	//此方法可返回一个 Element 对象。
	const save_link: any = document.createElementNS('http://www.w3.org/1999/xhtml', 'a');
	save_link.href = urlObject.createObjectURL(export_blob);
	save_link.download = name;
	save_link.click();
}

defineExpose({
	initInfo,
});
</script>

<style lang="scss" scoped>
.file-detail-box {
	position: relative;
	height: 100%;
	display: flex;
	flex-direction: column;
	.file-detail-header {
		height: 40px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 10px;
	}
	.file-detail-back {
		display: flex;
		align-items: center;
		font-size: 14px;
		color: #606266;
		&:hover {
			color: var(--el-color-primary);
		}
	}
	.file-detail-content {
		flex: 1;
		height: calc(100% - 90px);
		margin-top: 10px;
		background: #fff;
		border-radius: 5px;
	}
	.file-detail-bot {
		height: calc(100% - 125px);
	}
	.file-detail-infos {
		background: #fff;
		padding: 20px 20px 0 20px;
		.result-item {
			padding: 12px 100px 12px 16px;
			display: flex;
			justify-content: space-between;
			border-radius: 5px;
			background: #f3f3f3;
			.item-info {
				.item-file-message {
					padding: 5px 0;
				}
				.item-cover {
					position: relative;
					cursor: pointer;
					width: 80px;
					height: 80px;
					border-radius: 4px;
					overflow: hidden;
					background: #f0f0f0;
					margin-right: 20px;
					.el-image {
						width: 100%;
						height: 100%;
					}
					.file-type-label {
						position: absolute;
						bottom: 0;
						width: 100%;
						background: rgba(0, 0, 0, 0.66);
						color: #fff;
						text-align: center;
					}
				}
				.text-info {
					display: flex;
					align-items: center;
					.text-label {
						color: rgba(0, 0, 0, 0.4);
					}
					.text-value {
						color: rgba(0, 0, 0, 0.9);
					}
				}
			}
		}
	}
}
</style>
