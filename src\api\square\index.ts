import request from "/@/utils/request";

// 查询资源列表
export function querySquareList(params?: Object) {
	return request({
		url: "/datacenter/resource/square/page",
		method: "GET",
		params,
	});
}


export function queryResourceInfo(params?: Object) {
	return request({
		url: '/datacenter/personal/center/myCount',
		method: 'get',
		params,
	});
}
// 查询资源详情
export function getResourceInfo(params: Object) {
	return request({
		url: '/datacenter/resource/square/info',
		method: 'get',
		params,
	});
}
// 获取默认字段以及值
export function getResourceDefaultColumn(params: Object) {
	return request({
		url: `/datacenter/resource/square${params.token ? '/shareLink' : ''}/defaultColumn/value`,
		method: 'get',
		params,
	});
}
// 点赞
export function praise(params: Object) {
	return request({
		url: '/datacenter/resource/action/praise',
		method: 'post',
		data: params,
	});
}
// 取消点赞
export function unPraise(params: Object) {
	return request({
		url: '/datacenter/resource/action/unPraise',
		method: 'post',
		data: params,
	});
}
// 收藏
export function collect(params: Object) {
	return request({
		url: '/datacenter/resource/action/collect',
		method: 'post',
		data: params,
	});
}
// 取消收藏
export function unCollect(params: Object) {
	return request({
		url: '/datacenter/resource/action/unCollect',
		method: 'post',
		data: params,
	});
}
// 分享
export function share(params: Object) {
	return request({
		url: '/datacenter/resource/action/share',
		method: 'post',
		data: params,
	});
}
// 生成分享链接
export function shareUrl(params: Object) {
	return request({
		url: '/datacenter/resource/action/shareUrl',
		method: 'post',
		data: params,
	});
}
// 相关操作统计
export function summary(params: Object) {
	return request({
		url: '/datacenter/resource/action/summary',
		method: 'get',
		params,
	});
}
// 获取评论列表
export function getCommentList(params: Object) {
	return request({
		url: '/datacenter/resource/action/commentList',
		method: 'get',
		params,
	});
}
// 发送评论
export function addComment(params: Object) {
	return request({
		url: '/datacenter/resource/action/comment',
		method: 'post',
		data: params,
	});
}
// 获取点赞和收藏的状态
export function getFlag(params: Object) {
	return request({
		url: '/datacenter/resource/action/flag',
		method: 'get',
		params,
	});
}

// 资源详情
// 获取所有的菜单组
export function getResourceListColumnGroup(params: Object) {
	return request({
		url: `/datacenter/resource/square${params.token ? '/shareLink' : ''}/listColumnGroup`,
		method: 'get',
		params,
	});
}

// 按照组分段获取信息
export function getResourceInfoByGroupId(id: String, params: Object) {
	return request({
		url: `/datacenter/resource/square${params.token ? '/shareLink' : ''}/` + id,
		method: 'get',
		params,
	});
}
// 通过分享的链接访问
export function getResourceInfoByShareLink(params: Object) {
	return request({
		url: '/datacenter/resource/square/shareLink',
		method: 'get',
		params,
	});
}


// 附件信息
// 获取文件列表
export function getMaterialPage(params: Object) {
	return request({
		url: '/datacenter/learning/material/page',
		method: 'get',
		params,
	});
}

// 附件信息 带权限
// 获取文件列表
export function getMaterialPermissionPage(params: Object) {
	return request({
		url: `/datacenter/resource/square/${params.token ? 'shareLink' : ''}/material/page`,
		method: 'get',
		params,
	});
}


// 获取文件详情
export function getMaterialInfo(id: String, params: Object) {
	return request({
		url: params.token ? '/datacenter/resource/square/shareLink/material/' + id : '/datacenter/learning/material/' + id,
		method: 'get',
		params
	});
}
// 下载文件
export function downLoadMaterial(ids: Array<any>) {
	return request({
		url: '/datacenter/learning/material/download',
		method: 'get',
		params: ids,
	});
}
// 记录下载次数
export function recordDownLoadMaterial(params: Object) {
	return request({
		url: '/datacenter/resource/action/download',
		method: 'post',
		data: params,
	});
}
