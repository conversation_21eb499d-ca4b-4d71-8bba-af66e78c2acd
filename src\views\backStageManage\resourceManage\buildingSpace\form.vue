<template>
  <el-dialog :title="state.dialogTitle" v-model="state.visible" :width="600" :close-on-click-modal="false"
    :close-on-press-escape="false" :before-close="onClose" append-to-body class="building-form-dialog">
    <el-form ref="formRef" :model="state.form" :rules="dataRules" label-width="100px" v-loading="state.loading">
      <el-form-item label="建筑封面" prop="cover">
        <ImageUpload class="custom-upload" v-model:imageUrl="state.form.cover" borderRadius="6px" width="120px"
          height="90px" uploadFileUrl="/datacenter/learning/material/cover">
          <template #empty>
            <el-icon>
              <Plus />
            </el-icon>
            <span>请上传封面</span>
          </template>
        </ImageUpload>
        <div class="upload-tip">建议尺寸：400x300px，支持jpg、png格式</div>
      </el-form-item>
      <el-form-item label="建筑名称" prop="buildingName">
        <el-input v-model="state.form.buildingName" placeholder="请输入建筑名称" maxlength="50" show-word-limit />
      </el-form-item>
      <el-form-item label="揽胜图坐标" prop="coordinates">
        <el-input v-model="state.form.coordinates" placeholder="坐标格式：x,y（如：100.5,200.8）" />
      </el-form-item>
      <el-form-item label="建筑类型" prop="buildingType" v-if="!state.parentRow">
        <el-select v-model="state.form.buildingType" placeholder="请选择建筑类型" style="width: 100%"
          :disabled="state.dialogType == 'DoEdit'">
          <el-option v-for="type in state.buildingTypes" :key="type.value" :label="type.label" :value="type.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="标签" prop="labels">
        <el-input v-model="state.form.labels" placeholder="请输入标签，多个标签用逗号分隔" />
      </el-form-item>

      <el-form-item label="排序号" prop="sortOrder">
        <el-input-number v-model="state.form.sortOrder" :min="0" :max="9999" controls-position="right"
          style="width: 100%" />
      </el-form-item>

      <el-form-item label="建筑简介" prop="description">
        <el-input type="textarea" v-model="state.form.description" :rows="4" placeholder="请输入建筑简介" maxlength="500"
          show-word-limit />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" @click="onSubmit" :loading="state.submitLoading">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="BuildingDialog">
import { ref, reactive, defineAsyncComponent } from 'vue';
import { addBuilding, updateBuilding, getBuildingType, getBuildingInfo } from '/@/api/backStageManage/building';
import { useMessage } from '/@/hooks/message';
import { Plus } from '@element-plus/icons-vue';

const ImageUpload = defineAsyncComponent(() => import("/@/components/Upload/Image.vue"));


// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);

// 定义变量内容
const formRef = ref();

// 定义需要的数据
const state = reactive({
  visible: false,
  loading: false,
  dialogTitle: '',
  dialogType: '',
  submitLoading: false,
  parentRow: null as any,
  buildingTypes: [] as any[], // 建筑类型列表
  form: {
    buildingId: '',
    buildingType: '',
    buildingName: '',
    cover: '',
    coordinates: '',
    labels: '',
    description: '',
    sortOrder: 0,
    parentId: '',
  },
});

// 表单校验规则
const dataRules = reactive({
  buildingType: [
    { required: true, message: '请选择建筑类型', trigger: 'change' }
  ],
  buildingName: [
    { required: true, message: '请输入建筑名称', trigger: 'blur' }
  ],
  coordinates: [
    { required: true, message: '请输入揽胜图坐标', trigger: 'blur' },
    {
      pattern: /^(\d+\.?\d*),(\d+\.?\d*)$/,
      message: '坐标格式不正确，请输入格式：x,y（支持小数）',
      trigger: 'blur'
    }
  ],
});

// 获取建筑类型列表
const getBuildingTypeList = async () => {
  try {
    const res = await getBuildingType();
    state.buildingTypes = res.data || [];
  } catch (err) {
    useMessage().error('获取建筑类型失败');
  }
};

// 打开弹窗
const openDialog = async (type: string, row?: any) => {
  state.visible = true;
  state.dialogType = type;
  state.parentRow = null;

  // 获取建筑类型列表
  getBuildingTypeList();

  // 重置表单
  Object.assign(state.form, {
    buildingId: '',
    buildingType: '',
    buildingName: '',
    cover: '',
    coordinates: '',
    labels: '',
    description: '',
    sortOrder: 0,
    parentId: '',
  });

  if (type === 'DoEdit') {
    let res = await getBuildingInfo(row.id);
    // 编辑模式
    Object.assign(state.form, res.data);
    state.dialogTitle = '编辑建筑';
  } else if (type === 'DoAdd') {
    state.dialogTitle = row ? '新增建筑空间' : '新增建筑类型';
    if (row && row.buildingType) {
      // 在建筑类型下新增建筑空间
      state.parentRow = row;
      state.form.parentId = row.id;
      state.form.buildingType = row.id; // 建筑空间不需要类型
    }
  }
};



// 提交表单
const onSubmit = async () => {
  const valid = await formRef.value.validate().catch(() => false);
  if (!valid) return;

  try {
    state.submitLoading = true;

    // 处理坐标格式
    if (state.form.coordinates && !state.form.coordinates.includes(',')) {
      useMessage().error('坐标格式不正确，请输入格式：x,y');
      return;
    }

    // 准备提交数据
    const submitData: any = { ...state.form };

    // 新增情况下，如果buildingId为空值，则移除该参数
    if (!submitData.buildingId || submitData.buildingId === '') {
      delete submitData.buildingId;
      await addBuilding(submitData);
    } else {
      let data = {
        "buildingId": submitData.buildingId,
        "buildingType": submitData.buildingType,
        "buildingName": submitData.buildingName,
        "cover": submitData.cover,
        "coordinates": submitData.coordinates,
        "labels": submitData.labels,
        "description": submitData.description,
        "sortOrder": submitData.sortOrder,
      }
      await updateBuilding(data);
    }


    if (state.dialogType === 'DoAdd') {
      useMessage().success('新增成功');
    } else {
      useMessage().success('修改成功');
    }

    onClose();
    emit('refresh');
  } catch (err: any) {
    useMessage().error(err.msg || '操作失败');
  } finally {
    state.submitLoading = false;
  }
};

// 关闭弹窗
const onClose = () => {
  state.visible = false;
  formRef.value?.resetFields();
};

// 暴露方法
defineExpose({
  openDialog,
});
</script>

<style scoped lang="scss">
.building-form-dialog {

  .upload-tip,
  .form-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  // 自定义上传组件样式
  :deep(.custom-upload) {
    .el-upload__tip {
      margin-top: 0 !important;
    }
  }
}
</style>
