<template>
  <div class="mt-content">
    <div class="mtcb-title">{{ data.title }}</div>
    <!-- <v-chart v-if="chartOption" :option="chartOption" :autoresize="true" class="chart-box"></v-chart> -->
    <div ref="mtChart" class="chart-box"></div>
  </div>
</template>

<script lang="ts" name="CP0002" setup>
import * as echarts from "echarts";
const props = defineProps({
  data: {
    type: Object,
    default: {
      title: "",
      subtext: "",
      value: [],
      totalNum: 0,
      colors: [],
    },
  },
});
const mtChart: any = ref(null);
onMounted(() => {
  initChart();
});
const initChart = () => {
  let myChart = echarts.init(mtChart.value);
  mtChart.value.removeAttribute("_echarts_instance_");
  let option = {
    backgroundColor: "transparent",
    tooltip: {
      trigger: "item",
      formatter: "{b}: {c} ({d}%)",
    },
    title: {
      show: true,
      text: props.data?.totalNum || "",
      subtext: props.data?.subtext || "",
      textStyle: {
        color: "#FD4949",
        width: 100,
        height: 30,
        lineHeight: 30,
        fontSize: 30,
      },
      subtextStyle: {
        color: "#FD4949",
        width: 100,
        height: 16,
        lineHeight: 16,
        fontSize: 16,
      },
      left: "34%",
      top: "37%",
      textAlign: "center",
      triggerEvent: true,
    },
    legend: {
      orient: "vertical",
      itemWidth: 14,
      itemHeight: 14,
      left: "54%",
      bottom: "center",
      data: (props.data?.value || []).map((k: any) => String(k.name)),
      formatter: (name: string) => {
        let obj: any = props.data?.value.find((item: any) => item.name === name);
        let value = obj?.value || 0;
        let percent =
          props.data?.totalNum === 0
            ? "0%"
            : ((value / props.data?.totalNum) * 100).toFixed(1) + "%";
        return `{a|${echarts.format.truncateText(
          name,
          135,
          "10px Microsoft Yahei",
          "…"
        )}}{b|${percent}}`;
      },
      textStyle: {
        rich: {
          a: {
            width: 140,
            fontSize: "12",
            color: "#000",
            fontWeight: "normal",
            align: "left",
          },
          b: {
            width: 40,
            fontSize: "12",
            color: "#000",
            fontWeight: "normal",
            align: "right",
          },
        },
      },
      itemGap: 13,
      type: "scroll",
      pageIconSize: "10",
      pageIconColor: "#666",
      pageIconInactiveColor: "#999",
      pageTextStyle: {
        color: "#666",
      },
    },
    series: [
      {
        type: "pie",
        radius: ["42%", "65%"],
        color: props.data?.colors || [],
        label: {
          show: false,
        },
        left: "-30%",
        data: props.data?.value || [],
        emptyCircleStyle: {
          borderCap: "round",
        },
      },
    ],
  };
  myChart.setOption(option);
};
</script>
<style lang="scss" scoped>
.mt-content {
  height: 100%;
  padding: 10px;
  .mtcb-title {
    width: 100%;
    height: 30px;
    font-family: PingFang SC, PingFang SC;
    font-weight: bold;
    font-size: 15px;
    color: #000000;
    letter-spacing: 0.1em;
    text-align: center;
  }
  .chart-box {
    width: 100%;
    height: calc(100% - 30px);
  }
}
</style>
