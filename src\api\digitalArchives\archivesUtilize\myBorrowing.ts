import request from "/@/utils/request";

/**
 * 我的借阅 -- 分页
 *
 */
export function getListArchiveBorrow(params: any) {
  return request({
    url: "/datacenter/archive/borrow/page",
    method: "get",
    params: params,
  });
}
/**
 * 我的借阅 -- 查看详情
 *
 */
export function getArchiveBorrowInfo(params: any) {
  return request({
    url: "/datacenter/archive/borrow/info",
    method: "get",
    params: params,
  });
}
/**
 * 我的借阅 -- 查看详情
 *
 */
export function addArchiveBorrow(data: any) {
  return request({
    url: "/datacenter/archive/borrow/add",
    method: "post",
    data: data,
  });
}
/**
 * 查看档案详情
 *
 */
export function getArchiveBorrowArchiveInfo(params: any) {
  return request({
    url: "/datacenter/archive/borrow/archive/info",
    method: "get",
    params: params,
  });
}
/**
 * 查看流程详情
 *
 */
export function formatStartNodeShow(data: any) {
  return request({
    url: "/task/process-instance/formatStartNodeShow",
    method: "post",
    data: data,
  });
}

/**
 * 取消申请
 *
 */
export function applyCancel(data: any) {
  return request({
    url: "/datacenter/archive/borrow/cancel",
    method: "post",
    data: data,
  });
}