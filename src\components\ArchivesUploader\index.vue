<template>
  <uploader class="uploader-component" ref="uploaderRef" :autoStart="false" :options="options"
    :file-status-text="statusText" @file-complete="onFileComplete" @file-success="onFileSuccess"
    @files-added="onFilesAdded" @fileProgress="onFileProgress" @file-error="onFileError">
    <div></div>
  </uploader>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { useArchivesUploaderStore } from '/@/stores/archivesUploader';
import { Session } from '/@/utils/storage';

const uploaderRef = ref({} as any)
const archivesUploaderStore = useArchivesUploaderStore()
const options = {
  // target: import.meta.env.VITE_API_URL + `/admin/sys-file/upload`, // 系统管理里的上传api
  target: import.meta.env.VITE_API_URL + `/archive/wb/resource/uploadFile`, // 上传api
  testChunks: false,
  chunkSize: '2048000000000', // 大于1024G
  headers: {
    Authorization: `Bearer ${Session.getToken()}`,
  },
  simultaneousUploads: 3, // 最大并发上传数
  allowDuplicateUploads: true,
  processParams: (params: any, file: any) => {
    let fileObj: any = archivesUploaderStore.uploadingList.find((item: any) => item.id == file.id)
    let fileParams = {
      filePath: params.relativePath,
      directoryId: fileObj?.directoryId,
      classifyId: fileObj?.classifyId,
    }
    return fileParams
  }
}

onMounted(() => {
  nextTick(() => {
    archivesUploaderStore.setArchivesUploader(uploaderRef.value.uploader)
  })
})

const statusText = {
  success: '上传成功',
  error: '上传失败',
  uploading: '上传中',
  paused: '暂停',
  waiting: '等待中'
}

const onFilesAdded = (files: any, fileList: any) => {
  if (files.length > 0) {
    let newSelectedList = files.map((item: any) => {
      return {
        id: item.id,
        name: item.name,
        relativePath: item.relativePath, // 文件夹路径
        file: item,
        isFolder: item.isFolder,
        status: '上传中',
        progress: 0,
        directoryId: '',
        classifyId: '',
        address: '', // 位置(分类-目标文件夹-文件路径拼接)
      }
    })
    archivesUploaderStore.setFileList('selectedList', newSelectedList)
    archivesUploaderStore.setShowSelectTarget(true)
  }
}

// 单个文件上传成功
const onFileSuccess = (rootFile: any, file: any, response: any) => {
  archivesUploaderStore.deleteFile('uploadingList', file)
}

// 上传失败
const onFileError = (rootFile: any, file: any, response: any) => {
  let failedList = archivesUploaderStore.uploadingList.filter((item: any) => item.id === file.id)
  let msg = response ? JSON.parse(response)?.msg : '上传失败'
  failedList[0] ? failedList[0].msg = msg : null
  archivesUploaderStore.deleteFile('uploadingList', file)
  archivesUploaderStore.addFileList('failedList', failedList)
}

// 上传成功（选择文件夹时为文件夹上传成功，否则是文件上传成功）
const onFileComplete = (file: any) => {}

// progress 上传进度变化
const onFileProgress = (rootFile: any, file: any) => {
  // archivesUploaderStore.uploadingList.forEach((item: any) => {
  //   if (item.id === file.id) {
  //     item.progress = (file.progress() * 100).toFixed(1) + '%'
  //   }
  // })
}
</script>
