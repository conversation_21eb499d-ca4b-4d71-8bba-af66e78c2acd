import request from '/@/utils/request';
import { AxiosPromise } from 'axios';



// 文旅提交修改
export function submitData1(data:any): AxiosPromise {
  return request({
    url: '/wlHome/updateWlTicketConfig', 
    method: 'post',
    params: data
  });
}
// 文旅查询系数数据
export function getData1(): AxiosPromise {
  return request({
    url: '/wlHome/getWlTicketConfig',
    method: 'get',
  });
}
// 文旅查询数值数据
export function getData11(): AxiosPromise {
  return request({
    url: '/wlTodayReservation/getToday',
    method: 'get',
  });
}
// 文保提交修改
export function submitData2(data:any): AxiosPromise {
  return request({
    url: '/wbHome/updateWarnFourConfig',
    method: 'post',
    params: data
  });
}
// 文保查询数据
export function getData2(): AxiosPromise {
  return request({
    url: '/wbHome/getMonitor',
    method: 'get',
  });
}

// 文旅运营入口提交修改
export function submitData3(data:any): AxiosPromise {
  return request({
    url: '/wlHome/updateConfig',
    method: 'post',
    params: data
  });
}
// 文旅运营入口查询数据
export function getData3(): AxiosPromise {
  return request({
    url: '/wlHome/getRealTimeOne',
    method: 'get',
  });
}