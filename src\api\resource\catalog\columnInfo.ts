import request from "/@/utils/request";

// 新增字段目录
export function addColumnGroup(data?: Object) {
  return request({
    url: "/datacenter/columnInfo/addColumnGroup",
    method: "post",
    data: data,
  });
}
// 查询字段目录
export function listColumnGroup(data?: Object) {
  return request({
    url: "/datacenter/columnInfo/listColumnGroup",
    method: "post",
    data: data,
  });
}
// 编辑字段目录
export function editColumnGroup(data?: Object) {
  return request({
    url: "/datacenter/columnInfo/editColumnGroup",
    method: "post",
    data: data,
  });
}
// 删除字段目录
export function removeColumnGroup(data?: Object) {
  return request({
    url: "datacenter/columnInfo/removeColumnGroup",
    method: "post",
    data: data,
  });
}
// 新增字段分组
export function addFormColumnGroup(data?: Object) {
  return request({
    url: "/datacenter/columnInfo/addColumnSubGroup",
    method: "post",
    data: data,
  });
}
// 更新字段分组
export function updateFormColumnGroup(data?: Object) {
  return request({
    url: "/datacenter/columnInfo/editColumnSubGroup",
    method: "post",
    data: data,
  });
}
// 删除字段分组
export function delFormColumnGroup(data?: Object) {
  return request({
    url: "/datacenter/columnInfo/removeColumnSubGroup",
    method: "post",
    data: data,
  });
}
// 新增字段
export function addColumnInfo(data?: Object) {
  return request({
    url: "/datacenter/columnInfo/addColumnInfo",
    method: "post",
    data: data,
  });
}
// 编辑字段
export function editColumnInfo(data?: Object) {
  return request({
    url: "/datacenter/columnInfo/editColumnInfo",
    method: "post",
    data: data,
  });
}
// 查询字段列表
export function pageColumnInfo(data?: Object) {
  return request({
    url: "/datacenter/columnInfo/pageColumnInfo",
    method: "post",
    data: data,
  });
}
// 查询字段列表
export function baseInfoColumns(data?: Object) {
  return request({
    url: "/datacenter/columnInfo/baseInfoColumns",
    method: "post",
    data: data,
  });
}
// 查询字段类型rules
export function listColumnType(data?: Object) {
  return request({
    url: "/datacenter/columnInfo/listColumnType",
    method: "post",
    data: data,
  });
}