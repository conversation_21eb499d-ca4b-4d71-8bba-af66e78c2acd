import request from "/@/utils/request"

// 预计未来1h入园人数
export function getDoorPredictOneHourData() {
  return request({
    url: '/ai/wlTicketCheckDoorPredictHour/getDoorPredictOneHour',
    method: 'get',
  })
}
// 城墙整体未来48H客流趋势
export function getAll48Data() {
  return request({
    url: '/ai/wlTicketCheckDoorPredictHour/getAllDoorPredictHourList',
    method: 'get',
  })
}

// 各门点未来48h客流趋势
export function fetchList(query?: Object) {
  return request({
    url: '/ai/wlTicketCheckDoorPredictHour/getDoorPredictHourList',
    method: 'get',
    params: query
  })
}

// 获取门点选项字典
export function getDoorData() {
  return request({
    url: '/ai/wlTicketCheckDoor/list',
    method: 'get',
  })
}

