<template>
  <div class="home-container">
    <SearchComponent :catalogList="catalogList" />
    <div class="home-content">
      <TopicLibraryComponent :catalogList="catalogList" />
      <HotResourcesComponent @goResourceDetail="goResourceDetail" />
      <DepartmentResources @goResourceDetail="goResourceDetail" />
      <LatestUploads @goResourceDetail="goResourceDetail" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { getCountByCatalog } from "/@/api/squareNew/index";
import { ElMessage } from "element-plus";
import { useRouter } from "vue-router";
const router = useRouter();
const catalogList = ref([]);

const SearchComponent = defineAsyncComponent(() => import("./components/search.vue"));

const TopicLibraryComponent = defineAsyncComponent(
  () => import("./components/topic-library.vue")
);
const HotResourcesComponent = defineAsyncComponent(
  () => import("./components/hot-resources.vue")
);
const DepartmentResources = defineAsyncComponent(
  () => import("./components/department-resources.vue")
);
const LatestUploads = defineAsyncComponent(
  () => import("./components/latest-uploads.vue")
);

// 跳转详情页面
const goResourceDetail = (record: any) => {
  if (!record.viewed) {
    return ElMessage.warning("暂无查看权限");
  }
  if (!record.business_id || !record.assets_type || !record.table_name) {
    return ElMessage.warning("该资源暂时无法查看（缺少参数）");
  }
  router.push(`/squareNew/resource/detail/${record.business_id}?type=${record.assets_type}&tabName=${record.table_name}`);
};

onMounted(() => {
  queryCountByCatalog();
});

// 专题库统计
const queryCountByCatalog = () => {
  getCountByCatalog().then((res) => {
    catalogList.value = (res.data || []).map((item: any)=> {
      return {
        ...item,
        label: item.itemName,
        value: item.itemNo,
      };
    });
  });
};
</script>

<style lang="scss" scoped>
.home-container {
  .home-content {
    margin: 0 210px;
    min-width: 1400px;
  }
}
</style>
