import request from "/@/utils/request";

// 热搜词
export function getHotKeyWord(params?: Object) {
	return request({
		url: "/datacenter/hotKeyWord/list",
		method: "GET",
		params,
	});
}

// 查询首页统计项
export function getCardCount(data?: Object) {
	return request({
		url: "/datacenter/resource/square/cardCount",
		method: "POST",
		data,
	});
}

// 专题库统计
export function getCountByCatalog(params?: Object) {
	return request({
		url: "/datacenter/resource/square/countByCatalog",
		method: "GET",
		params,
	});
}

// 热门资源
export function getHotResource(params?: Object) {
	return request({
		url: "/datacenter/resource/square/hotResource",
		method: "GET",
		params,
	});
}

// 部门资源
export function getDeptResource(params?: Object) {
	return request({
		url: "/datacenter/resource/square/deptResource",
		method: "GET",
		params,
	});
}

// 最新上传
export function getNewResource(params?: Object) {
	return request({
		url: "/datacenter/resource/square/newResource",
		method: "GET",
		params,
	});
}

// 关键字检索
export function getKeywordSearch(data?: Object) {
	return request({
		url: "/datacenter/resource/square/keywordSearch",
		method: "POST",
		data,
	});
}


// 关键字检索按照目录统计
export function getSearchCountByCatalog(data?: Object) {
	return request({
		url: "/datacenter/resource/square/searchCountByCatalog",
		method: "POST",
		data,
	});
}


// 关键字检索按照部门统计
export function getSearchCountByDept(data?: Object) {
	return request({
		url: "/datacenter/resource/square/searchCountByDept",
		method: "POST",
		data,
	});
}

// 文件分享token生成
export function shareUrl(data?: Object) {
	return request({
		url: "/datacenter/learning/material/shareUrl",
		method: "POST",
		data,
	});
}

// 分享链接访问
export function getShareLink(params?: Object) {
	return request({
		url: "/datacenter/learning/material/shareLink",
		method: "GET",
		params,
	});
}

// 记录分享操作
export function share(params: Object) {
	return request({
		url: '/datacenter/resource/action/share',
		method: 'post',
		data: params,
	});
}