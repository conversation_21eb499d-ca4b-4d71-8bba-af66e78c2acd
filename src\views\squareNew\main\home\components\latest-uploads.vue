<template>
  <div class="latest-uploads-container">
    <div class="title-box">最新上传</div>
    <div class="uploads-grid">
      <div
        class="upload-card"
        v-for="(item, i) in lasterList"
        :key="i"
        @click="handleClick(item)"
      >
        <div class="img-box">
          <div class="resource-image">
            <img :src="item.assets_cover || noCoverImg" alt="" :class="{ 'no-cover': !item.assets_cover }" @error="onImgError" />
          </div>
          <div class="icon-box" v-if="item?.collected">
            <img src="/@/assets/img/squareNew/collectIcon1.png" alt="" />
          </div>
        </div>
        <div class="card-info">
          <div class="resource-tag">
            <div class="tag" v-for="(tag, index) in getTags(item)" :key="'tag' + index">{{ tag }}</div>
          </div>
          <div class="resource-title">{{ item.assets_name }}</div>
          <div class="resource-meta">
            <div class="resource-size">{{ item?.third_catalog_name?.split(",").join(' > ') }}</div>
            <div class="view-count">
              <i src="" alt="" />
              <span>{{ item.view_count || 0 }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { getNewResource } from "/@/api/squareNew/index";
import noCoverImg from "/@/assets/img/squareNew/no_cover.png";

const lasterList = ref<any>([]);
const emit = defineEmits(['goResourceDetail']);

const handleClick = (record: any) => {
  emit('goResourceDetail', record)
};

const queryNewResource = () => {
  getNewResource().then((res) => {
    lasterList.value = res?.data || [];
  });
};
onMounted(() => {
  queryNewResource();
});
const onImgError = (e: any) => {
  e.target.src = noCoverImg;
  e.target.classList.add("no-cover");
};
// 处理标签
const getTags = (item: any) => {
  let tags = []
  if (item.assets_tag) {
    tags = item.assets_tag.split(/[,，]/);
    tags = tags.slice(0, 5)
  }
  return tags;
};
</script>

<style scoped lang="scss">
.latest-uploads-container {
  width: 100%;
  padding: 0; // Adjust padding as needed for the overall section
  box-sizing: border-box;
  // background-color: #f7f2f2; // Match the background color of the main-container
  margin-bottom: 60px;

  .title-box {
    font-family: Source Han Serif CN;
    font-weight: 700;
    font-size: 50px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
    color: #141414;
    margin-bottom: 45px;
    width: 100%; // Align with the grid
    text-align: left; // Align title to the left
  }

  .uploads-grid {
    display: grid;
    grid-template-columns: repeat(4, calc(25% - 22.5px)); // Four fixed-width columns
    // gap: 20px; // Gap between cards
    grid-column-gap: 30px;
    grid-row-gap: 20px;
    width: fit-content; // Make the grid container fit its content
    width: 100%;

    .upload-card {
      cursor: pointer;
      width: 100%;
      border: 0.892px solid rgba(0, 0, 0, 0.10);
      // height: 365px;
      background: #fff;
      overflow: hidden;
      position: relative;
      padding: 20px;
      transition: all 0.3s;
      &:hover{
        box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.15);
      }

      .img-box {
        height: 178px;
        position: relative;
        .resource-image {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
          .no-cover {
            width: 200px;
            height: auto;
            object-fit: contain;
          }
        }
        .icon-box {
          position: absolute;
          right: 9px;
          bottom: 7px;
          display: flex;
          > img {
            width: 28px;
            height: 28px;
            margin-right: 10px;
            &:last-child {
              margin-right: 0;
            }
          }
        }
      }

      .card-info {
        background: rgba(255, 255, 255, 0.9);
        margin-top: 12px;
        display: flex;
        flex-direction: column;

        .resource-tag {
          display: flex;
          align-items: center;
          margin-bottom: 12px;
          height: 24px;

          .tag {
            background-color: #DCB171;
            color: white;
            padding: 2px 4px;
            border-radius: 2px;
            align-self: flex-start;
            font-family: Source Han Serif CN;
            font-weight: 500;
            font-size: 13px;
            height: 24px;
            text-align: center;
            box-sizing: border-box;
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            &+.tag {
              margin-left: 8px; 
            }
          }
        }

        .resource-title {
          font-family: Source Han Sans CN;
          font-weight: 700;
          font-size: 16px;
          line-height: 100%;
          letter-spacing: 0%;
          vertical-align: middle;
          color: #862626;
          margin-bottom: 8px;
        }

        .resource-meta {
          font-size: 12px;
          color: #666;
          display: flex;
          font-weight: 400;
          font-size: 14px;
          display: flex;
          align-items: center;

          .resource-size {
            flex-grow: 1;
            color: #262626B2;
            font-family: Source Han Sans CN;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }

          .view-count {
            display: flex;
            align-items: center;
            flex-grow: 1;
            justify-content: flex-end;

            i {
              width: 24px;
              height: 24px;
              display: flex;
              align-items: center;
              margin-right: 4px;
              &::before {
                content: "";
                display: inline-block;
                width: 22px;
                height: 15px;
                background: url("/@/assets/img/squareNew/eyePrimaryIcon.png") no-repeat
                  center;
                background-size: contain;
              }
            }
            > span {
              font-family: Source Han Sans CN;
              font-weight: 500;
              font-size: 16px;
              line-height: 22.54px;
              letter-spacing: 0%;
              color: rgba(134, 38, 38, 1);
            }
          }
        }
      }
    }
  }
}
</style>
