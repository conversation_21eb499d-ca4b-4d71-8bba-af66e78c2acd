<template>
  <div class="search-container">
    <div class="search-control">
      <div class="search-input-area">
        <div class="dropdown">
          <el-select v-model="searchType" placeholder="Select" style="width: 240px">
            <el-option
              v-for="item in searchTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="search-input-box">
          <el-input
            type="text"
            placeholder="请输入关键词，点击搜索按钮进行检索"
            class="search-input"
            v-model="searchText"
            @keyup.enter="queryKeywordSearch"
          />
          <el-button class="search-button" @click="queryKeywordSearch">
            <i class="search-icon"></i>
          </el-button>
        </div>
        <!-- <button class="ai-search-button">
          <i class="ai-icon"></i>AI检索
        </button> -->
      </div>
      <div class="hot-keywords">
        <span>热门搜索：</span>
        <span
          class="keyword-item"
          v-for="item in hotKeyWord"
          :key="item"
          @click="searchText = item"
          >{{ item }}</span
        >
      </div>
    </div>
    <div class="resource-info">
      <div v-for="item in resourceTotals" class="info-item" :key="item.itemName">
        <div class="count-box">{{ item.itemCount }}</div>
        <div class="label-box">{{ item.itemName }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from "vue";
import { getHotKeyWord, getCardCount } from "/@/api/squareNew/index";
import { useRouter } from "vue-router";
import { useSquareStore } from "/@/stores/square";

const squareStore = useSquareStore();

const props = defineProps({
  catalogList: {
    type: Array,
    default: () => [],
  },
});
const resourceTotals = ref<any>([]);
const searchText = ref("");
const router = useRouter();

const searchType = ref("");
const hotKeyWord = ref([]);
const searchTypeOptions = computed(() => {
  return [{ label: "全部", value: "" }].concat(props.catalogList);
});
const queryCardCount = () => {
  getCardCount({}).then((res) => {
    if (res.ok) {
      resourceTotals.value = res.data || [];
    }
  });
};

const queryHotKeyWord = () => {
  getHotKeyWord({}).then((res) => {
    hotKeyWord.value = res?.data || [];
  });
};

// 搜索关键词
const queryKeywordSearch = () => {
  router.push(
    `/squareNew/resource?keyword=${searchText.value}&topicId=${searchType.value}`
  );
};

onMounted(() => {
  queryHotKeyWord();
  queryCardCount();
});
</script>

<style scoped lang="scss">
.search-container {
  width: 100%;
  min-width: 1820px;
  padding: 20px 0;
  background: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-image: url("/@/assets/img/squareNew/hotResourceBg.png");
  background-size: 100% 100%;
  height: 500px;
  justify-content: center;
  position: relative;

  .search-control {
    margin-top: -20px;
    .search-input-area {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 22px;
      height: 54px;

      .dropdown {
        display: flex;
        align-items: center;
        border-right: none;
        border-radius: 5px;
        width: 120px;
        height: 100%;

        :deep(.el-select) {
          height: 100%;
          .select-trigger {
            height: 100%;
            .el-input {
              height: 100%;
              .el-input__wrapper {
                height: 100%;
                border: none;
                background-color: var(--el-color-primary);
                box-shadow: none;
                input::placeholder {
                  font-family: Source Han Sans CN;
                  font-weight: 500;
                  font-size: 16px;
                  line-height: 20px;
                  letter-spacing: 0%;
                  text-align: center;
                  color: #ffffff;
                }
                .el-input__suffix {
                  color: #fff;
                  .el-input__suffix-inner {
                    .el-icon {
                      color: #fff;
                    }
                  }
                }
              }
            }
          }
          .el-input__wrapper.is-focus {
            box-shadow: none !important;
          }
          .el-input.is-focus {
            .el-input__wrapper {
              box-shadow: none !important;
            }
          }
          .el-input__inner {
            color: #fff;
          }
        }
      }

      .search-input-box {
        position: relative;
        height: 100%;
        :deep(.search-input) {
          border: 1px solid #dcdfe6;
          width: 740px;
          font-size: 14px;
          outline: none;
          height: 100%;
          border: 5px;
          height: 100%;
          margin: 0 6px;
          .el-input__wrapper {
            box-shadow: none;
            padding-right: 104px;
          }
          .el-input__wrapper:hover {
            box-shadow: none !important;
          }
        }

        .search-button {
          background-color: var(--el-color-primary); // 红色按钮
          border-radius: 5px;
          cursor: pointer;
          width: 72px;
          border: none;
          position: absolute;
          height: 42px;
          top: 50%;
          right: 12px;
          transform: translateY(-50%);

          .search-icon {
            // 假设是一个字体图标或背景图
            display: inline-block;
            width: 24px;
            height: 24px;
            background-image: url("/@/assets/img/squareNew/searchIcon.png");
            background-size: 100% 100%;
          }
        }
      }

      .ai-search-button {
        background-color: #ba8548;
        border: none;
        border-radius: 5px;
        margin-left: 0px;
        cursor: pointer;
        height: 100%;
        width: 120px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: Source Han Serif CN;
        font-weight: 400;
        font-size: 18px;
        color: #ffffff;

        .ai-icon {
          // 假设是一个字体图标或背景图
          display: inline-block;
          margin-right: 5px;
          background-image: url("/@/assets/img/squareNew/aiIcon.png");
          width: 32px;
          height: 32px;
          background-size: 100% 100%;
          margin-right: 12px;
        }
      }
    }

    .hot-keywords {
      color: #fff;
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 16px;
      line-height: 20px;
      letter-spacing: 0%;

      .keyword-item {
        margin-left: 15px;
        cursor: pointer;
      }
    }
  }

  .resource-info {
    position: absolute;
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;

    .info-item {
      margin-right: 179px;
      &:last-child {
        margin-right: 0;
      }
      .count-box {
        font-family: Source Han Sans CN;
        font-weight: 700;
        font-size: 50px;
        line-height: 100%;
        letter-spacing: 10%;
        text-align: center;
        color: #ffffff;
        margin-bottom: 20px;
      }
      .label-box {
        text-align: center;
        text-wrap: nowrap;

        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        line-height: 20px;
        letter-spacing: 0px;
        color: #ffffffb2;
      }
    }
  }
}
</style>
