<template>
	<div class="w100 item_container" style="z-index: 9">
		<tab3 :editCode="editCode" :editRecord="editRecord" :selectCatalog="props.selectCatalog" @backTo="backTo" />
	</div>
</template>

<script setup lang="ts">
import { fetchList as pageList_resourceCatalog } from '/@/api/resource/catalog/catalog';
const props = defineProps({
	deptData: {
		type: Array,
		default: () => [],
	},
	editCode: {
		type: String,
		default: null,
	},
	editRecord: {
		type: Object,
		default: null,
	},
	selectCatalog: {
		type: Object,
		default: null,
	},
});
const emit = defineEmits(['backTo']);
// 引入组件
const tab3 = defineAsyncComponent(() => import('./tab3.vue'));

const resuorceData = ref<any[]>([]);

const backTo = () => {
	emit('backTo');
};
onMounted(() => {
	getResuorceTreeData();
});

let getResuorceTreeData = async () => {
	pageList_resourceCatalog({}).then((res: any) => {
		for (let i in res.data) {
			res.data[i].disabled = true;
			for (let j in res.data[i].childCatalogs) {
				res.data[i].childCatalogs[j].disabled = true;
			}
		}
		resuorceData.value = res.data;
	});
};
</script>

<style lang="scss" scoped>
.item_container {
  position: absolute;
	height: 100%;
	background: #fff;
	border-radius: 6px;
	box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
	padding: 0;
	width: 100% !important;
	margin: 0 auto;
}
</style>
