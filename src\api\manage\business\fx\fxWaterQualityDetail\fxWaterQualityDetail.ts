import request from "/@/utils/request"

export function fetchList(query?: Object) {
  return request({
    url: '/business/fxWaterQualityDetail/byPage',
    method: 'get',
    params: query
  })
}

export function addObj(obj?: any) {
  return request({
    // url: '/business/fxWaterQualityDetail',
    url: '/business/fxWaterQualityDetail/batchInsert',
    method: 'post',
    data: obj
  })
}

export function getObj(id?: string) {
  return request({
    url: '/business/fxWaterQualityDetail/' + id,
    method: 'get'
  })
}

export function delObjs(ids?: Object) {
  return request({
    url: '/business/fxWaterQualityDetail',
    method: 'delete',
    data: ids
  })
}

export function putObj(obj?: Object) {
  return request({
    url: '/business/fxWaterQualityDetail',
    method: 'put',
    data: obj
  })
}

// ocr识别
export function getOcr(obj?: Object) {
  return request({
    url: '/business/fxWaterQualityDetail',
    method: 'put',
    data: obj
  })
}
