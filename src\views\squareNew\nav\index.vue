<script setup lang="ts">
import logoImg from "/@/assets/square_logo.png";
import { ElMessage, ElMessageBox } from "element-plus";
import { useI18n } from "vue-i18n";
import mittBus from "/@/utils/mitt";
import { useRouter, useRoute } from "vue-router";
import { useUserInfo } from "/@/stores/userInfo";
import other from "/@/utils/other";
import { useSquareStore } from "/@/stores/square";
import { logout } from "/@/api/login";
import { Local, Session } from "/@/utils/storage";

const router = useRouter();
const route = useRoute();
const squareStore = useSquareStore();

const stores = useUserInfo();
const userInfos = ref({});
const onThemeConfigChange = () => {};
const globalTitle = ["武当文化资源广场"];

const { locale, t } = useI18n();
// 下拉菜单点击时
const onHandleCommandClick = (path: string) => {
  if (path === "logOut") {
    ElMessageBox({
      closeOnClickModal: false,
      closeOnPressEscape: false,
      title: t("user.logOutTitle"),
      message: t("user.logOutMessage"),
      showCancelButton: true,
      confirmButtonText: t("user.logOutConfirm"),
      cancelButtonText: t("user.logOutCancel"),
      buttonSize: "default",
      beforeClose: (action, instance, done) => {
        if (action === "confirm") {
          instance.confirmButtonLoading = true;
          instance.confirmButtonText = t("user.logOutExit");
          setTimeout(() => {
            done();
            setTimeout(() => {
              instance.confirmButtonLoading = false;
            }, 300);
          }, 700);
        } else {
          done();
        }
      },
    })
      .then(async () => {
        // 关闭全部的标签页
        mittBus.emit(
          "onCurrentContextmenuClick",
          Object.assign({}, { contextMenuClickId: 3, ...router })
        );
        // 调用后台接口
        await logout();
        // 清除缓存/token等
        Session.clear();
        // 使用 reload 时，不需要调用 resetRoute() 重置路由
        window.location.reload();
      })
      .catch(() => {});
  } else if (path === "personal") {
    // 打开个人页面
    // personalDrawerRef.value.open();
    // squareStore.changeLayout("personalCenter");
    window.open("#/squareNew/personCenter");
  } else {
    router.push(path);
  }
};

onMounted(async () => {
  if (!route.query.token) {
    await useUserInfo().setUserInfos();
    // userInfos.value = stores.userInfos || {};
  }
});
watch(() => useUserInfo().userInfos, (newValue) => {
  userInfos.value = newValue;
});
</script>

<template>
  <div class="layout-logo">
    <div class="logo-box" @click="!route.query.token && router.push('/gatewayOut')">
      <img :src="logoImg" class="logoImg" />
      <div class="layout-tile">
        <span> {{ globalTitle[0] }}</span>
      </div>
    </div>
    <div class="user-box" v-if="!route.query.token">
      <el-dropdown :show-timeout="70" :hide-timeout="50" @command="onHandleCommandClick">
        <span class="layout-navbars-breadcrumb-user-link">
          <img
            v-if="userInfos?.user?.avatar"
            :src="other.adaptationUrl(userInfos?.user?.avatar)"
            class="layout-navbars-breadcrumb-user-link-photo mr5"
            :onerror="() => (userInfos.user.avatar = null)"
          />
          <img
            v-else
            src="/@/assets/img/resourceSetupCenter/systemManagement/avatIcon.png"
            class="mr5"
          />
          {{ userInfos?.user?.name || userInfos?.user?.username }}
          <el-icon class="el-icon--right">
            <ele-ArrowDown />
          </el-icon>
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="personal">{{
              $t("user.dropdown2")
            }}</el-dropdown-item>
            <el-dropdown-item divided command="logOut">{{
              $t("user.dropdown5")
            }}</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<style scoped lang="scss">
.layout-logo {
  width: 100%;
  height: 66px;
  display: flex;
  align-items: center;
  box-shadow: rgb(0 21 41 / 2%) 0px 1px 4px;
  font-size: 16px;
  // cursor: pointer;
  animation: logoAnimation 0.3s ease-in-out;
  flex-shrink: 0;
  border: 1px solid var(--Light-ThNaNpxe-Color-White, #fff);
  background: #fcfcfc;

  .logo-box {
    display: flex;
    align-items: center;
    cursor: pointer;
  }

  .logoImg {
    width: auto;
    height: 60px;
    margin: 0 0 0 20px;
    padding: 4px;
  }

  .layout-tile {
    display: inline-block;
    white-space: nowrap;
    color: #262626;
    font-style: normal;
    font-family: Source Han Sans CN;
    font-weight: 700;
    font-size: 20px;
    letter-spacing: 0px;
    padding-left: 20px;
  }

  &:hover {
    span {
      color: var(--color-primary-light-2);
    }
  }

  .user-box {
    flex-grow: 1;
    text-align: right;
    padding-right: 25px;

    .el-dropdown {
      .layout-navbars-breadcrumb-user-link {
        display: flex;
        align-items: center;

        > img {
          width: 25px;
          height: 25px;
          border-radius: 100%;
        }
      }
    }
  }
}
</style>
