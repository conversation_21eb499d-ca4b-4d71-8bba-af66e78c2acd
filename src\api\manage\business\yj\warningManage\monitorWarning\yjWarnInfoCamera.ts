import request from "/@/utils/request"

export function fetchList(query?: Object) {
  return request({
    url: '/business/yjWarnInfoCamera/byPage',
    // url: '/business/yjWarnInfoFire/byPage',
    method: 'get',
    params: query
  })
}

export function addObj(obj?: Object) {
  return request({
    url: '/business/yjWarnInfoCamera',
    method: 'post',
    data: obj
  })
}

export function getObj(id?: string) {
  return request({
    url: '/business/yjWarnInfoCamera/' + id,
    method: 'get'
  })
}

export function delObjs(ids?: Object) {
  return request({
    url: '/business/yjWarnInfoCamera',
    method: 'delete',
    data: ids
  })
}

export function putObj(obj?: Object) {
  return request({
    url: '/business/yjWarnInfoCamera',
    method: 'put',
    data: obj
  })
}

export function cancelObjs(ids?: Array<string>) {
  return request({
    url: '/business/yjWarnInfoCamera/updateByIds',
    method: 'post',
    data: ids
  })
}