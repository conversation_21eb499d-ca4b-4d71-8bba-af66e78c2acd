/**
 * 文件API参数类型声明
 */
export interface FileInfo {
  name: string;
  url: string;
}

/**
 * 新建文件夹API参数类型声明
 */
export interface AddFileFolder{
  folderName: string;
  pid: string;
  type:string
}

/**
 * 编辑文件夹名称API参数类型声明
 */
export interface EditFileFolder{
  folderName: string;
  id: string;
}

/**
 * 上传文件夹获取桶路径API参数类型声明
 */
export interface FolderData{
  "fileName":string,
  "format":string,
  "type":string,
  "folderId":string,
  "totalSize":number,
  "objectKey":string
}

/**
 * 上传文件夹各个文件API类型声明
 */
export interface FolderFileData{
  "fileName":string,
  "file":any
}

/**
 * 提交上传表单API参数类型声明
 */
export interface FormDataType{
  "fileName": string,//资源名称
  "type": string,//资源类型
  "keywordIds": string,//关键词
  "publicStatus": string,//公开权限
  "commentStatus": string,//评论权限
  "downloadStatus": string,//下载权限
  "shareStatus": string,//分享权限
  "quoteStatus": string,//引用权限
  //   resource: string,
  "desc": string,//资源描述
  "cover": string,//资源封面
  "file": string,
  "objectKey":string,//文件
  "lng": string,//定位信息
  "lat": string,//定位信息
  "folderId":string,//文件夹id
  "totalSize":number,//文件大小
  "url":string//在线视频的路径
}
/**
 * 素材发表评论API参数类型声明
 */
export interface CommentMaterialType{
  "materialId":string,
  "content":string
}
/**
 * 场景发表评论API参数类型声明
 */
export interface CommentSceneType{
  "sceneId":string,
  "content":string
}

// 筛选搜索条件表单类型
export interface ResourcesForm {
  pageNum?: number,
  pageSize?: number,
  type?: string,
  sortName?: string,
  createBy?: string,
  searchValue?: string,
  folderId?: string
}