import request from '/@/utils/request'

export function fetchList(query?: Object) {
	return request({
		url: '/business/wb/region/byPage',
		method: 'get',
		params: query,
	})
}

export function addObj(obj?: Object) {
	return request({
		url: '/business/wb/region',
		method: 'post',
		data: obj,
	})
}

export function getObj(id?: string) {
	return request({
		url: '/business/wb/region/' + id,
		method: 'get',
	})
}

export function delObjs(ids?: Object) {
	return request({
		url: '/business/wb/region',
		method: 'delete',
		data: ids,
	})
}

export function putObj(obj?: Object) {
	return request({
		url: '/business/wb/region',
		method: 'put',
		data: obj,
	})
}
