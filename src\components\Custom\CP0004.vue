<template>
	<div class="stat-cards-container">
		<div v-for="(item, k) in props.data" :key="k" class="stat-card">
				<div class="stat-card-title">{{ titleAlias[item.title]||item.title }}</div>
				<div class="stat-card-value">
					<span class="stat-number">{{ item.num }}</span>
				</div>
		</div>
	</div>
</template>

<script lang="ts" name="CP0004" setup>
const titleAlias = ref({
	"数据项": '资源项数量（个）',
	资源数量: '资源数据量（条）',
	近一月发布: '近一月新增资源（条）',
	资源数据量: '资源存储量（GB）',
});
const props = defineProps({
	data: {
		type: Array<any>,
		default: [
			{ title: '资源项', num: 12, unit: '', tip: '总共有多少个数据项类型' },
			{ title: '资源数量', num: 13449, unit: '条', tip: '总共发布了多少个资源数' },
			{ title: '近一月', num: 3687, unit: '', tip: '近一个月内总共发布了多少个资源数' },
			{ title: '资源存储量', num: 88.43, unit: 'GB', tip: '所有资源的数据存储内存有多大' },
		],
	},
});
</script>
<style lang="scss" scoped>
.stat-cards-container {
	height: 100%;
	width: 100%;
	display: flex;
	justify-content: space-between;
	gap: 15px;
	padding: 0px 0;

	.stat-card {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: left;
		justify-content: top;
		border-radius: 8px;
		box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
		height: 100%;
    background:url('/@/assets/img/resourceHome/title_back.png') no-repeat right bottom;
    background-size: 100%;
    background-color: #fff;

		.stat-card-value {
			display: flex;
			align-items: baseline;
			justify-content: is-left;
			

			.stat-number {
        font-family: Source Han Sans CN;
        font-weight: 700;
        font-size: 32px;
        line-height: 100%;
        letter-spacing: 0px;
        padding-left:20px;
        height:50px;
			}
		}

		.stat-card-title {
      padding:20px;
			font-family: Source Han Sans CN;
			font-weight: 400;
			font-size: 16px;
			line-height: 100%;
			letter-spacing: 0px;
			color: #3B0000;
		}
	}
}
</style>
