<template>
  <div class="mt-content">
    <div ref="mtChart" class="chart-box"></div>
  </div>
</template>

<script lang="ts" name="CP0007" setup>
import * as echarts from "echarts";

let myChart = null;
const props = defineProps({
  data: {
    type: Object,
    default: {
      legend: ["资源1", "资源2", "资源3", "资源4", "资源5", "资源6", "资源7"],
      value: [2000, 1000, 3000, 4000, 2000, 2000, 2000],
      max: 10000,
    },
  },
});
const mtChart: any = ref(null);

const resizeFn = () => {
  myChart?.resize();
};

onUnmounted(() => {
  window.removeEventListener("resize", resizeFn);
});

onMounted(() => {
  initChart();
  window.addEventListener("resize", resizeFn);
});
const initChart = () => {
  myChart = echarts.init(mtChart.value);
  mtChart.value.removeAttribute("_echarts_instance_");
  let salvProName = props.data.legend;
  let salvProValue = props.data.value;
  let salvProMax = props.data.legend.map(() => {
    return props.data.max;
  });
  let options = {
    backgroundColor: "transparent",
    grid: {
      left: "2%",
      right: "2%",
      bottom: "-25px",
      top: "24px",
      containLabel: true,
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "none",
      },
      formatter: function (params: any) {
        return params[0].name + " : " + params[0].value;
      },
    },
    xAxis: {
      show: false,
      type: "value",
    },
    yAxis: [
      {
        type: "category",
        inverse: true,
        axisLabel: {
          show: true,
          color: "#554242",
          fontSize: 14,
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        data: salvProName,
      },
      {
        type: "category",
        inverse: true,
        axisTick: "none",
        axisLine: "none",
        show: true,
        axisLabel: {
          color: "#FD4949",
          fontSize: "12",
        },
        data: salvProValue,
      },
    ],
    series: [
      {
        name: "值",
        type: "bar",
        zlevel: 1,
        itemStyle: {
          borderRadius: 10,
          color: "#FD4949",
        },
        barWidth: 10,
        data: salvProValue,
        barCategoryGap: 20,
      },
      {
        name: "背景",
        type: "bar",
        barWidth: 10,
        barGap: "-100%",
        data: salvProMax,
        itemStyle: {
          color: "rgba(253,73,73,0.15)",
          borderRadius: 10,
        },
      },
    ],
  };
  myChart.setOption(options);
};
</script>
<style lang="scss" scoped>
.mt-content {
  height: 100%;
  padding: 10px;
  .chart-box {
    width: 100%;
    height: calc(100% - 0px);
  }
}
</style>
