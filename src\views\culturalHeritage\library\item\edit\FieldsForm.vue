<template>
  <el-dialog class="fields-form-dialog" v-model="visible" :width="1000" :title="form.id ? '编辑字段' : '新增字段'" align-center
    destroy-on-close :show-close="true" :close-on-click-modal="false" :close-on-press-escape="false" @close="onCancel">
    <div class="fields-form-container">
      <!-- 左侧字段类型选择区域 -->
      <div class="field-type-selector">
        <div class="selector-header">
          <h3>请选择字段类型</h3>
        </div>
        <div class="type-groups">
          <div v-for="(group, index) in formGroupList" :key="index" class="type-group">
            <div class="group-title">{{ group.name }}</div>
            <div class="type-buttons">
              <el-button v-for="typeCode in group.children" :key="typeCode" class="type-button"
                :class="{ 'is-selected': form.typeCode === typeCode }"
                :type="form.typeCode === typeCode ? 'primary' : 'default'" @click="selectFieldType(typeCode)">
                <el-icon class="type-icon">
                  <component :is="getFieldTypeIcon(typeCode)" />
                </el-icon>
                <span>{{ columnTypeConfig[typeCode]?.name }}</span>
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧表单配置区域 -->
      <div class="field-config-form">
        <el-form ref="dataFormRef" :model="form" :rules="dataRules" formDialogRef label-width="100px"
          v-loading="loading">
          <div style="
              width: calc(100% + 10px);
              padding-right: 5px;
              overflow-x: hidden;
            ">
            <div class="form-subTitle mb20">
              字段信息
              <div class="split-line"></div>
            </div>
            <el-row :gutter="24" class="mb20">
              <el-col :span="12">
                <el-form-item label="字段名：" prop="name">
                  <el-input v-model="form.name" maxlength="20" placeholder="请输入字段名" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="中文名称：" prop="cnName">
                  <el-input v-model="form.cnName" maxlength="20" placeholder="请输入中文名称" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24" class="mb20">
              <el-col :span="12">
                <el-form-item label="字段类型：" prop="typeCode">
                  <el-select v-model="form.typeCode" placeholder="请输入选择字段类型">
                    <el-option-group v-for="group in formGroupList" :key="group.name" :label="group.name">
                      <el-option v-for="typeCode in group.children" :key="typeCode"
                        :label="columnTypeConfig[typeCode].name" :value="typeCode" />
                    </el-option-group>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="提示：" prop="prompt">
                  <el-input v-model="form.prompt" maxlength="20" placeholder="请输入提示" />
                </el-form-item>
              </el-col>
            </el-row>
            <!-- 分组选择字段 - 仅表单类型条目显示 -->
            <el-row :gutter="24" class="mb20" v-if="isFormTypeGroup">
              <el-col :span="24">
                <el-form-item label="所属分组：" prop="subGroupId">
                  <el-select v-model="form.subGroupId" placeholder="请选择分组" clearable style="width: 100%">
                    <el-option v-for="subGroup in availableSubGroups" :key="subGroup.id" :label="subGroup.groupName"
                      :value="subGroup.id" />
                  </el-select>
                  <div class="form-item-tip">
                    {{ availableSubGroups.length === 0 ? '当前条目暂无分组，可以不选择' : '选择字段所属的分组' }}
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24" class="mb20" v-if="['oneLine', 'multiLine', 'richText'].includes(form.typeCode)">
              <el-col :span="12">
                <el-form-item label="最小长度：" prop="input_minLength">
                  <el-input v-model="form.input_minLength" type="number" step="1" min="0" maxlength="20"
                    placeholder="请输入最小长度" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="最大长度：" prop="input_maxLength">
                  <el-input v-model="form.input_maxLength" type="number" step="1" min="0" maxlength="20"
                    placeholder="请输入最大长度" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24" class="mb20" v-if="['oneLine', 'multiLine', 'richText'].includes(form.typeCode)">
              <el-col :span="24">
                <el-form-item label="默认值：" prop="input_defaultVal">
                  <el-input v-model="form.input_defaultVal" maxlength="20" placeholder="请输入默认值" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24" class="mb20" v-if="['number', 'amount'].includes(form.typeCode)">
              <el-col :span="12">
                <el-form-item label="单位：" prop="number_unit">
                  <el-input v-model="form.number_unit" maxlength="20" placeholder="请输入单位" />
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="['amount'].includes(form.typeCode)">
                <el-form-item label="显示大写：" prop="number_showUppercase">
                  <el-radio-group v-model="form.number_showUppercase">
                    <el-radio :label="1" border>是</el-radio>
                    <el-radio :label="0" border>否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="['number'].includes(form.typeCode)">
                <el-form-item label="格式：" prop="number_format">
                  <el-select placeholder="请选择格式" clearable v-model="form.number_format">
                    <el-option label="数值" value="数值" />
                    <el-option label="百分比" value="百分比" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24" class="mb20" v-if="['number', 'amount'].includes(form.typeCode)">
              <el-col :span="12">
                <el-form-item label="小数位：" prop="number_point">
                  <el-input-number v-model="form.number_point" :min="0" :max="10" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="数值范围：" prop="number_range">
                  <el-input v-model="form.number_range[0]" maxlength="20" style="width: 80px" />
                  <span style="margin: 0px 3px; font-weight: bolder">~</span>
                  <el-input v-model="form.number_range[1]" maxlength="20" style="width: 80px" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24" class="mb20" v-if="['date', 'dateRange'].includes(form.typeCode)">
              <el-col :span="24">
                <el-form-item label="格式：" prop="date_format">
                  <el-select placeholder="请选择格式" clearable v-model="form.date_format">
                    <el-option v-for="(item, key) in timeConfig" :key="key" :label="item.name" :value="item.format" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24" class="mb20" v-if="['date', 'dateRange'].includes(form.typeCode)">
              <el-col :span="24">
                <el-form-item label="日期范围：" prop="date_range">
                  <el-date-picker v-model="form.date_range[0]" :type="timeConfig[form.date_format]?.timeType"
                    placeholder="开始时间" :disabled="!form.date_format" :format="timeConfig[form.date_format]?.format"
                    value-format="YYYY/MM/DD HH:mm:ss" style="width: 230px !important" />
                  <span style="margin: 0px 5px">~</span>
                  <el-date-picker v-model="form.date_range[1]" :type="timeConfig[form.date_format]?.timeType"
                    placeholder="结束时间" :disabled="!form.date_format" :format="timeConfig[form.date_format]?.format"
                    value-format="YYYY/MM/DD HH:mm:ss" style="width: 230px !important" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24" class="mb20" v-if="['ratioOption', 'multiOption'].includes(form.typeCode)">
              <el-col :span="24">
                <el-form-item label="选项组：" prop="options_list">
                  <div style="width: 100%; padding-bottom: 5px" class="">
                    <el-table :data="form.options_list" style="width: 100%" border>
                      <el-table-column prop="id" label="" width="40" align="center">
                        <template #default>
                          <div v-if="['ratioOption'].includes(form.typeCode)" style="
                              width: 14px;
                              height: 14px;
                              border: 2px solid var(--el-color-primary);
                              border-radius: 100%;
                            "></div>
                          <div v-if="['multiOption'].includes(form.typeCode)" style="
                              width: 14px;
                              height: 14px;
                              border: 2px solid var(--el-color-primary);
                            "></div>
                        </template>
                      </el-table-column>
                      <el-table-column prop="key" label="选项名称" align="center">
                        <template #default="scope">
                          <el-input v-model="scope.row.key" maxlength="100" placeholder="请输入选项名称" @change="
                            () => {
                              scope.row.key = scope.row.key;
                            }
                          " />
                        </template>
                      </el-table-column>
                      <el-table-column prop="value" label="选项值" align="center">
                        <template #default="scope">
                          <el-input v-model="scope.row.value" maxlength="100" placeholder="请输入选项名称" @change="
                            () => {
                              scope.row.value = scope.row.value;
                            }
                          " />
                        </template>
                      </el-table-column>
                      <el-table-column fixed="right" label="操作" align="center" width="120px">
                        <template #default="scope">
                          <el-button link type="danger" size="small"
                            @click="form.options_list.splice(scope.$index, 1)">删除</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <el-button class="mt-4" icon="CirclePlusFilled" type="primary" style="width: 100%"
                      @click="form.options_list.push({})">新增选项</el-button>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24" class="mb20" v-if="['province'].includes(form.typeCode)">
              <el-col :span="12">
                <el-form-item label="省别：" prop="area_province">
                  <el-select placeholder="请选择省别" clearable v-model="form.area_province">
                    <el-option v-for="(name, key) in provinceObject" :key="key" :label="name" :value="key" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="格式：" prop="area_format">
                  <el-select placeholder="请选择格式" clearable v-model="form.area_format">
                    <el-option label="省" value="省" />
                    <el-option label="省市" value="省市" />
                    <el-option label="省市区" value="省市区" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24" class="mb20" v-if="['image', 'attach'].includes(form.typeCode)">
              <el-col :span="12">
                <el-form-item label="上传个数：" prop="upload_fileCount">
                  <el-input-number v-model="form.upload_fileCount" :min="1" :max="100" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="大小限制：" prop="upload_fileSize">
                  <el-input v-model="form.upload_fileSize" placeholder="文件大小限制">
                    <template #append>MB</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24" class="mb20">
              <el-col :span="24">
                <el-form-item label="备注：" prop="remark">
                  <el-input type="textarea" maxlength="250" :rows="3" v-model="form.remark" placeholder="请输入备注" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="form-subTitle mb10">
            字段规则
            <div class="split-line"></div>
          </div>
          <el-row :gutter="24" class="mb20">
            <el-col :span="12">
              <el-form-item label="是否必填：" prop="mustFilee">
                <el-radio-group v-model="form.mustFilee">
                  <el-radio :label="true" border>是</el-radio>
                  <el-radio :label="false" border>否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否唯一值：" prop="uniqueValue">
                <el-radio-group v-model="form.uniqueValue">
                  <el-radio :label="true" border>是</el-radio>
                  <el-radio :label="false" border>否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24" class="mb20">
            <el-col :span="12">
              <el-form-item label="是否只读：" prop="readOnly">
                <el-radio-group v-model="form.readOnly">
                  <el-radio :label="true" border>是</el-radio>
                  <el-radio :label="false" border>否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否脱敏：" prop="desensitization">
                <el-radio-group v-model="form.desensitization">
                  <el-radio :label="true" border>是</el-radio>
                  <el-radio :label="false" border>否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="onCancel">取消</el-button>
        <el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog v-model="fieldsErrorVisible" :show-close="false" width="400">
    <el-result icon="error" title="检查失败" style="padding-top: 0px">
      <template #sub-title>
        <div v-for="item in validateErrMsg" :key="item">
          <el-text type="danger">
            <el-icon>
              <WarnTriangleFilled />
            </el-icon>
            {{ item }}
          </el-text>
        </div>
      </template>
      <template #extra>
        <el-button type="primary" @click="validateErrMsg = []">{{ "确定" }}</el-button>
      </template>
    </el-result>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref, computed, watch } from "vue";
import { useMessage } from "/@/hooks/message";
import { addColumnInfo, editColumnInfo } from "/@/api/resource/catalog/columnInfo";
import { useFieldSerialization } from "/@/composables/useFieldSerialization";

import { columnTypeConfig, timeConfig } from "/@/config/resourceConfig";
import { provinceObject } from "/@/utils/chinaArea";
import {
  EditPen,
  ChatLineSquare,
  Notebook,
  Edit,
  Money,
  Calendar,
  Picture,
  Paperclip,
  AlarmClock,
  Select,
  Finished,
  WarnTriangleFilled
} from "@element-plus/icons-vue";

const props = defineProps({
  record: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(['cancel', 'refresh']);

// 获取字段序列化工具
const { serializeFieldData, deserializeFieldData } = useFieldSerialization();

// 字段类型分组配置
const formGroupList: any = [
  {
    name: "文本",
    children: ["oneLine", "multiLine", "richText"],
  },
  {
    name: "数字",
    children: ["number", "amount"],
  },
  {
    name: "日期",
    children: ["date", "dateRange"],
  },
  {
    name: "选项",
    children: ["ratioOption", "multiOption", "province"],
  },
  {
    name: "其他",
    children: ["image", "attach"],
  },
];

// 字段类型图标映射
const fieldTypeIcons: any = {
  oneLine: EditPen,
  multiLine: ChatLineSquare,
  richText: Notebook,
  number: Edit,
  amount: Money,
  date: Calendar,
  dateRange: AlarmClock,
  ratioOption: Select,
  multiOption: Finished,
  province: Edit,
  image: Picture,
  attach: Paperclip
};

// 获取字段类型图标
const getFieldTypeIcon = (typeCode: string) => {
  return fieldTypeIcons[typeCode] || Edit;
};

// 选择字段类型
const selectFieldType = (typeCode: string) => {
  form.typeCode = typeCode;
};


const visible = ref(false);
const loading = ref(false);
const dataFormRef: any = ref();

// 分组相关数据
const currentGroupInfo = ref<any>(null); // 当前选中的条目信息
const availableSubGroups = ref<any[]>([]); // 可用的分组列表

// 计算属性：判断当前条目是否为表单类型
const isFormTypeGroup = computed(() => {
  return currentGroupInfo.value.length;
});

// 设置分组数据
const setSubGroupData = (groupData: any) => {
  if (groupData && Array.isArray(groupData)) {
    // 从传入的条目数据中获取子分组
    availableSubGroups.value = groupData;
  } else {
    availableSubGroups.value = [];
  }
};
const form: any = reactive({
  id: null as any,
  tableInfoId: null as any, //表所属元数据id
  groupId: null as any, //字段所属目录id
  subGroupId: null as any, //字段所属分组id
  name: null as any,
  cnName: null as any,
  typeCode: "oneLine" as any,
  prompt: null as any,
  remark: null as any,
  columnRule: {
    uniqueValue: true, // 是否唯一值
    mustFilee: true, // 是否必填
    readOnly: true, // 是否只读
    desensitization: true, // 是否脱敏
  },
  columnCustomeRule: {},
  input_minLength: 0,
  input_maxLength: 250,
  input_defaultVal: null as any,
  number_unit: null as any,
  number_showUppercase: 1,
  number_point: 2,
  number_format: "数值",
  number_range: [0, 100] as any,
  date_format: "YYYY/MM/DD HH:mm:ss",
  date_range: [] as any,
  options_list: [] as any,
  area_province: null as any,
  area_format: "省市区" as any,
  upload_fileCount: 1,
  upload_fileSize: 200,
  mustFilee: false,
  uniqueValue: false,
  readOnly: false,
  desensitization: false,
  zljc: [] as any,
});
const validateErrMsg: any = ref([]);
// 定义校验规则
const dataRules: any = ref({
  name: [
    { required: true, message: "字段名不能为空", trigger: "blur" },
    {
      trigger: "change",
      validator: (_rule: any, value: any, callback: any) => {
        const reg = new RegExp("[\\u4E00-\\u9FFF]+", "g");
        if (!(value && /[a-zA-Z]+/.test(value.substring(0, 1)))) {
          callback(new Error("字段名首字母必须是字母"));
        } else if (!(value && /^[a-z0-9]+$/.test(value))) {
          callback(new Error("字段名必须是小写字母"));
        } else if (reg.test(value)) {
          callback(new Error("字段名首字母不能包含中文"));
        } else {
          callback();
        }
      },
    },
  ],
  cnName: [{ required: true, message: "中文名称不能为空", trigger: "blur" }],
  typeCode: [{ required: true, message: "字段类型不能为空", trigger: "blur" }],
  input_minLength: [
    { required: true, message: "最小长度不能为空", trigger: "blur" },
    // { type: "number", message: "请输入数值" },
  ],
  input_maxLength: [
    { required: true, message: "最大长度不能为空", trigger: "blur" },
    // { type: "number", message: "请输入数值" },
  ],
});
watch(
  () => form.typeCode,
  (val) => {
    dataFormRef.value?.resetFields();
    form.typeCode = val;
    let rules: any = {
      name: [{ required: true, message: "字段名不能为空", trigger: "blur" }],
      cnName: [{ required: true, message: "中文名称不能为空", trigger: "blur" }],
      typeCode: [{ required: true, message: "字段类型不能为空", trigger: "blur" }],
    };

    // 为表单类型条目添加分组验证规则
    if (isFormTypeGroup.value && availableSubGroups.value.length > 0) {
      rules.subGroupId = [
        { required: true, message: "请选择所属分组", trigger: "change" }
      ];
    }
    if (["oneLine", "multiLine", "richText"].includes(val)) {
      rules.input_minLength = [
        { required: true, message: "最小长度不能为空", trigger: "blur" },
        // { type: "number", message: "请输入数值" },
      ];
      rules.input_maxLength = [
        { required: true, message: "最大长度不能为空", trigger: "blur" },
        // { type: "number", message: "请输入数值" },
      ];
    } else if (["number"].includes(val)) {
      rules.number_format = [
        { required: true, message: "请选择格式", trigger: "change" },
      ];
    } else if (["date", "dateRange"].includes(val)) {
      rules.date_format = [{ required: true, message: "请选择格式", trigger: "change" }];
      rules.date_range = [
        {
          message: "开始时间必须小于结束时间",
          trigger: "change",
          validator: (_rule: any, value: any, callback: any) => {
            if (new Date(value[0]) > new Date(value[1])) {
              callback(new Error("开始时间必须小于结束时间"));
            } else {
              callback();
            }
          },
        },
      ];
      form.date_range = [];
    } else if (["ratioOption", "multiOption"].includes(val)) {
      rules.options_list = [
        {
          required: true,
          trigger: "change",
          validator: (_rule: any, value: any, callback: any) => {
            const isnull = value?.find((obj: any) => {
              return (
                !obj.value || obj.value.length === 0 || !obj.key || obj.key.length === 0
              );
            });
            const objValuelist: any = [];
            let issame = false;
            value?.forEach((obj: any) => {
              if (objValuelist.includes(obj.value)) {
                issame = true;
                return;
              } else {
                objValuelist.push(obj.value);
              }
            });
            if (!value || value?.length < 2) {
              callback(new Error("选项不能小于两个"));
            } else if (isnull) {
              callback(new Error("选项名称和选项值不能为空"));
            } else if (issame) {
              callback(new Error("选项名称和选项值不能重复"));
            } else {
              callback();
            }
          },
        },
      ];
      // 只在新增模式下重置选项列表，编辑模式保留现有数据
      if (!form.id) {
        form.options_list = [];
      }
    } else if (["province"].includes(val)) {
      rules.area_province = [{ required: true, message: "请选择省别", trigger: "blur" }];
      rules.area_format = [{ required: true, message: "请选择格式", trigger: "blur" }];
    } else if (["image", "attach"].includes(val)) {
      rules.upload_fileCount = [
        { required: true, message: "请输入上传文件个数", trigger: "blur" },
      ];
      rules.upload_fileSize = [
        { required: true, message: "请输入上传文件大小", trigger: "blur" },
      ];
    }
    dataRules.value = rules;
  }
);
// 打开弹窗，赋值
const openDialog = (record?: any) => {
  visible.value = true;

  // 设置当前条目信息和分组数据
  currentGroupInfo.value = record.groupData;
  setSubGroupData(record.groupData);

  // 设置表单初始值
  Object.assign(form, {
    id: null as any,
    tableInfoId: null as any, //表所属元数据id
    groupId: null as any, //字段所属目录id
    subGroupId: null as any, //字段所属分组id
    name: null as any,
    cnName: null as any,
    typeCode: "oneLine" as any,
    prompt: null as any,
    remark: null as any,
    columnRule: "",
    columnCustomeRule: "",
    input_minLength: 0,
    input_maxLength: 250,
    input_defaultVal: null as any,
    number_unit: null as any,
    number_showUppercase: 1,
    number_point: 2,
    number_format: "数值",
    number_range: [0, 100] as any,
    date_format: "YYYY/MM/DD HH:mm:ss",
    date_range: [] as any,
    options_list: [] as any,
    area_province: null as any,
    area_format: "省市区" as any,
    upload_fileCount: 1,
    upload_fileSize: 200,
    mustFilee: false,
    uniqueValue: false,
    readOnly: false,
    desensitization: false,
    zljc: [] as any,
  });

  // 编辑模式 - 使用反序列化工具解析服务端数据
  if (record?.id) {
    // 使用 useFieldSerialization 组合式函数反序列化数据
    const deserializedData = deserializeFieldData(record);
    Object.assign(form, deserializedData);

    // 特殊处理选项类型字段的数据源
    if (["ratioOption", "multiOption"].includes(record.typeCode)) {
      let optionsList: any[] = [];

      // 优先使用传入的已解析 options_list
      if (record.options_list && Array.isArray(record.options_list)) {
        optionsList = record.options_list;
      }
      // 其次尝试解析 columnCustomeRule
      else if (record.columnCustomeRule) {
        try {
          const parsed = JSON.parse(record.columnCustomeRule);
          optionsList = Array.isArray(parsed) ? parsed : [];
        } catch (e) {
          optionsList = [];
        }
      }

      // 确保数据格式正确
      form.options_list = optionsList.map((item: any) => ({
        key: item.key || '',
        value: item.value || ''
      }));
    }
  }

  // 设置必要的关联ID
  if (record) {
    form.tableInfoId = record.tableInfoId;
    form.groupId = record.groupId;
    form.subGroupId = record.subGroupId || null; // 设置分组ID
  }
};
// 关闭
const onCancel = () => {
  visible.value = false;
  emit("cancel");
};

// 提交
const onSubmit = async () => {
  // 表单验证
  const valid = await dataFormRef.value.validate().catch(() => { });
  if (!valid) return false;

  loading.value = true;

  try {
    // 使用序列化工具处理表单数据
    const serializedData = serializeFieldData(form);

    // 提交到服务器
    serializedData.id ?
      await editColumnInfo(serializedData) :
      await addColumnInfo(serializedData);

    // 提示成功消息
    useMessage().success(form.id ? "编辑成功" : "新增成功");
    visible.value = false;
    emit("refresh");
  } catch (err) {
    useMessage().error((err as any).msg);
  } finally {
    loading.value = false;
  }
};
const fieldsErrorVisible = computed(() => {
  return validateErrMsg.value.length > 0;
});
onMounted(() => {
  if (JSON.stringify(props.record) != "{}") {
    openDialog(props.record);
  }
});
defineExpose({
  openDialog,
});
</script>

<style lang="scss" scoped>
.fields-form-dialog {
  :deep(.el-dialog__body) {
    padding: 0;
  }
}

.fields-form-container {
  display: flex;
  height: 550px;
  min-height: 550px;
}

.field-type-selector {
  width: 320px;
  border-right: 1px solid #e4e7ed;
  background-color: #fafafa;

  .selector-header {
    padding: 20px 16px 16px;
    border-bottom: 1px solid #e4e7ed;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }

  .type-groups {
    padding: 20px 16px;
    height: calc(100% - 80px);
    overflow-y: auto;
  }

  .type-group {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }

    .group-title {
      font-size: 14px;
      font-weight: 600;
      color: #909399;
      margin-bottom: 10px;
      padding-left: 4px;
    }

    .type-buttons {
      display: flex !important;
      flex-wrap: wrap !important;
      gap: 8px !important;
      justify-content: space-between !important;
    }

    .type-button {
      width: calc(50% - 10px) !important;
      height: 40px !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      padding: 0 8px !important;
      border-radius: 6px !important;
      transition: all 0.2s !important;
      font-size: 13px !important;
      margin-bottom: 0 !important;
      margin-right: 0 !important;
      white-space: nowrap !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      border: 1px solid #dcdfe6 !important;
      margin: 0px;

      .type-icon {
        margin-right: 6px !important;
        font-size: 14px !important;
        flex-shrink: 0 !important;
      }

      &.is-selected {
        background-color: #A12F2F;
        border-color: #A12F2F;
        color: white;
        box-shadow: 0 2px 4px rgba(161, 47, 47, 0.2);
      }

      &:hover:not(.is-selected) {
        background-color: #f5f7fa;
        border-color: #c0c4cc;
        color: #606266;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }
}

.field-config-form {
  flex: 1;
  padding: 20px;
  overflow-y: auto;

  .field-form {
    height: 100%;
  }

  .form-content {
    height: 100%;
  }

  .form-section {
    margin-bottom: 32px;

    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      font-size: 16px;
      font-weight: 600;
      color: #303133;

      .title-line {
        flex: 1;
        height: 1px;
        background-color: #e4e7ed;
        margin-left: 16px;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid #e4e7ed;

  // 修复对话框底部确认按钮悬浮时文字颜色问题
  :deep(.el-button--primary) {
    background-color: #A12F2F;
    border-color: #A12F2F;
    color: #ffffff !important;

    &:hover {
      background-color: #8a2828;
      border-color: #8a2828;
      color: #ffffff !important;
    }

    &:focus {
      background-color: #A12F2F;
      border-color: #A12F2F;
      color: #ffffff !important;
    }

    &:active {
      background-color: #8a2828;
      border-color: #8a2828;
      color: #ffffff !important;
    }

    &:disabled {
      background-color: #c0c4cc;
      border-color: #c0c4cc;
      color: #ffffff !important;
    }
  }
}

// 保持原有的主题色样式
:deep(.el-button--primary) {
  background-color: #A12F2F;
  border-color: #A12F2F;
  color: #ffffff;

  &:hover {
    background-color: #8a2828;
    border-color: #8a2828;
    color: #ffffff !important; // 确保悬浮时文字保持白色
  }

  &:focus {
    background-color: #A12F2F;
    border-color: #A12F2F;
    color: #ffffff !important;
  }

  &:active {
    background-color: #8a2828;
    border-color: #8a2828;
    color: #ffffff !important;
  }
}

:deep(.el-button--default:hover) {
  color: #A12F2F;
  border-color: #A12F2F;
}

// 修复新增选项按钮悬浮时文字颜色问题
:deep(.el-button--primary.mt-4) {
  background-color: #A12F2F;
  border-color: #A12F2F;
  color: #ffffff !important;

  &:hover {
    background-color: #8a2828;
    border-color: #8a2828;
    color: #ffffff !important;
  }

  &:focus {
    background-color: #A12F2F;
    border-color: #A12F2F;
    color: #ffffff !important;
  }

  &:active {
    background-color: #8a2828;
    border-color: #8a2828;
    color: #ffffff !important;
  }
}

:deep(.el-form-item__label) {
  color: #606266;
  font-weight: 500;
}

:deep(.el-dialog__header) {
  padding: 10px;
  border-bottom: 1px solid #e4e7ed;

  .el-dialog__title {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
  }
}

// 表单样式优化
:deep(.el-input) {
  .el-input__wrapper {
    border-radius: 6px;
  }
}

:deep(.el-select) {
  .el-select__wrapper {
    border-radius: 6px;
  }
}

:deep(.el-textarea) {
  .el-textarea__inner {
    border-radius: 6px;
  }
}

// 保留原有的表单子标题样式
.form-subTitle {
  width: 100%;
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;

  .split-line {
    flex: 1;
    height: 1px;
    background-color: #e4e7ed;
    margin-left: 16px;
  }
}

// 确保按钮样式生效的深度选择器
:deep(.type-buttons) {
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 8px !important;
  justify-content: space-between !important;

  .el-button {
    width: calc(50% - 10px) !important;
    height: 40px !important;
    margin-bottom: 0 !important;
    margin-right: 0 !important;
    font-size: 13px !important;
    padding: 0 8px !important;

    .el-icon {
      margin-right: 6px !important;
      font-size: 14px !important;
    }
  }
}

// 全局修复主要按钮文字颜色问题
:deep(.el-button) {
  &.el-button--primary {
    background-color: #A12F2F !important;
    border-color: #A12F2F !important;
    color: #ffffff !important;

    &:hover {
      background-color: #8a2828 !important;
      border-color: #8a2828 !important;
      color: #ffffff !important;
    }

    &:focus {
      background-color: #A12F2F !important;
      border-color: #A12F2F !important;
      color: #ffffff !important;
    }

    &:active {
      background-color: #8a2828 !important;
      border-color: #8a2828 !important;
      color: #ffffff !important;
    }

    &:disabled {
      background-color: #c0c4cc !important;
      border-color: #c0c4cc !important;
      color: #ffffff !important;
    }

    // 确保图标颜色也是白色
    .el-icon {
      color: #ffffff !important;
    }

    // 确保按钮内的所有文本都是白色
    span {
      color: #ffffff !important;
    }
  }
}

// 分组选择字段样式
.form-item-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

// 响应式设计
@media (max-width: 1200px) {
  .fields-form-container {
    height: 500px;
  }

  .field-type-selector {
    width: 280px;
  }
}
</style>
