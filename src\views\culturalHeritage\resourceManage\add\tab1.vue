<template>
  <div style="width: 100%; height: calc(100% - 55px); position: relative">
    <span class="dialog-footer">
      <el-button @click="backTo">取消</el-button>
      <el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
    </span>
    <div class="layout-padding-auto w100" style="overflow-y: auto; height: 100%">
      <el-form
        ref="dataFormRef"
        :model="form"
        :rules="dataRules"
        formDialogRef
        label-width="120px"
        v-loading="loading"
      >
        <el-col :span="24" class="mb0">
          <div class="form-subTitle mb5">
            <div class="split-line"></div>
            基础信息
          </div>
        </el-col>
        <div class="layout-form-view layout-border-radius mb10">
          <el-row>
            <el-col :span="8">
              <el-form-item label="资源封面" prop="assets_cover">
                <ImageUpload
                  class="custom-upload"
                  v-model:imageUrl="form.assets_cover"
                  borderRadius="0%"
                  width="150px"
                  height="130px"
                  uploadFileUrl="/datacenter/learning/material/cover"
                >
                  <template #empty>
                    <el-icon><Picture /></el-icon>
                    <span>请上传封面</span>
                  </template>
                </ImageUpload>
                <div style="position: absolute; right: 0px; width: 200px; color: #999999">
                  <div>图片支持JPG、PNG且小于5M</div>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-row :gutter="24" class="mb20">
                <el-col :span="12" class="mb20">
                  <el-form-item label="资源名称" prop="assets_name">
                    <el-input
                      v-model="form.assets_name"
                      maxlength="20"
                      placeholder="请输入资源名称"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12" class="mb20">
                  <el-form-item label="公开状态" prop="public_state">
                    <el-select
                      placeholder="请选择公开状态"
                      clearable
                      v-model="form.public_state"
                    >
                      <el-option label="公开" :value="1" />
                      <el-option label="私有" :value="0" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="24" class="mb20">
                <el-col :span="12" class="mb20">
                  <el-form-item label="资源密级" prop="security_id">
                    <el-select
                      v-model="form.security_id"
                      clearable
                      placeholder="请选择文件密级"
                    >
                      <el-option
                        v-for="item in securityList"
                        :key="item.id"
                        :label="item.title"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12" class="mb20">
                  <el-form-item label="资源标签" prop="assets_tag">
                    <el-input
                      v-model="form.assets_tag"
                      maxlength="16"
                      placeholder="输入内容，多个标签用“，”隔开"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="描述" prop="assets_remark">
                <el-input
                  type="textarea"
                  maxlength="250"
                  :rows="6"
                  show-word-limit
                  v-model="form.assets_remark"
                  placeholder="请输入描述"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <el-col :span="24" class="mb0">
          <div class="form-subTitle mb5">
            <div class="split-line"></div>
            定位信息
          </div>
        </el-col>
        <div
          class="layout-form-view layout-border-radius mb10"
          style="position: relative"
        >
          <div class="map-maker-box" @click="mapMakerVisible = true">
            <el-icon style="font-size: 18px"><Location /></el-icon>
            <span>地图拾取</span>
          </div>
          <el-row>
            <el-col :span="6" class="mb0">
              <el-form-item label="经度" prop="longitude">
                <el-input
                  v-model="form.longitude"
                  maxlength="20"
                  placeholder="请输入经度"
                  width="100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6" class="mb0">
              <el-form-item label="纬度" prop="latitude">
                <el-input
                  v-model="form.latitude"
                  maxlength="20"
                  placeholder="请输入纬度"
                  width="100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12" class="mb0">
              <el-form-item label="位置描述" prop="location_remark">
                <el-input
                  v-model="form.location_remark"
                  maxlength="20"
                  placeholder="请输入位置描述"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <div
        v-for="(item, index) in columnInfoList"
        :key="index"
        :gutter="24"
        class="w100 mb0"
        style="width: calc(100% + 25px)"
      >
        <el-col :span="24" class="mb0">
          <div class="form-subTitle mb5">
            <div class="split-line"></div>
            {{ item.groupName }}
          </div>
        </el-col>
        <div class="layout-form-view layout-border-radius mb10">
          <el-col :span="24" class="mb0">
            <CustomFieldForm
              :ref="
                (el) => {
                  item.customFieldRef = el;
                }
              "
              v-if="item.groupType == 2 && item.columnList.length > 0"
              :colSpan="3"
              :columnList="item.columnList"
            />
            <CustomFieldTable
              :ref="
                (el) => {
                  item.customFieldRef = el;
                }
              "
              v-else-if="item.groupType == 1 && item.columnList.length > 0"
              :colSpan="3"
              :columnList="item.columnList"
              :dataList="[]"
            />
            <el-empty v-else description="该目录暂无元数据字段" />
          </el-col>
        </div>
      </div>
    </div>
    <el-dialog
      v-model="mapMakerVisible"
      :width="1200"
      title="地图拾取"
      align-center
      destroy-on-close
      :show-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="mapMakerVisible = false"
    >
      <!-- <Das3d @mapClick="onMapClick" type="point" /> -->
      <LeafletMap @mapClick="onMapClick" style="height: 650px" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useMessage, useMessageBox } from "/@/hooks/message";
import { securityLevelConfig } from "/@/config/resourceConfig";
import { fetchList as fetchList_security } from "/@/api/resource/security/level";
import { addModelData, getAllColumns } from "/@/api/resource/data/resource";
const emit = defineEmits(["backTo"]);
const dataFormRef = ref();
const loading = ref(false);
const LeafletMap = defineAsyncComponent(
  () => import("/@/components/LeafletMap/index.vue")
);
const ImageUpload = defineAsyncComponent(() => import("/@/components/Upload/Image.vue"));
const CustomFieldForm = defineAsyncComponent(() => import("../edit/CustomFieldForm.vue"));
const CustomFieldTable = defineAsyncComponent(
  () => import("../edit/CustomFieldTable.vue")
);
const props = defineProps({
  editRecord: {
    type: Object,
    default: null,
  },
});
const mapMakerVisible = ref(false);
const columnInfoList: any = ref([]);
const securityList: any = ref([]);
// 提交表单数据
const form: any = reactive({
  assets_cover: "",
  assets_name: "",
  public_state: "",
  security_id: "",
  assets_tag: "",
  assets_remark: "",
  longitude: "",
  latitude: "",
  location_remark: "",
});
// 定义校验规则
const dataRules = ref({
  assets_cover: [{ required: true, message: "资源封面不能为空", trigger: "change" }],
  assets_name: [{ required: true, message: "资源名称不能为空", trigger: "change" }],
  public_state: [{ required: true, message: "公开状态不能为空", trigger: "blur" }],
  security_id: [{ required: true, message: "资源密级不能为空", trigger: "blur" }],
});
const backTo = () => {
  emit("backTo");
};

onMounted(async () => {
  let res_AllColumns = await getAllColumns({ tableId: props.editRecord.tableId });
  columnInfoList.value = (res_AllColumns?.data || []).filter(item => item.groupId != 1);
  let res_security = await fetchList_security({ enabled: 1 });
  res_security.data.forEach((item: any) => {
    item.title = item.securityName + "（" + securityLevelConfig[item.sortOrder] + "）";
  });
  securityList.value = res_security.data;
  // 重置表单数据
  nextTick(() => {
    dataFormRef.value?.resetFields();
  });
});
// 提交
const onSubmit = async () => {
  const valid = await dataFormRef.value.validate().catch(() => {});
  if (!valid) return false;
  let securityOBJ = securityList.value.find((obj: any) => {
    return obj.id == form.security_id;
  });
  let obj_add: any = {
    tableName: props.editRecord.tableName,
    datas: {},
    extraMap: {
      third_catalog_no: props.editRecord.third_catalog_no,
      security_name: securityOBJ.securityName,
    },
    groupMap: {},
  };
  form.security_id = form.security_id;
  form.longitude = String(form.longitude);
  form.latitude = String(form.latitude);
  Object.assign(obj_add.datas, form);
  for (let i in columnInfoList.value) {
    let obj: any = columnInfoList.value[i];
    let isvalid = await obj.customFieldRef.validate();
    if (!isvalid) return false;
    let data = JSON.parse(JSON.stringify(obj.customFieldRef.getData()));
    if (obj.groupType == 2) {
      for (let k in data) {
        if (Array.isArray(data[k])) data[k] = JSON.stringify(data[k]);
      }
      // 表单
      Object.assign(obj_add.datas, data);
    } else {
      data.forEach((obj: any) => {
        for (let k in obj) {
          if (Array.isArray(obj[k])) obj[k] = JSON.stringify(obj[k]);
        }
      });
      // 列表
      obj_add.groupMap[obj.groupId] = data;
    }
  }
  try {
    loading.value = true;
    await addModelData(obj_add);
    useMessage().success("添加成功");
    emit("backTo");
  } catch (err) {
    useMessage().error((err as any).msg);
  } finally {
    loading.value = false;
  }
};
const onMapClick = (coord: Array<number>) => {
  // form.zb = coord.join(",");
  form.longitude = coord[0];
  form.latitude = coord[1];
};
</script>

<style scoped lang="scss">
.map-maker-box {
  font-weight: bolder;
  cursor: pointer;
  background-color: #ffeded;
  color: #fd4949;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px 6px;
  border-radius: 6px;
  border: 1px solid #ff0000;
  position: absolute;
  right: 15px;
  margin-top: -49px;
  &:hover {
    color: #ff0000;
  }
  span {
    margin-left: 5px;
  }
}
:deep(.custom-upload) {
  .el-upload__tip {
    margin-top: 0 !important;
  }
}
</style>
