import request from "/@/utils/request"
import { IPlan } from './interface'

// 查询巡查计划列表
export function queryPartolPlanList(params: IPlan) {
  return request({
    url: '/check/patrolPlan/planList',
    method: 'get',
    params
  })
}

// 新增巡查计划
export function addPartolPlan(data: any) {
  return request({
    url: '/check/patrolPlan/add',
    method: 'post',
    data
  })
}


// 编辑巡查计划
export function editPartolPlan(data: any) {
  return request({
    url: '/check/patrolPlan/edit',
    method: 'post',
    data
  })
}


// 删除巡查计划列表
export function delPartolPlan(params: any) {
  return request({
    url: '/check/patrolPlan/delById',
    method: 'get',
    params
  })
}


// 查询计划路径
export function queryPlanPath(params: any) {
  return request({
    url: '/check/patrolPlan/planPosition',
    method: 'get',
    params
  })
}

// 查询巡查计划详情
export function queryPlanDetail(params: any) {
  return request({
    url: '/check/patrolPlan/planInfoById',
    method: 'get',
    params
  })
}

// 查询巡查区域
export function queryPartolArea(params: any) {
  return request({
    url: '/admin/sysArchitecturalSpace/page',
    method: 'get',
    params
  })
}


// 查询点位信息
export function queryPointList(params: any) {
  return request({
    url: '/admin/sysArchitecturalSpace/getArchitecturalSpaceTree',
    method: 'get',
    params
  })
}

// http://localhost:9999/admin/sysArchitecturalSpace/getArchitecturalSpaceTree?loadArcSpace=true