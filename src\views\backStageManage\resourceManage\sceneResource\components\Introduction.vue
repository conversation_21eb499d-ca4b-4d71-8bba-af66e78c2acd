<template>
	<div class="introduction-container">
		<div class="form-content">
			<el-form ref="formRef" :model="formData" :rules="rules" label-width="120px" class="resource-form">
				<!-- 基本信息 -->
				<div class="form-section">
					<div class="section-title">
						<img src="/@/assets/img/backManage/Union.png" alt="基本信息" class="section-icon" />
						基本信息
					</div>
					<el-form-item label="资源封面" prop="cover">
						<ImageUpload
							class="custom-upload"
							v-model:imageUrl="formData.cover"
							borderRadius="6px"
							width="120px"
							height="90px"
							uploadFileUrl="/datacenter/learning/material/cover"
						>
							<template #empty>
								<el-icon>
									<Plus />
								</el-icon>
								<span>请上传封面</span>
							</template>
						</ImageUpload>
						<div class="upload-tip" style="padding-left: 10px">图片支持JPG/PNG格式且最大5M</div>
					</el-form-item>
					<el-form-item label="资源名称" prop="resourceName">
						<el-input v-model="formData.resourceName" maxlength="50" show-word-limit placeholder="请输入资源名称" />
					</el-form-item>

					<el-form-item label="资源类型" prop="resourceType">
						<el-select v-model="formData.resourceType" placeholder="请选择资源类型" style="width: 100%">
							<el-option label="附属建筑" value="1" />
							<el-option label="文物" value="2" />
							<el-option label="文化资源" value="3" />
						</el-select>
					</el-form-item>

					<el-form-item label="资源标签" prop="labels">
						<el-input v-model="formData.labels" maxlength="50" show-word-limit placeholder="请输入资源标签，多个标签用逗号分隔" />
					</el-form-item>

					<el-form-item label="资源描述" prop="description">
						<el-input v-model="formData.description" placeholder="请输入资源描述" type="textarea" :rows="3" maxlength="500" show-word-limit />
					</el-form-item>
				</div>

				<!-- 文件信息 -->
				<div class="form-section">
					<div class="section-title">
						<img src="/@/assets/img/backManage/Union.png" alt="文件上传" class="section-icon" />
						文件上传
					</div>
					<el-form-item label="文件上传">
						<div class="file-upload-area">
							<el-upload
								ref="uploadRef"
								class="resource-uploader"
								drag
								:http-request="handleCustomUpload"
								:file-list="formData.fileList"
								:on-success="handleUploadSuccess"
								:before-upload="beforeUpload"
								:on-change="handleFileChange"
								:disabled="uploadProgress.isUploading"
								:show-file-list="false"
							>
								<!-- 未上传状态 -->
								<div class="upload-content" v-if="!uploadProgress.isUploading && !formData.uploadedFile">
									<div class="upload-text">
										<p>将文件拖拽到此处，或<span class="upload-link">点击上传</span></p>
									</div>
								</div>

								<!-- 上传进度展示 -->
								<div class="upload-progress" v-if="uploadProgress.isUploading">
									<div class="progress-content">
										<div class="progress-text">
											<span class="file-name">{{ uploadProgress.fileName }}</span>
											<span class="progress-percent">{{ uploadProgress.percentage }}%</span>
										</div>
										<el-progress :percentage="uploadProgress.percentage" :show-text="false" />
									</div>
								</div>

								<!-- 已上传文件信息 -->
								<div class="uploaded-file-info" v-if="!uploadProgress.isUploading && formData.uploadedFile">
									<div class="file-icon">
										<el-icon size="24">
											<Document />
										</el-icon>
									</div>
									<div class="file-details">
										<div class="file-name">{{ formData.uploadedFile.name }}</div>
									</div>
									<div class="file-actions">
										<el-button type="danger" size="small" :icon="Delete" circle @click.stop="handleRemoveFile" />
									</div>
								</div>
							</el-upload>

							<!-- 上传提示信息 -->
							<div class="upload-tips">
								<p>1.三维模型请将3DTiles或手工模型的zip格式压缩文件整体上传</p>
								<p>2.总大小不超过10GB</p>
							</div>
						</div>
					</el-form-item>
				</div>
			</el-form>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, defineAsyncComponent } from 'vue';
import { Plus, Document, Delete } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { uploadResourceFile } from '/@/api/backStageManage/resource';

const ImageUpload = defineAsyncComponent(() => import('/@/components/Upload/Image.vue'));

interface Props {
	data: any;
}

interface Emits {
	(e: 'update', field: string, value: any): void;
}

const props = defineProps<Props>();

const emit = defineEmits<Emits>();

const uploadRef = ref();
// 表单数据
const formData = reactive({
	resourceName: '',
	resourceType: '',
	resourceTypeName: '',
	buildingId: '',
	buildingName: '',
	labels: '',
	cover: '',
	description: '',
	fileList: [] as Array<any>,
	modelId: '',
	uploadedFile: null as any,
});

// 上传进度状态
const uploadProgress = reactive({
	isUploading: false,
	percentage: 0,
	fileName: '',
});

// 表单引用
const formRef = ref();

// 表单验证规则
const rules = {
	resourceName: [{ required: true, message: '请输入资源名称', trigger: 'blur' }],
	resourceType: [{ required: true, message: '请选择资源类型', trigger: 'change' }],
	labels: [{ required: true, message: '请输入资源标签', trigger: 'blur' }],
};

// 监听props数据变化
watch(
	() => props.data,
	(newData) => {
		if (newData) {
			// 映射API数据结构到表单数据
			Object.assign(formData, {
				resourceName: newData.resourceName || '',
				resourceType: newData.resourceType || '',
				resourceTypeName: newData.resourceTypeName || '',
				buildingId: newData.buildingId || '',
				buildingName: newData.buildingName || '',
				labels: newData.labels || '',
				cover: newData.cover || '',
				description: newData.description || '',
				fileList: newData.model
					? [
							{
								id: newData.model.id,
								name: newData.model.mainFile + '.' + newData.model.fileExtension,
								url: newData.model.url,
							},
					  ]
					: [],
				modelId: newData.modelId,
				uploadedFile: newData.model ? {
					id: newData.model?.id || '',
					url: newData.model.url,
					name: newData.model?.mainFile + '.' + newData.model?.fileExtension || '',
				} : null,
			});
		}
	},
	{ immediate: true, deep: true }
);

// 监听表单数据变化，向父组件发送更新事件
// watch(formData, (newData) => {
//   Object.keys(newData).forEach(key => {
//     emit('update', key, (newData as any)[key])
//   })
// }, { deep: true })

// 自定义文件上传处理
const handleCustomUpload = async (options: any) => {
	console.log(1111111111111111111111111)
	try {
		// 开始上传，设置进度状态
		uploadProgress.isUploading = true;
		uploadProgress.percentage = 0;
		uploadProgress.fileName = options.file.name;

		const uploadData = {
			// 这里可以添加额外的上传参数
		};

		// 模拟上传进度（因为实际API可能不支持进度回调）
		const progressInterval = setInterval(() => {
			if (uploadProgress.percentage < 90) {
				uploadProgress.percentage += Math.random() * 20;
				if (uploadProgress.percentage > 90) {
					uploadProgress.percentage = 90;
				}
			}
		}, 200);

		const response = await uploadResourceFile(options.file, uploadData);

		// 清除进度定时器
		clearInterval(progressInterval);

		// 完成上传进度
		uploadProgress.percentage = 100;

		// 延迟一下让用户看到100%
		await new Promise((resolve) => setTimeout(resolve, 500));

		// 单文件上传，替换现有文件
		formData.fileList = [
			{
				name: options.file.name,
				url: response.data?.url || response.data?.filePath,
				status: 'success',
				uid: options.file.uid || Date.now(),
			},
		];
		formData.modelId = response.data?.id || '';

		// 设置已上传文件信息
		formData.uploadedFile = {
			name: options.file.name,
			size: options.file.size,
			url: response.data?.url || response.data?.filePath,
			id: response.data?.id || '',
		};

		// 重置上传状态
		uploadProgress.isUploading = false;
		uploadProgress.percentage = 0;
		uploadProgress.fileName = '';

		ElMessage.success('文件上传成功');
		options.onSuccess(response);
	} catch (err) {
		// 重置上传状态
		uploadProgress.isUploading = false;
		uploadProgress.percentage = 0;
		uploadProgress.fileName = '';

		ElMessage.error('文件上传失败');
		options.onError(err);
	}
};

const handleUploadSuccess = () => {
	// 这个方法现在主要用于兼容，实际上传逻辑在handleCustomUpload中
};

const beforeUpload = (file: File) => {
	const isLt50M = file.size / 1024 / 1024 < 50 * 200;
	if (!isLt50M) {
		ElMessage.error('文件大小不能超过 10GB!');
		return false;
	}
	return true;
};

// 移除已上传的文件
const handleRemoveFile = () => {
	formData.uploadedFile = null;
	formData.fileList = [];
	formData.modelId = '';
};

const handleFileChange = () => {
	uploadRef.value.clearFiles();
};

// 获取表单数据
const getData = async () => {
	try {
		const valid = await formRef.value.validate().catch(() => {});
		if (!valid) throw new Error('表单验证失败');
		return { ...formData };
	} catch (error) {
		throw new Error('表单验证失败');
	}
};

// 暴露方法给父组件
defineExpose({
	getData,
});
</script>

<style lang="scss" scoped>
.introduction-container {
	height: 100%;
	overflow: auto;

	.form-content {
		padding: 20px;
	}

	.resource-form {
		max-width: 1200px;
	}

	.form-section {
		margin-bottom: 32px;

		.section-title {
			font-size: 16px;
			font-weight: 500;
			color: #303133;
			margin-bottom: 16px;
			padding-bottom: 8px;
			border-bottom: 1px solid #e4e7ed;
			display: flex;
			align-items: center;

			.section-icon {
				width: 16px;
				height: 16px;
				margin-right: 8px;
			}
		}
	}

	.file-upload-area {
		.resource-uploader {
			width: 100%;

			:deep(.el-upload-dragger) {
				width: 100%;
				height: 100px;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				border: 1px dashed #d9d9d9;
				border-radius: 8px;
				background: #fafafa;
				transition: all 0.3s;

				&:hover {
					border-color: var(--el-color-primary, #a12f2f);
				}

				.upload-content {
					text-align: center;

					.upload-icon {
						font-size: 48px;
						color: #c0c4cc;
						margin-bottom: 16px;
						transition: color 0.3s;
					}

					.upload-text {
						p {
							font-size: 14px;
							color: #606266;

							.upload-link {
								color: var(--el-color-primary, #a12f2f);
								cursor: pointer;

								&:hover {
									text-decoration: underline;
								}
							}
						}

						.upload-tip {
							margin-left: 10px;
							font-size: 12px;
							color: #909399;
							text-align: left;
						}
					}
				}

				// 已上传文件信息样式
				.uploaded-file-info {
					display: flex;
					align-items: center;
					padding: 16px;
					width: 100%;

					.file-icon {
						margin-right: 12px;
						color: #409eff;
					}

					.file-details {
						flex: 1;

						.file-name {
							font-size: 14px;
							font-weight: 500;
							color: #303133;
							margin-bottom: 4px;
							word-break: break-all;
						}

						.file-size {
							font-size: 12px;
							color: #909399;
						}
					}

					.file-actions {
						margin-left: 12px;
					}
				}

				&:hover .upload-icon {
					color: var(--el-color-primary, #a12f2f);
				}
			}

			// 上传进度样式
			.upload-progress {
				display: flex;
				align-items: center;
				justify-content: center;
				flex-direction: column;
				height: 140px;
				padding: 20px;

				.progress-icon {
					font-size: 32px;
					color: var(--el-color-primary, #a12f2f);
					margin-bottom: 16px;
					animation: rotate 2s linear infinite;
				}

				.progress-content {
					width: 100%;
					max-width: 300px;

					.progress-text {
						display: flex;
						justify-content: space-between;
						align-items: center;
						margin-bottom: 8px;
						font-size: 14px;

						.file-name {
							color: #303133;
							font-weight: 500;
							flex: 1;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
							margin-right: 12px;
						}

						.progress-percent {
							color: var(--el-color-primary, #a12f2f);
							font-weight: 600;
							min-width: 40px;
							text-align: right;
						}
					}
				}
			}

			@keyframes rotate {
				from {
					transform: rotate(0deg);
				}

				to {
					transform: rotate(360deg);
				}
			}
		}

		// 上传提示样式
		.upload-tips {
			margin-top: 12px;
			padding: 8px 0;

			p {
				margin: 0 0 4px 0;
				font-size: 12px;
				color: #909399;
				line-height: 1.4;

				&:last-child {
					margin-bottom: 0;
				}
			}
		}
	}
}
</style>
