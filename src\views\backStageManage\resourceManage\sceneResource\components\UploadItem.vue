<template>
	<div class="upload-item-container">
		<div class="upload-item">
			<!-- 左侧文件信息区域 -->
			<div class="file-info">
				<div class="file-name" v-if="displayFileInfo.name">
					{{ displayFileInfo.name }}
				</div>
				<div class="placeholder-text" v-else>
					{{ placeholderText }}
				</div>
				<div class="file-progress">
					<!-- 上传进度 -->
					<div class="progress-container" v-if="uploading">
						<el-progress :percentage="uploadProgress" :width="15" type="circle" :stroke-width="3" :show-text="false" />
						<span class="progress-text">{{ uploadProgress }}%</span>
					</div>

					<!-- 删除按钮 -->
					<el-button v-else-if="displayFileInfo.name && !uploading" type="danger" size="small" :icon="Delete" circle @click="handleDelete" />
				</div>
			</div>

			<!-- 右侧操作区域 -->
			<div class="file-actions">
				<!-- 上传按钮 -->
				<el-upload
					ref="uploadRef"
					:auto-upload="false"
					:show-file-list="false"
					:on-change="handleFileChange"
					:before-upload="beforeUpload"
					:accept="acceptTypes"
					style="display: inline-block"
				>
					<el-button :disabled="disabled">
						{{ displayFileInfo.name ? '重新上传' : '选择文件' }}
					</el-button>
				</el-upload>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { Delete } from '@element-plus/icons-vue';
import type { UploadFile, UploadRawFile } from 'element-plus';
import { uploadResourceFile } from '/@/api/backStageManage/resource';

interface Props {
	// 文件类型：image, audio, video, model
	fileType?: 'image' | 'audio' | 'video' | 'model';
	// 文件大小限制(MB)
	maxSize?: number;
	// 上传时的额外参数
	uploadData?: Record<string, any>;
	// 是否禁用
	disabled?: boolean;
	// 文件信息（用于回显）
	fileInfo?: {
		id?: string;
		name?: string;
		url?: string;
		size?: number;
		type?: string;
	};
}

interface Emits {
	// 上传成功回调
	(e: 'upload-success', data: any): void;
	// 上传失败回调
	(e: 'upload-error', error: any): void;
	// 删除文件回调
	(e: 'delete-file'): void;
}

const props = withDefaults(defineProps<Props>(), {
	fileType: 'image',
	maxSize: 1024, // 默认1024MB
	uploadData: () => ({}),
	disabled: false,
	fileInfo: () => ({}),
});

const emit = defineEmits<Emits>();

// 文件类型配置
const fileTypeConfig = {
	image: {
		accept: 'image/*',
		extensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
		placeholder: '请选择图片文件',
	},
	audio: {
		accept: 'audio/*',
		extensions: ['mp3', 'wav', 'ogg', 'aac', 'flac'],
		placeholder: '请选择音频文件',
	},
	video: {
		accept: 'video/*',
		extensions: ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'],
		placeholder: '请选择视频文件',
	},
	model: {
		accept: '.zip,.obj,.fbx,.gltf,.glb',
		extensions: ['zip', 'obj', 'fbx', 'gltf', 'glb'],
		placeholder: '请选择模型文件',
	},
};

// 计算属性
const acceptTypes = computed(() => fileTypeConfig[props.fileType].accept);
const placeholderText = computed(() => fileTypeConfig[props.fileType].placeholder);
const allowedExtensions = computed(() => fileTypeConfig[props.fileType].extensions);

// 显示的文件信息（优先显示回显的文件信息，其次是内部文件信息）
const displayFileInfo = computed(() => {
	// 如果有回显的文件信息且有名称，则显示回显信息
	if (props.fileInfo && props.fileInfo.name) {
		return props.fileInfo;
	}
	// 否则显示内部文件信息
	return internalFileInfo;
});

// 上传组件引用
const uploadRef = ref();

// 内部文件信息
const internalFileInfo = reactive({
	name: '',
	size: 0,
	type: '',
	file: null as File | null,
});

// 上传状态
const uploading = ref(false);
const uploadProgress = ref(0);
const uploadedSize = ref(0);
const uploadStatus = ref<'success' | 'exception' | undefined>(undefined);

// 上传结果
const uploadResult = ref<{
	success: boolean;
	message?: string;
	data?: any;
} | null>(null);

// 文件选择处理
const handleFileChange = (file: UploadFile) => {
	if (!file.raw) return;

	// 重置状态
	resetUploadState();

	// 设置文件信息
	internalFileInfo.name = file.name;
	internalFileInfo.size = file.size || 0;
	internalFileInfo.type = file.raw.type;
	internalFileInfo.file = file.raw;

	// 自动开始上传
	startUpload();
};

// 上传前验证
const beforeUpload = (file: UploadRawFile) => {
	// 检查文件大小
	if (props.maxSize && file.size > props.maxSize * 1024 * 1024) {
		ElMessage.error(`文件大小不能超过 ${props.maxSize}MB`);
		return false;
	}

	// 检查文件类型
	const fileExt = file.name.split('.').pop()?.toLowerCase();
	if (fileExt && !allowedExtensions.value.includes(fileExt)) {
		ElMessage.error(`只支持 ${allowedExtensions.value.join(', ')} 格式的文件`);
		return false;
	}

	return true;
};

// 开始上传
const startUpload = async () => {
	if (!internalFileInfo.file) return;

	uploading.value = true;
	uploadProgress.value = 0;
	uploadedSize.value = 0;
	uploadStatus.value = undefined;
	uploadResult.value = null;

	try {
		// 模拟上传进度
		const progressInterval = setInterval(() => {
			if (uploadProgress.value < 90) {
				uploadProgress.value += Math.random() * 10;
				uploadedSize.value = (internalFileInfo.size * uploadProgress.value) / 100;
			}
		}, 200);

		// 调用上传API
		const response = await uploadResourceFile(internalFileInfo.file, props.uploadData);

		// 清除进度定时器
		clearInterval(progressInterval);

		// 完成上传
		uploadProgress.value = 100;
		uploadedSize.value = internalFileInfo.size;
		uploadStatus.value = 'success';

		// 延迟显示成功状态
		setTimeout(() => {
			uploading.value = false;
			uploadResult.value = {
				success: true,
				data: response.data,
			};

			// 触发成功回调
			emit('upload-success', response.data);
		}, 500);
	} catch (error: any) {
		uploading.value = false;
		uploadStatus.value = 'exception';
		uploadResult.value = {
			success: false,
			message: error.message || '上传失败',
		};

		// 触发失败回调
		emit('upload-error', error);
	}
};

// 删除文件
const handleDelete = () => {
	resetFileInfo();
	resetUploadState();
	emit('delete-file');
};

// 重置文件信息
const resetFileInfo = () => {
	internalFileInfo.name = '';
	internalFileInfo.size = 0;
	internalFileInfo.type = '';
	internalFileInfo.file = null;
};

// 重置上传状态
const resetUploadState = () => {
	uploading.value = false;
	uploadProgress.value = 0;
	uploadedSize.value = 0;
	uploadStatus.value = undefined;
	uploadResult.value = null;
};

// 暴露方法给父组件
defineExpose({
	// 清空文件
	clearFile: () => {
		resetFileInfo();
		resetUploadState();
	},
	// 获取文件信息
	getFileInfo: () => internalFileInfo,
	// 获取上传状态
	getUploadStatus: () => ({
		uploading: uploading.value,
		progress: uploadProgress.value,
		result: uploadResult.value,
	}),
});
</script>

<style lang="scss" scoped>
.upload-item-container {
	width: 100%;

	.upload-item {
		display: flex;
		align-items: center;
		background: #fff;
		min-height: 40px;

		.file-info {
			flex: 1;
			margin-right: 12px;
			border: 1px solid #e4e7ed;
			border-radius: 4px;
			padding: 0 12px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			height: 32px;

			.file-name {
				font-size: 14px;
				color: #303133;
				word-break: break-all;
				line-height: 1.4;
			}

			.placeholder-text {
				font-size: 14px;
				color: #c0c4cc;
			}
			.file-progress {
				.el-button {
					width: 18px;
					height: 18px;
				}
			}
			.progress-container {
				display: flex;
				align-items: center;
				gap: 4px;

				.progress-text {
					font-size: 14px;
					color: #909399;
					white-space: nowrap;
				}
			}
		}

		.file-actions {
			display: flex;
			align-items: center;
			gap: 8px;
			:deep(.el-upload) {
				display: inline-block;
			}
		}
	}
}
</style>
