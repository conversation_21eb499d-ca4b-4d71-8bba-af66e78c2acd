<template>
  <div class="upload-item-container">
    <!-- 上传区域 -->
    <div class="upload-area" v-if="!fileInfo.name">
      <el-upload
        ref="uploadRef"
        class="upload-dragger"
        drag
        :auto-upload="false"
        :show-file-list="false"
        :on-change="handleFileChange"
        :before-upload="beforeUpload"
        accept="*"
      >
        <div class="upload-content">
          <el-icon class="upload-icon">
            <UploadFilled />
          </el-icon>
          <div class="upload-text">
            <p>将文件拖拽到此处，或<span class="upload-link">点击上传</span></p>
            <p class="upload-tip" v-if="tip">{{ tip }}</p>
          </div>
        </div>
      </el-upload>
    </div>

    <!-- 文件信息显示区域 -->
    <div class="file-info-area" v-else>
      <div class="file-item">
        <div class="file-icon">
          <el-icon size="24">
            <Document />
          </el-icon>
        </div>
        <div class="file-details">
          <div class="file-name">{{ fileInfo.name }}</div>
          <div class="file-size">{{ formatFileSize(fileInfo.size) }}</div>
        </div>
        <div class="file-actions">
          <el-button
            type="danger"
            size="small"
            :icon="Delete"
            circle
            @click="handleDelete"
            :disabled="uploading"
          />
        </div>
      </div>

      <!-- 上传进度条 -->
      <div class="progress-area" v-if="uploading">
        <el-progress
          :percentage="uploadProgress"
          :status="uploadStatus"
          :stroke-width="6"
        />
        <div class="progress-text">
          {{ uploadProgress }}% ({{ formatFileSize(uploadedSize) }}/{{ formatFileSize(fileInfo.size) }})
        </div>
      </div>

      <!-- 上传状态 -->
      <div class="status-area" v-if="!uploading && uploadResult">
        <div class="status-success" v-if="uploadResult.success">
          <el-icon color="#67c23a">
            <CircleCheck />
          </el-icon>
          <span>上传成功</span>
        </div>
        <div class="status-error" v-else>
          <el-icon color="#f56c6c">
            <CircleClose />
          </el-icon>
          <span>上传失败: {{ uploadResult.message }}</span>
          <el-button type="text" size="small" @click="retryUpload">重试</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  UploadFilled,
  Document,
  Delete,
  CircleCheck,
  CircleClose
} from '@element-plus/icons-vue'
import type { UploadFile, UploadRawFile } from 'element-plus'
import { uploadResourceFile } from '/@/api/backStageManage/resource'

interface Props {
  // 上传提示文字
  tip?: string
  // 文件大小限制(MB)
  maxSize?: number
  // 允许的文件类型
  allowedTypes?: string[]
  // 上传时的额外参数
  uploadData?: Record<string, any>
  // 是否禁用
  disabled?: boolean
}

interface Emits {
  // 上传成功回调
  (e: 'upload-success', data: any): void
  // 上传失败回调
  (e: 'upload-error', error: any): void
  // 删除文件回调
  (e: 'delete-file'): void
}

const props = withDefaults(defineProps<Props>(), {
  tip: '支持各种格式文件上传',
  maxSize: 100, // 默认100MB
  allowedTypes: () => [],
  uploadData: () => ({}),
  disabled: false
})

const emit = defineEmits<Emits>()

// 上传组件引用
const uploadRef = ref()

// 文件信息
const fileInfo = reactive({
  name: '',
  size: 0,
  type: '',
  file: null as File | null
})

// 上传状态
const uploading = ref(false)
const uploadProgress = ref(0)
const uploadedSize = ref(0)
const uploadStatus = ref<'success' | 'exception' | undefined>(undefined)

// 上传结果
const uploadResult = ref<{
  success: boolean
  message?: string
  data?: any
} | null>(null)

// 文件选择处理
const handleFileChange = (file: UploadFile) => {
  if (!file.raw) return
  
  // 重置状态
  resetUploadState()
  
  // 设置文件信息
  fileInfo.name = file.name
  fileInfo.size = file.size || 0
  fileInfo.type = file.raw.type
  fileInfo.file = file.raw
  
  // 自动开始上传
  startUpload()
}

// 上传前验证
const beforeUpload = (file: UploadRawFile) => {
  // 检查文件大小
  if (props.maxSize && file.size > props.maxSize * 1024 * 1024) {
    ElMessage.error(`文件大小不能超过 ${props.maxSize}MB`)
    return false
  }
  
  // 检查文件类型
  if (props.allowedTypes.length > 0) {
    const fileExt = file.name.split('.').pop()?.toLowerCase()
    if (!fileExt || !props.allowedTypes.includes(fileExt)) {
      ElMessage.error(`只支持 ${props.allowedTypes.join(', ')} 格式的文件`)
      return false
    }
  }
  
  return true
}

// 开始上传
const startUpload = async () => {
  if (!fileInfo.file) return
  
  uploading.value = true
  uploadProgress.value = 0
  uploadedSize.value = 0
  uploadStatus.value = undefined
  uploadResult.value = null
  
  try {
    // 模拟上传进度
    const progressInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += Math.random() * 10
        uploadedSize.value = (fileInfo.size * uploadProgress.value) / 100
      }
    }, 200)
    
    // 调用上传API
    const response = await uploadResourceFile(fileInfo.file, props.uploadData)
    
    // 清除进度定时器
    clearInterval(progressInterval)
    
    // 完成上传
    uploadProgress.value = 100
    uploadedSize.value = fileInfo.size
    uploadStatus.value = 'success'
    
    // 延迟显示成功状态
    setTimeout(() => {
      uploading.value = false
      uploadResult.value = {
        success: true,
        data: response.data
      }
      
      // 触发成功回调
      emit('upload-success', response.data)
    }, 500)
    
  } catch (error: any) {
    uploading.value = false
    uploadStatus.value = 'exception'
    uploadResult.value = {
      success: false,
      message: error.message || '上传失败'
    }
    
    // 触发失败回调
    emit('upload-error', error)
  }
}

// 重试上传
const retryUpload = () => {
  startUpload()
}

// 删除文件
const handleDelete = () => {
  resetFileInfo()
  resetUploadState()
  emit('delete-file')
}

// 重置文件信息
const resetFileInfo = () => {
  fileInfo.name = ''
  fileInfo.size = 0
  fileInfo.type = ''
  fileInfo.file = null
}

// 重置上传状态
const resetUploadState = () => {
  uploading.value = false
  uploadProgress.value = 0
  uploadedSize.value = 0
  uploadStatus.value = undefined
  uploadResult.value = null
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 暴露方法给父组件
defineExpose({
  // 清空文件
  clearFile: () => {
    resetFileInfo()
    resetUploadState()
  },
  // 获取文件信息
  getFileInfo: () => fileInfo,
  // 获取上传状态
  getUploadStatus: () => ({
    uploading: uploading.value,
    progress: uploadProgress.value,
    result: uploadResult.value
  })
})
</script>

<style lang="scss" scoped>
.upload-item-container {
  width: 100%;

  .upload-area {
    .upload-dragger {
      :deep(.el-upload) {
        width: 100%;
      }

      :deep(.el-upload-dragger) {
        width: 100%;
        height: 120px;
        border: 2px dashed #d9d9d9;
        border-radius: 8px;
        background: #fafafa;
        transition: all 0.3s;

        &:hover {
          border-color: #409eff;
          background: #f0f9ff;
        }
      }

      .upload-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;

        .upload-icon {
          font-size: 32px;
          color: #8c939d;
          margin-bottom: 12px;
        }

        .upload-text {
          text-align: center;

          p {
            margin: 0;
            font-size: 14px;
            color: #606266;
            line-height: 1.5;

            &.upload-tip {
              font-size: 12px;
              color: #909399;
              margin-top: 4px;
            }
          }

          .upload-link {
            color: #409eff;
            cursor: pointer;

            &:hover {
              text-decoration: underline;
            }
          }
        }
      }
    }
  }

  .file-info-area {
    .file-item {
      display: flex;
      align-items: center;
      padding: 12px;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      background: #fff;

      .file-icon {
        margin-right: 12px;
        color: #409eff;
      }

      .file-details {
        flex: 1;

        .file-name {
          font-size: 14px;
          font-weight: 500;
          color: #303133;
          margin-bottom: 4px;
          word-break: break-all;
        }

        .file-size {
          font-size: 12px;
          color: #909399;
        }
      }

      .file-actions {
        margin-left: 12px;
      }
    }

    .progress-area {
      margin-top: 12px;

      .progress-text {
        font-size: 12px;
        color: #909399;
        text-align: center;
        margin-top: 8px;
      }
    }

    .status-area {
      margin-top: 12px;

      .status-success,
      .status-error {
        display: flex;
        align-items: center;
        font-size: 14px;

        .el-icon {
          margin-right: 8px;
        }
      }

      .status-success {
        color: #67c23a;
      }

      .status-error {
        color: #f56c6c;

        .el-button {
          margin-left: 8px;
        }
      }
    }
  }
}
</style>
