import request from "/@/utils/request";

/**
 * 档案鉴定列表
 *
 */
export function getListArchiveIdentify(params: any) {
  return request({
    url: "/datacenter/archive/identify/page",
    method: "get",
    params: params,
  });
}
/**
 * 获取某条档案是否有未处理的鉴定
 *
 */
export function getListUnhandledArchiveIdentify(params: any) {
  return request({
    url: '/datacenter/archive/identify/unhandled',
    method: 'get',
    params: params,
  });
}
/**
 * 对档案进行鉴定
 *
 */
export function addArchiveIdentify(data: any) {
  return request({
    url: '/datacenter/archive/identify/add',
    method: 'post',
    data
  });
}
/**
 * 对档案进行鉴定
 *
 */
export function handleArchiveIdentify(data: any) {
  return request({
    url: '/datacenter/archive/identify/handle',
    method: 'post',
    data
  });
}
