<template>
  <div style="width: 100%; height: calc(100% - 70px); position: relative;">
    <div class="custom-box">
      <fileManagement :resuorceRecord="editRecord" :editCode="editCode" @refresh="fileRefresh"/>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from "vue";
  const emit = defineEmits(["backTo","fileRefresh"]);
  // 引入组件
  const fileManagement = defineAsyncComponent(() => import("../../fileManagement/index.vue"));
  const props = defineProps({
    resuorceData: {
      type: Array,
      default: [],
    },
    deptData: {
      type: Array,
      default: [],
    },
    editCode: {
      type: String,
      default: 'view',
    },
    editRecord: {
      type: Object,
      default: null,
    },
  });
  const loading = ref(false);
  const backTo = () => {
    emit("backTo");
  };
  const fileRefresh = () => {
    emit("fileRefresh");
  };
  onMounted(() => {
    // 重置表单数据
    nextTick(() => {});
  });
</script>

<style scoped lang="scss">
  .custom-box {
    position: relative;
    width: calc(100% + 20px);
    height: calc(100% - 0px);
    left: -15px;
    top: 20px;
    ::v-deep(.mt-title) {
      margin: 5px 0px;
    }
    ::v-deep(.info-box) {
      .ib-item {
        height: 38px;
        line-height: 38px;
      }
    }
  }
</style>
