import request from "/@/utils/request";

// 查询场景列表
export function getSceneList(params?: Object) {
    return request({
        url: "/exhibition/scene/page",
        method: "get",
        params: params,
    });
}

// 查询场景详情
export function getSceneDetail(id?: string) {
    return request({
        url: "/exhibition/scene/" + id,
        method: "get",
    });
}

// 新增场景
export function addScene(data?: Object) {
    return request({
        url: "/exhibition/scene",
        method: "post",
        data: data,
    });
}

// 更新场景
export function updateScene(data?: Object) {
    return request({
        url: "/exhibition/scene",
        method: "put",
        data: data,
    });
}

// 删除场景
export function delScene(id: any) {
    return request({
        url: `/exhibition/scene/${id}`,
        method: "delete"
    });
}

// 更新场景状态
export function updateSceneStatus(data?: Object) {
    return request({
        url: "/exhibition/scene/updateStatus",
        method: "put",
        data: data,
    });
}

// 场景配置-漫游列表
export function getSceneRoamTree(params?: Object) {
    return request({
        url: "/exhibition/sceneRoam/simpleSceneRoamTree",
        method: "get",
        params,
    });
}

// 通过id查询场景漫游
export function getSceneRoamDetail(roamId?: string) {
    return request({
        url: "/exhibition/sceneRoam/" + roamId,
        method: "get",
    });
}

// 添加场景漫游章节
export function addRoamFirst(data?: Object) {
    return request({
        url: "/exhibition/sceneRoam/addRoamFirst",
        method: "post",
        data,
    });
}

// 添加宫观
export function addBatchRoamSecond(data?: Object) {
    return request({
        url: "/exhibition/sceneRoam/addBatchRoamSecond",
        method: "post",
        data,
    });
}

// 添加节点
export function addBatchRoamThird(data?: Object) {
    return request({
        url: "/exhibition/sceneRoam/addBatchRoamThird",
        method: "post",
        data,
    });
}

// 更新场景漫游章节
export function updateRoamFirst(data?: Object) {
    return request({
        url: "/exhibition/sceneRoam/updateRoamFirst",
        method: "put",
        data,
    });
}

// 更新宫观
export function updateRoamSecond(data?: Object) {
    return request({
        url: "/exhibition/sceneRoam/updateRoamSecond",
        method: "put",
        data,
    });
}

// 更新节点
export function updateRoamThird(data?: Object) {
    return request({
        url: "/exhibition/sceneRoam/updateRoamThird",
        method: "put",
        data,
    });
}

// 删除漫游
export function deleteRoam(roamId?: string) {
    return request({
        url: "/exhibition/sceneRoam/" + roamId,
        method: "delete",
    });
}
