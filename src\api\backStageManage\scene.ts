import request from "/@/utils/request";

// 查询场景列表
export function getSceneList(params?: Object) {
    return request({
        url: "/exhibition/scene/page",
        method: "get",
        params: params,
    });
}

// 查询场景详情
export function getSceneDetail(id?: string) {
    return request({
        url: "/exhibition/scene/" + id,
        method: "get",
    });
}

// 新增场景
export function addScene(data?: Object) {
    return request({
        url: "/exhibition/scene",
        method: "post",
        data: data,
    });
}

// 更新场景
export function updateScene(data?: Object) {
    return request({
        url: "/exhibition/scene",
        method: "put",
        data: data,
    });
}

// 删除场景
export function delScene(id: any) {
    return request({
        url: `/exhibition/scene/${id}`,
        method: "delete"
    });
}

// 更新场景状态
export function updateSceneStatus(data?: Object) {
    return request({
        url: "/exhibition/scene/updateStatus",
        method: "put",
        data: data,
    });
}