<template>
  <div class="introduction-info">
    <div class="info-content">
      <div class="editor-section">
        <h4 class="section-title">场景简介</h4>
        <el-form :model="formData" label-width="100px">
          <el-form-item label="简介内容">
            <el-input
              v-model="formData.introduction"
              type="textarea"
              :rows="6"
              placeholder="请输入场景简介内容"
              maxlength="1000"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>

      <div class="editor-section">
        <h4 class="section-title">解说词配置</h4>
        <el-form :model="formData" label-width="100px">
          <el-form-item label="解说词">
            <el-input
              v-model="formData.narration"
              type="textarea"
              :rows="8"
              placeholder="请输入解说词内容"
              maxlength="2000"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="音频文件">
            <el-upload
              class="audio-uploader"
              :show-file-list="false"
              :on-success="handleAudioSuccess"
              :before-upload="beforeAudioUpload"
              action="/api/upload"
            >
              <el-button type="primary">
                <el-icon><Upload /></el-icon>
                上传音频
              </el-button>
            </el-upload>
            <div v-if="formData.audioUrl" class="audio-preview">
              <audio :src="formData.audioUrl" controls style="width: 100%; margin-top: 12px;">
                您的浏览器不支持音频播放
              </audio>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <div class="action-buttons">
        <el-button @click="resetForm">重置</el-button>
        <el-button type="primary" @click="saveData" :loading="saving">保存</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useMessage } from '/@/hooks/message';
import { Upload } from '@element-plus/icons-vue';

interface Props {
  sceneId: string;
}

const props = defineProps<Props>();
const { success, error } = useMessage();

// 响应式数据
const saving = ref(false);
const formData = reactive({
  introduction: '',
  narration: '',
  audioUrl: '',
});

// 方法
const handleAudioSuccess = (response: any) => {
  if (response.code === 200) {
    formData.audioUrl = response.data.url;
    success('音频上传成功');
  } else {
    error('音频上传失败');
  }
};

const beforeAudioUpload = (file: File) => {
  const isAudio = file.type.startsWith('audio/');
  const isLt10M = file.size / 1024 / 1024 < 10;

  if (!isAudio) {
    error('只能上传音频文件!');
    return false;
  }
  if (!isLt10M) {
    error('音频文件大小不能超过 10MB!');
    return false;
  }
  return true;
};

const resetForm = () => {
  Object.assign(formData, {
    introduction: '',
    narration: '',
    audioUrl: '',
  });
  loadData();
};

const saveData = async () => {
  try {
    saving.value = true;
    // TODO: 调用API保存数据
    await new Promise(resolve => setTimeout(resolve, 1000));
    success('保存成功');
  } catch (err) {
    error('保存失败');
  } finally {
    saving.value = false;
  }
};

const loadData = async () => {
  try {
    // TODO: 调用API获取数据
    // 模拟数据
    Object.assign(formData, {
      introduction: '武当山主峰天柱峰，海拔1612米，是道教圣地的核心区域...',
      narration: '欢迎来到武当山主峰天柱峰，这里是道教文化的发源地...',
      audioUrl: '',
    });
  } catch (err) {
    error('获取数据失败');
  }
};

onMounted(() => {
  loadData();
});
</script>

<style scoped lang="scss">
.introduction-info {
  height: 100%;
  overflow: auto;
}

.info-content {
  padding: 24px;
}

.editor-section {
  margin-bottom: 32px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 2px solid #409eff;
    display: inline-block;
  }
}

.audio-uploader {
  :deep(.el-upload) {
    .el-button {
      .el-icon {
        margin-right: 4px;
      }
    }
  }
}

.audio-preview {
  margin-top: 12px;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}
</style>
