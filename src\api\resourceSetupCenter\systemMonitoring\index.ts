import request from '/@/utils/request';
//服务器基本信息-右侧
export function getMonitorStatus(
    data: any
  ) {
    return request({
      url: '/datacenter/monitor/status',
      method: 'get',
      data
    });
  }

  //服务器基本信息-左侧
export function getMonitorBasicInfo(
  data: any
) {
  return request({
    url: '/datacenter/monitor/basic-info',
    method: 'get',
    data
  });
}

//cpu使用率表图标
export function getMonitorCpuHistory(
  data: any
) {
  return request({
    url: '/datacenter/monitor/cpu/history',
    method: 'get',
    data
  });
}

//内存使用率图表
export function getMonitorMemoryHistory(
  data: any
) {
  return request({
    url: '/datacenter/monitor/memory/history',
    method: 'get',
    data
  });
}

//磁盘IO图表
export function getMonitorDiskHistory(
  data: any
) {
  return request({
    url: '/datacenter/monitor/disk/history',
    method: 'get',
    data
  });
}


//网络流量
export function getMonitorNetworkHistory(
  data: any
) {
  return request({
    url: '/datacenter/monitor/network/history',
    method: 'get',
    data
  });
}
