import request from "/@/utils/request"

export function fetchList(query?: Object) {
  return request({
    url: '/business/yjResponsePlan/byPage',
    method: 'get',
    params: query
  })
}

export function addObj(obj?: Object) {
  return request({
    url: '/business/yjResponsePlan',
    method: 'post',
    data: obj
  })
}

export function getObj(id?: string) {
  return request({
    url: '/business/yjResponsePlan/' + id,
    method: 'get'
  })
}

export function delObjs(ids?: Object) {
  return request({
    url: '/business/yjResponsePlan',
    method: 'delete',
    data: ids
  })
}

export function putObj(obj?: Object) {
  return request({
    url: '/business/yjResponsePlan',
    method: 'put',
    data: obj
  })
}

// 提交ocr识别任务
export function submitOcr(obj?: Object) {
  return request({
    url: '/business/yjResponsePlan/submitTask',
    method: 'post',
    data: obj,
    headers: {
      'Content-Type': 'multipart/form-data' 
    }  
  })
}

// 获取ocr识别状态
export function getOcrstatus(query?: string) {
  return request({
    url: '/business/yjResponsePlan/getResult',
    method: 'get',
    params: query
  })
}
// 获取ocr识别状态
export function getOcrResult(query?: string) {
  return request({
    url: '/business/yjResponsePlan/getResultFile',
    method: 'get',
    params: query
  })
}