import request from "/@/utils/request"

// 查询巡查事件数据报表
export function queryReportList(query?: any) {
  return request({
    url: '/check/patrolEventReport/page',
    method: 'get',
    params: query
  })
}
//导出数据
export function patrolEventReport(query?: any) {
  return request({
    url: '/check/patrolEventReport/download',
    method: 'get',
    params: query
  })
}
export function pageReportDetail(query?: any) {
  return request({
    url: '/check/patrolEventReport/pageReportDetail',
    method: 'get',
    params: query
  })
}