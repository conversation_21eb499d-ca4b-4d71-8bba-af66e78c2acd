<template>
  <div class="mt-button" style="height: 100%;">
    <div style="display: flex; flex-direction: column; height: 100%; overflow: hidden;">
      <el-table :data="tableData" style="width: 100%;" border show-overflow-tooltip
        header-cell-class-name="custom-table-header">
        <el-table-column v-for="(item, index) in columnInfoList" :key="index" :prop="item.name" :label="item.cnName"
          min-width="140px" align="center">
          <template #default="scope">
            <CustomFieldEditor v-if="props.viewMode === 'edit' && scope.row.editing && !item.columnRule?.readOnly"
              v-model="scope.row[item.name]" :columnInfo="item" />
            <span v-else-if="['dateRange', 'multiOption'].includes(item.typeCode)">{{ scope.row[item.name] ?
              scope.row[item.name].join("，") : "--" }}</span>
            <span v-else-if="['province'].includes(item.typeCode) && scope.row[item.name]?.length > 0">
              <span v-for="(code, index) in scope.row[item.name].split(',')" :key="index">
                {{ (index > 0 ? " >> " : "") + CodeToText[code] }}
              </span>
            </span>
            <span v-else-if="['image'].includes(item.typeCode)">
              <ImageUpload v-for="(img_obj, index) in scope.row[item.name]" :key="index" v-model:imageUrl="img_obj.url"
                :disabled="true" borderRadius="0%" width="120px" height="80px" style="float: left; margin-right: 15px;">
                <template #empty>
                  <el-icon>
                    <Picture />
                  </el-icon>
                  <span>请上传数据</span>
                </template>
              </ImageUpload>
            </span>
            <span v-else-if="['attach'].includes(item.typeCode) && scope.row[item.name]?.length > 0">
              <div v-for="(file_obj, index) in scope.row[item.name]" :key="index">
                {{ index + 1 + "、" + file_obj.name || "--" }}
              </div>
            </span>
            <span v-else>{{ scope.row[item.name] || "--" }}</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="130" align="center"
          v-if="props.viewMode === 'edit' && canEditor">
          <template #default="scope">
            <el-button v-if="scope.row.editing" link type="primary" size="small"
              @click.prevent="onOperation('DoSave', scope.row, scope.$index)">保存</el-button>
            <el-button v-if="!scope.row.editing" link type="primary" size="small"
              @click.prevent="onOperation('DoEdit', scope.row, scope.$index)">编辑</el-button>
            <el-button link type="danger" size="small"
              @click.prevent="onOperation('DoDelete', scope.row, scope.$index)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="width: 100%;" v-if="props.viewMode === 'edit'">
        <el-button v-if="canEditor" class="mt-4" plain icon="CirclePlusFilled" style="width: calc(100% - 115px);"
          type="primary" @click="onOperation('DoAdd')">添加记录</el-button>
        <el-button v-if="canEditor" class="mt-4" icon="CirclePlusFilled" style="width: 100px;" :disabled="!hasNoSave"
          :type="hasNoSave ? 'primary' : 'default'" @click="onOperation('DoSave', hasNoSave)">保存记录</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="upload-file">
import { useMessage, useMessageBox } from "/@/hooks/message";
import other from "/@/utils/other";
import { BasicTableProps, useTable } from "/@/hooks/table";
import { CodeToText } from "/@/utils/chinaArea";
const ImageUpload = defineAsyncComponent(() => import("/@/components/Upload/Image.vue"));
const CustomFieldEditor = defineAsyncComponent(() => import("./CustomFieldEditor.vue"));
const props = defineProps({
  colSpan: {
    type: Number,
    default: 3,
  },
  dataList: {
    type: Array as any,
    default: {},
  },
  columnList: {
    type: Array as any,
    default: () => [],
  },
  canEditor: {
    type: Boolean,
    default: true,
  },
  viewMode: {
    type: String as () => 'view' | 'edit',
    default: 'edit',
  },
});
const dataRules: any = ref({});
const columnInfoList: any = ref([]);
const tableData: any = ref([]);
const hasNoSave = computed(() => {
  return tableData.value.find((obj: any) => {
    return obj.editing;
  });
});
const state: BasicTableProps = reactive<BasicTableProps>({
  createdIsNeed: false,
  isPage: false,
});
const { tableStyle, getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile } = useTable(state);
onMounted(() => {
  columnInfoList.value = props.columnList;
  tableData.value = props.dataList;
  columnInfoList.value.forEach((obj: any) => {
    // 安全解析 columnRule
    if (obj.columnRule) {
      if (typeof obj.columnRule === 'string') {
        try {
          obj.columnRule = JSON.parse(obj.columnRule);
        } catch {
          obj.columnRule = {};
        }
      } else if (typeof obj.columnRule === 'object') {
        // 已经是对象，直接使用
        obj.columnRule = obj.columnRule;
      } else {
        obj.columnRule = {};
      }
    } else {
      obj.columnRule = {};
    }

    // 安全解析 columnCustomeRule
    if (obj.columnCustomeRule) {
      if (typeof obj.columnCustomeRule === 'string') {
        try {
          obj.columnCustomeRule = JSON.parse(obj.columnCustomeRule);
        } catch {
          obj.columnCustomeRule = {};
        }
      } else if (typeof obj.columnCustomeRule === 'object') {
        // 已经是对象，直接使用
        obj.columnCustomeRule = obj.columnCustomeRule;
      } else {
        obj.columnCustomeRule = {};
      }
    } else {
      obj.columnCustomeRule = {};
    }
    dataRules.value[obj.name] = [];
    if (obj.columnRule?.mustFilee) dataRules.value[obj.name].push({ required: obj.columnRule.mustFilee, message: obj.prompt || obj.cnName + "不能为空", trigger: "change" });
    if (["oneLine", "multiLine", "tichText"].includes(obj.typeCode)) {
      obj.columnCustomeRule.maxLength > 0 &&
        dataRules.value[obj.name].push({
          validator: (rule: any, value: any, callback: any) => {
            if (value?.length < obj.columnCustomeRule.minLength && value?.length > obj.columnCustomeRule.maxLength) {
              callback(new Error(obj.cnName + "的长度必须在" + obj.columnCustomeRule.minLength + "到" + obj.columnCustomeRule.maxLength + "之间"));
            } else {
              callback();
            }
          },
        });
      if (!obj.prompt) obj.prompt = "请输入" + obj.cnName;
    } else if (["number", "amount"].includes(obj.typeCode)) {
      if (obj.columnCustomeRule.format == "百分比" && !obj.columnCustomeRule.unit) obj.columnCustomeRule.unit = "%";
      obj.cnName += obj.columnCustomeRule.unit ? "（" + obj.columnCustomeRule.unit + "）" : "";
      obj.columnCustomeRule.range?.length > 0 &&
        dataRules.value[obj.name].push({
          validator: (rule: any, value: any, callback: any) => {
            if (value < obj.columnCustomeRule.range[0] && value > obj.columnCustomeRule.range[1]) {
              callback(new Error(obj.cnName + "的长度必须在" + obj.columnCustomeRule.range[0] + "到" + obj.columnCustomeRule.range[1] + "之间"));
            } else {
              callback();
            }
          },
        });
    } else if (["date"].includes(obj.typeCode)) {
      if (!obj.prompt) obj.prompt = "请选择" + obj.cnName;
      obj.columnCustomeRule.range?.length > 0 &&
        dataRules.value[obj.name].push({
          validator: (rule: any, value: any, callback: any) => {
            if (value && new Date(value) > new Date(obj.columnCustomeRule.range[1])) {
              callback(new Error("时间范围必须在" + obj.columnCustomeRule.range[0] + "到" + obj.columnCustomeRule.range[1] + "之间"));
            } else if (value && new Date(value) < new Date(obj.columnCustomeRule.range[0])) {
              callback(new Error("时间范围必须在" + obj.columnCustomeRule.range[0] + "到" + obj.columnCustomeRule.range[1] + "之间"));
            } else {
              callback();
            }
          },
        });
    } else if (["dateRange"].includes(obj.typeCode)) {
      if (!obj.prompt) obj.prompt = "请选择" + obj.cnName;
      obj.columnCustomeRule.range?.length > 0 &&
        dataRules.value[obj.name].push({
          validator: (rule: any, value: any, callback: any) => {
            if (value[1] && new Date(value[1]) > new Date(obj.columnCustomeRule.range[1])) {
              callback(new Error("时间范围必须在" + obj.columnCustomeRule.range[0] + "到" + obj.columnCustomeRule.range[1] + "之间"));
            } else if (value[0] && new Date(value[0]) < new Date(obj.columnCustomeRule.range[0])) {
              callback(new Error("时间范围必须在" + obj.columnCustomeRule.range[0] + "到" + obj.columnCustomeRule.range[1] + "之间"));
            } else {
              callback();
            }
          },
        });
      dataRules.value[obj.name].push({
        validator: (rule: any, value: any, callback: any) => {
          if (value[1] && new Date(value[0]) > new Date(value[1])) {
            callback(new Error("开始时间必须小于结束时间"));
          } else {
            callback();
          }
        },
      });
    } else if (["ratioOption", "multiOption"].includes(obj.typeCode)) {
      if (!obj.prompt) obj.prompt = "请选择" + obj.cnName;
    } else if (["province"].includes(obj.typeCode)) {
      if (!obj.prompt) obj.prompt = "请选择" + obj.cnName;
    } else if (["image", "attach"].includes(obj.typeCode)) {
      if (!obj.prompt) obj.prompt = "请上传" + obj.cnName;
    }
  });
});
const onOperation = async (type: string, record?: any, index?: number) => {
  switch (type) {
    case "DoAdd":
      let findItem = tableData.value.find((item: any) => {
        return item.editing;
      });
      if (findItem) {
        for (let i in columnInfoList.value) {
          let obj: any = columnInfoList.value[i];
          if (!findItem[obj.name] && obj.columnRule?.mustFilee) {
            useMessage().wraning("请先保存已有数据");
            return;
          }
        }
        findItem.editing = false;
        tableData.value.push({ editing: true });
      } else {
        tableData.value.push({ editing: true });
      }
      break;
    case "DoSave":
      for (let i in columnInfoList.value) {
        let obj: any = columnInfoList.value[i];
        if (!record[obj.name] && obj.columnRule?.mustFilee) {
          useMessage().wraning(obj.cnName + "不能为空");
          return;
        }
      }
      record.editing = false;
      break;
    case "DoEdit":
      record.editing = true;
      break;
    case "DoDelete":
      try {
        await useMessageBox().confirm("确认进行删除操作吗？");
      } catch {
        return;
      }
      tableData.value.splice(index, 1);
      break;
  }
};
const getData = () => {
  tableData.value.forEach((obj: any) => {
    delete obj.editing;
  });
  return tableData.value;
};
const validate = async () => {
  let findItem = tableData.value.find((item: any) => {
    return item.editing;
  });
  if (findItem) {
    useMessage().wraning("请先保存列表中的数据");
    return false;
  }
  return true;
};
defineExpose({
  getData,
  validate,
});
</script>
<style lang="scss" scoped>
// 自定义表头样式
:deep(.custom-table-header) {
  background-color: var(--next-bg-main-color, #f5f7fa) !important;
  color: rgba(0, 0, 0, 0.4) !important;
  font-weight: 400;
  font-size: 14px;
  height: 50px;
}
</style>
