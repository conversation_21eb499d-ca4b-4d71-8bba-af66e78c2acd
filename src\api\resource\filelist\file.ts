import request from "/@/utils/request";

// 分页查询  ?global=false&type=&sortName=&createBy=&searchValue=&folderId=1&sort=desc&publicStatus=&searchTarget=file
export function fetchList(query?: Object) {
  return request({
    url: "/datacenter/learning/material/page",
    method: "get",
    params: query,
  });
}
// 查询统计
export function getSummary(query?: Object) {
  return request({
    url: "/datacenter/learning/material/summary",
    method: "get",
    params: query,
  });
}
// 上传文件
export function upload(file?: Object) {
  return request({
    url: "/datacenter/learning/material/upload",
    method: "post",
    data: file,
  });
}
// 上传文件解析进度
export function uploadPercent(params?: Object) {
  return request({
    url: "/datacenter/learning/material/uploadPercent",
    method: "get",
    params: params,
  });
}
// 检查文件是否存在
export function checkFileUnique(params?: Object) {
  return request({
    url: "/datacenter/learning/material/checkFileUnique",
    method: "get",
    params: params,
  });
}
// 新增文件对象
export function addObj(obj?: Object) {
  return request({
    url: "/datacenter/learning/material",
    method: "post",
    data: obj,
  });
}
// 更新文件对象
export function updateObj(obj?: Object) {
  return request({
    url: "/datacenter/learning/material",
    method: "put",
    data: obj,
  });
}
// 获取对象详情
export function getObj(id?: Object, params?: Object) {
  return request({
    url: "/datacenter/learning/material/" + id,
    method: "get",
    params,
  });
}
// 删除对象
export const delObj = (id: Object) => {
  return request({
    url: "/datacenter/learning/material/" + id,
    method: "delete",
  });
};
// 批量下载
export function batchDownload(query?: Object) {
  return request({
    url: "/datacenter/learning/material/download" + (query || ""),
    method: "get",
  });
}
// 下载操作进入日志
export function actionDownload(obj?: Object) {
  return request({
    url: "/datacenter/resource/action/download",
    method: "post",
    data: obj,
  });
}
// 获取操作日志
export function operateLogList(obj?: Object) {
  return request({
    url: "/datacenter/operate/log/page",
    method: "get",
    params: obj,
  });
}


// 获取角色列表
export function getListAllUser(obj?: Object) {
  return request({
    url: "/admin/user/listAllUser",
    method: "get",
    params: obj,
  });
}
