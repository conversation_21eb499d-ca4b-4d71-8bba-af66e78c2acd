import request from '/@/utils/request';

export function queryList(query?: Object) {
	return request({
		url: '/datacenter/personal/center/resource',
		method: 'get',
		params: query,
	});
}


export function queryCatalogList(data?: Object) {
	return request({
		url: '/datacenter/catalog/listForResource',
		method: 'post',
		data,
	});
}

export function queryCatalogListForSquare(data?: Object) {
	return request({
		url: '/datacenter/catalog/listForResourceForSquare',
		method: 'post',
		data,
	});
}
export function handleUnCollect(data?: Object) {
	return request({
		url: '/datacenter/resource/action/unCollect',
		method: 'post',
		data,
	});
}


export function queryResourceInfo(params?: Object) {
	return request({
		url: '/datacenter/resource/action/summary',
		method: 'get',
		params,
	});
}

export function queryCenterMyCount(params?: Object) {
	return request({
		url: '/datacenter/personal/center/myCount',
		method: 'get',
		params,
	});
}


export function queryArchiveList(params?: Object) {
	return request({
		url: '/datacenter/personal/center/archive',
		method: 'get',
		params,
	});
}


