<template>
  <el-dialog
    v-model="dialogVisible"
    title="宫观配置"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="palace-dialog">
      <div class="dialog-content">
        <!-- 左侧：可选宫观列表 -->
        <div class="left-panel">
          <div class="panel-header">
            <span class="panel-title">宫</span>
            <el-input
              v-model="searchKeyword"
              placeholder="请输入内容"
              clearable
              @input="handleSearch"
            >
              <template #suffix>
                <el-icon class="search-icon">
                  <Search />
                </el-icon>
              </template>
            </el-input>
          </div>
          <div class="panel-content">
            <div class="building-tree">
              <div class="tree-group" v-for="group in filteredBuildingData" :key="group.id">
                <div class="group-header" @click="toggleGroup(group)">
                  <el-icon class="expand-icon" :class="{ 'expanded': group.expanded }">
                    <CaretRight />
                  </el-icon>
                  <span>{{ group.name }}</span>
                </div>
                <div class="group-content" v-show="group.expanded">
                  <div
                    class="building-item"
                    v-for="building in group.children"
                    :key="building.id"
                    @click="selectBuilding(building)"
                  >
                    <el-checkbox
                      :model-value="isSelected(building.id)"
                      @change="toggleSelection(building)"
                    />
                    <span class="building-name">{{ building.name }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 中间：操作按钮 -->
        <div class="center-panel">
          <div class="transfer-buttons">
            <el-button
              type="primary"
              :icon="ArrowRight"
              @click="addSelected"
              :disabled="selectedBuildings.length === 0"
            />
            <el-button
              :icon="ArrowLeft"
              @click="removeSelected"
              :disabled="selectedPalaces.length === 0"
            />
          </div>
        </div>

        <!-- 右侧：已选宫观列表 -->
        <div class="right-panel">
          <div class="panel-header">
            <span class="panel-title">已选</span>
            <span class="count-text">暂无数据</span>
          </div>
          <div class="panel-content">
            <div class="selected-list">
              <div
                class="selected-item"
                v-for="palace in palaceList"
                :key="palace.id"
                @click="selectPalace(palace)"
              >
                <el-checkbox
                  :model-value="isPalaceSelected(palace.id)"
                  @change="togglePalaceSelection(palace)"
                />
                <span class="palace-name">{{ palace.name }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Search, CaretRight, ArrowRight, ArrowLeft } from '@element-plus/icons-vue';
import { getBuildingTree } from '/@/api/backStageManage/building';
import { useMessage } from '/@/hooks/message';

interface BuildingItem {
  id: string;
  name: string;
  children?: BuildingItem[];
  expanded?: boolean;
}

interface PalaceItem {
  id: string;
  name: string;
  buildingId: string;
}

interface Emits {
  (e: 'confirm', palaces: PalaceItem[]): void;
}

const emit = defineEmits<Emits>();
const { error } = useMessage();

// 响应式数据
const dialogVisible = ref(false);
const searchKeyword = ref('');
const buildingData = ref<BuildingItem[]>([]);
const selectedBuildings = ref<string[]>([]);
const selectedPalaces = ref<string[]>([]);
const palaceList = ref<PalaceItem[]>([]);

// 计算属性
const filteredBuildingData = computed(() => {
  if (!searchKeyword.value) {
    return buildingData.value;
  }

  return buildingData.value.map(group => ({
    ...group,
    children: group.children?.filter(building =>
      building.name.includes(searchKeyword.value)
    ) || []
  })).filter(group => group.children.length > 0);
});

// 获取建筑空间数据
const fetchBuildingData = async () => {
  try {
    const response = await getBuildingTree({ buildingName: '' });
    buildingData.value = (response?.data || []).map((item: any) => ({
      ...item,
      expanded: false
    }));
  } catch (err) {
    error('获取建筑空间数据失败');
  }
};

// 搜索处理
const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
};

// 切换分组展开状态
const toggleGroup = (group: BuildingItem) => {
  group.expanded = !group.expanded;
};

// 选择建筑
const selectBuilding = (building: BuildingItem) => {
  toggleSelection(building);
};

// 切换建筑选择状态
const toggleSelection = (building: BuildingItem) => {
  const index = selectedBuildings.value.indexOf(building.id);
  if (index > -1) {
    selectedBuildings.value.splice(index, 1);
  } else {
    selectedBuildings.value.push(building.id);
  }
};

// 检查建筑是否被选中
const isSelected = (buildingId: string) => {
  return selectedBuildings.value.includes(buildingId);
};

// 添加选中的建筑到宫观列表
const addSelected = () => {
  selectedBuildings.value.forEach(buildingId => {
    // 查找建筑信息
    let building: BuildingItem | undefined;
    for (const group of buildingData.value) {
      building = group.children?.find(b => b.id === buildingId);
      if (building) break;
    }

    if (building && !palaceList.value.find(p => p.buildingId === buildingId)) {
      palaceList.value.push({
        id: `palace_${buildingId}`,
        name: building.name,
        buildingId: buildingId
      });
    }
  });

  // 清空选中状态
  selectedBuildings.value = [];
};

// 移除选中的宫观
const removeSelected = () => {
  selectedPalaces.value.forEach(palaceId => {
    const index = palaceList.value.findIndex(p => p.id === palaceId);
    if (index > -1) {
      palaceList.value.splice(index, 1);
    }
  });

  // 清空选中状态
  selectedPalaces.value = [];
};

// 选择宫观
const selectPalace = (palace: PalaceItem) => {
  togglePalaceSelection(palace);
};

// 切换宫观选择状态
const togglePalaceSelection = (palace: PalaceItem) => {
  const index = selectedPalaces.value.indexOf(palace.id);
  if (index > -1) {
    selectedPalaces.value.splice(index, 1);
  } else {
    selectedPalaces.value.push(palace.id);
  }
};

// 检查宫观是否被选中
const isPalaceSelected = (palaceId: string) => {
  return selectedPalaces.value.includes(palaceId);
};

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false;
  // 重置数据
  selectedBuildings.value = [];
  selectedPalaces.value = [];
  palaceList.value = [];
  searchKeyword.value = '';
};

// 确认选择
const handleConfirm = () => {
  emit('confirm', palaceList.value);
  handleClose();
};

// 打开弹窗
const openDialog = () => {
  dialogVisible.value = true;
  fetchBuildingData();
};

// 暴露方法
defineExpose({
  openDialog
});

onMounted(() => {
  // 组件挂载时可以进行初始化
});
</script>

<style lang="scss" scoped>
.palace-dialog {
  .dialog-content {
    display: flex;
    height: 400px;
    gap: 16px;
  }

  .left-panel,
  .right-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    border: 1px solid #e4e7ed;
    border-radius: 6px;

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid #e4e7ed;
      background-color: #f5f7fa;

      .panel-title {
        font-weight: 600;
        color: #303133;
      }

      .count-text {
        font-size: 12px;
        color: #909399;
      }
    }

    .panel-content {
      flex: 1;
      overflow-y: auto;
      padding: 8px;
    }
  }

  .center-panel {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;

    .transfer-buttons {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .el-button {
        width: 40px;
        height: 32px;
        padding: 0;
      }
    }
  }

  .building-tree {
    .tree-group {
      margin-bottom: 8px;

      .group-header {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        cursor: pointer;
        border-radius: 4px;
        transition: background-color 0.3s;

        &:hover {
          background-color: #f5f7fa;
        }

        .expand-icon {
          margin-right: 8px;
          transition: transform 0.3s;

          &.expanded {
            transform: rotate(90deg);
          }
        }
      }

      .group-content {
        padding-left: 24px;

        .building-item {
          display: flex;
          align-items: center;
          padding: 6px 12px;
          cursor: pointer;
          border-radius: 4px;
          transition: background-color 0.3s;

          &:hover {
            background-color: #f0f9ff;
          }

          .building-name {
            margin-left: 8px;
            font-size: 14px;
            color: #606266;
          }
        }
      }
    }
  }

  .selected-list {
    .selected-item {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      cursor: pointer;
      border-radius: 4px;
      transition: background-color 0.3s;

      &:hover {
        background-color: #f0f9ff;
      }

      .palace-name {
        margin-left: 8px;
        font-size: 14px;
        color: #606266;
      }
    }
  }
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-input__wrapper) {
  border-radius: 4px;
}
</style>