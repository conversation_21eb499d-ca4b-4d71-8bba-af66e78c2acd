<template>
	<div class="edit-panel" v-loading="isLoading">
		<div class="edit-panel-back" @click="emit('close')">
			<el-button icon="DArrowLeft" plain>返回</el-button>
		</div>
		<div class="edit-panel-header">
			<el-tabs v-model="activeTab">
				<el-tab-pane label="基本信息" name="introduction"></el-tab-pane>
				<el-tab-pane label="解说词" name="commentary"></el-tab-pane>
				<el-tab-pane label="展览配置" name="exhibition"></el-tab-pane>
			</el-tabs>
		</div>
		<div class="edit-panel-content">
            <div class="edit-panel-form">
                <Introduction ref="introductionRef" :data="resourceData" @update="handleDataUpdate" v-if="activeTab === 'introduction'" />
                <Commentary ref="commentaryRef" :data="resourceData" @update="handleDataUpdate" v-if="activeTab === 'commentary'" />
                <Exhibition ref="exhibitionRef" :data="resourceData" @update="handleDataUpdate" v-if="activeTab === 'exhibition'" />
            </div>
            <div class="edit-panel-footer">
                <el-button @click="emit('close')">取消</el-button>
                <el-button type="primary" @click="handleSave" :loading="saveLoading">保存</el-button>
            </div>
        </div>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import Introduction from './Introduction.vue';
import Commentary from './Commentary.vue';
import Exhibition from './Exhibition.vue';
import {
	getResourceDetail,
	saveBasicInfo,
	saveCommentary,
	saveExhibitionFiles,
	saveTimelineConfig,
	saveScrollConfig,
} from '/@/api/backStageManage/resource';

const emit = defineEmits(['close', 'refresh']);
const props = defineProps({
	currentId: {
		type: String,
		default: '',
	},
});
const activeTab = ref('introduction');
const saveLoading = ref(false);
const resourceId = ref('');
const isLoading = ref(false);
const resourceData = reactive({
	// 基本信息
	resourceId: '',
	buildingId: '',
	resourceName: '',
	cover: '',
	resourceType: '',
	resourceTypeName: '',
	labels: '',
	description: '',
	modelId: '',

	// 模型详情
	model: {
		id: '',
		mainFile: '',
		properties: '',
		url: '',
		fileExtension: '',
	},

	// 解说词
	commentary: '',

	// 文件列表
	pictureFiles: [] as Array<{
		id: string;
		mainFile: string;
		properties: string;
		url: string;
		fileExtension: string;
	}>,
	audioFiles: [] as Array<{
		id: string;
		mainFile: string;
		properties: string;
		url: string;
		fileExtension: string;
	}>,
	videoFiles: [] as Array<{
		id: string;
		mainFile: string;
		properties: string;
		url: string;
		fileExtension: string;
	}>,
	subModelFiles: [] as Array<{
		id: string;
		mainFile: string;
		properties: string;
		url: string;
		fileExtension: string;
	}>,

	// 文件描述
	pictureNote: '',
	audioNote: '',
	videoNote: '',
	subModelNote: '',

	// 时间轴配置
	timeAxisQuantity: 0,
	timeAxisList: [] as Array<{
		timeId: string;
		resourceId: string;
		sortOrder: number;
		timePhase: number;
		title: string;
		description: string;
		cover: string;
	}>,

	// 长卷配置
	subScrollQuantity: 0,
	mainScroll: {
		scrollId: '',
		resourceId: '',
		mainPageFlag: '1',
		sortOrder: 0,
		cover: '',
		coverDescription: '',
		title: '',
		subtitle: '',
		description: '',
	},
	subScrollList: [] as Array<{
		scrollId: string;
		resourceId: string;
		mainPageFlag: string;
		sortOrder: number;
		cover: string;
		coverDescription: string;
		title: string;
		subtitle: string;
		description: string;
	}>,

	// 创建和更新信息
	createBy: '',
	createTime: '',
	updateBy: '',
	updateTime: '',
});

// 组件引用
const introductionRef = ref();
const commentaryRef = ref();
const exhibitionRef = ref();

onMounted(() => {
    resourceId.value = props.currentId;
    nextTick(() => {
        fetchResourceDetail();
    });
});
// 获取资源详情
const fetchResourceDetail = async () => {
	try {
		if (!resourceId.value) return;

		const response = await getResourceDetail(resourceId.value);
		if (response.code === 0 && response.data) {
			// 直接赋值API返回的数据结构
			Object.assign(resourceData, {
				resourceId: response.data.resourceId || '',
				buildingId: response.data.buildingId || '',
				resourceName: response.data.resourceName || '',
				cover: response.data.cover || '',
				resourceType: response.data.resourceType || '',
				resourceTypeName: response.data.resourceTypeName || '',
				labels: response.data.labels || '',
				description: response.data.description || '',
				modelId: response.data.modelId || '',
				model: response.data.model || {},
				commentary: response.data.commentary || '',
				pictureFiles: response.data.pictureFiles || [],
				audioFiles: response.data.audioFiles || [],
				videoFiles: response.data.videoFiles || [],
				subModelFiles: response.data.subModelFiles || [],
				pictureNote: response.data.pictureNote || '',
				audioNote: response.data.audioNote || '',
				videoNote: response.data.videoNote || '',
				subModelNote: response.data.subModelNote || '',
				timeAxisQuantity: response.data.timeAxisQuantity || 0,
				timeAxisList: response.data.timeAxisList || [],
				subScrollQuantity: response.data.subScrollQuantity || 0,
				mainScroll: response.data.mainScroll || {},
				subScrollList: response.data.subScrollList || [],
				createBy: response.data.createBy || '',
				createTime: response.data.createTime || '',
				updateBy: response.data.updateBy || '',
				updateTime: response.data.updateTime || '',
			});
		} else {
			ElMessage.error(response.message || '获取资源详情失败');
		}
	} catch (error) {
		ElMessage.error('获取资源详情失败');
	}
};

// 处理数据更新
const handleDataUpdate = (field: string, value: any) => {
	if (field.includes('.')) {
		// 处理嵌套字段
		const keys = field.split('.');
		let target = resourceData as any;
		for (let i = 0; i < keys.length - 1; i++) {
			target = target[keys[i]];
		}
		target[keys[keys.length - 1]] = value;
	} else {
		(resourceData as any)[field] = value;
	}
};

// 保存数据
const handleSave = async () => {
	try {
		saveLoading.value = true;
		const saveResults: Array<{ type: string; success: boolean; message?: string }> = [];
		if (activeTab.value == 'introduction') {
			const introData = await introductionRef.value.getData();
			const basicInfoData = {
				resourceId: resourceData.resourceId,
				resourceName: introData.resourceName,
				resourceType: introData.resourceType,
				buildingId: introData.buildingId,
				labels: introData.labels,
				description: introData.description,
				cover: introData.cover,
				modelId: introData.modelId,
			};
			saveBasicInfo(basicInfoData)
				.then((res) => {
					return res;
				})
				.catch((err) => {
					throw err;
				});
		} else if (activeTab.value == 'commentary') {
			const commentaryData = await commentaryRef.value.getData();
			const commentaryPayload = {
				resourceId: resourceData.resourceId,
				commentary: commentaryData.commentary,
			};
			saveCommentary(commentaryPayload)
				.then((res) => {
					return res;
				})
				.catch((err) => {
					throw err;
				});
		} else if (activeTab.value == 'exhibition') {
			const exhibitionData = await exhibitionRef.value.getData();
			saveExhibitionFiles(exhibitionData)
				.then((res) => {
					saveResults.push({ type: '展览文件配置', success: res.code === 200, message: res.message });
					return res;
				})
				.catch((err) => {
					saveResults.push({ type: '展览文件配置', success: false, message: err.message || '保存失败' });
					throw err;
				});

			saveTimelineConfig({})
				.then((res) => {
					saveResults.push({ type: '时间轴配置', success: res.code === 200, message: res.message });
					return res;
				})
				.catch((err) => {
					saveResults.push({ type: '时间轴配置', success: false, message: err.message || '保存失败' });
					throw err;
				});

			saveScrollConfig({})
				.then((res) => {
					saveResults.push({ type: '长卷配置', success: res.code === 200, message: res.message });
					return res;
				})
				.catch((err) => {
					saveResults.push({ type: '长卷配置', success: false, message: err.message || '保存失败' });
					throw err;
				});
		}
		ElMessage.success('保存成功');
		emit('refresh');
	} catch (error) {
		ElMessage.error('保存过程中发生错误，请检查信息填写是否正确');
	} finally {
		saveLoading.value = false;
	}
};
</script>

<style lang="scss" scoped>
.edit-panel {
	position: relative;
	height: 100%;
	display: flex;
	flex-direction: column;
	.edit-panel-back {
		position: absolute;
		right: 10px;
		top: 9px;
		cursor: pointer;
		z-index: 999;
		.el-icon {
			font-size: 18px;
		}
	}
	:deep(.edit-panel-header) {
		height: 50px;
		background: #fff;
		border-radius: 5px;
		padding: 0 20px;
		.el-tabs__item {
			height: 50px;
			line-height: 50px;
		}
		.el-tabs__header {
			margin-bottom: 0;
		}
		.el-tabs__nav-wrap::after {
			height: 0 !important;
		}
	}
    .edit-panel-content {
        flex: 1;
		height: calc(100% - 100px);
		margin-top: 15px;
		background: #fff;
		border-radius: 6px;
        .edit-panel-form {
            height: calc(100% - 50px);
        }
        .edit-panel-footer {
            height: 50px;
            text-align: center;
        }
    }
}
</style>
