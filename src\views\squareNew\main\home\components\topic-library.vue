<template>
  <div class="topic-library-container" v-if="topicList.length">
    <div class="title">专题库</div>
    <div class="topic-cards">
      <div
        class="card-item"
        :class="'card-item-' + (index % 6)"
        v-for="(i, index) in topicList"
        :key="index"
        @click="handleClick(i)"
      >
        <div class="item-content">
          <img :src="i.icon" />
          <div class="card-number">{{ i.itemCount || "" }}</div>
          <div class="card-text">{{ i.itemName || "" }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import icon1 from "/@/assets/img/squareNew/icon1.png";
import icon2 from "/@/assets/img/squareNew/icon2.png";
import icon3 from "/@/assets/img/squareNew/icon3.png";
import icon4 from "/@/assets/img/squareNew/icon4.png";
import icon5 from "/@/assets/img/squareNew/icon5.png";
import icon6 from "/@/assets/img/squareNew/icon6.png";
import { useRouter } from "vue-router";
const props = defineProps({
  catalogList: {
    type: Array,
    default: () => [],
  },
});
const router = useRouter();
const handleClick = (record: any) => {
  router.push(`/squareNew/resource?topicId=${record.itemNo}`);
};
const iconList = [icon1, icon2, icon3, icon4, icon5, icon6];
const topicList = computed(() => {
  return props.catalogList.map((item: any, index: number) => {
    return {
      ...item,
      icon: iconList[index % iconList.length],
    };
  });
});

onMounted(() => {});
</script>

<style scoped lang="scss">
.topic-library-container {
  display: flex;
  flex-direction: column;
  margin-top: 50px;
  .title {
    margin-bottom: 45px;
    text-align: left;
    font-family: "Source Han Serif CN";
    font-weight: 700;
    font-size: 40px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
    color: #141414;
  }

  .topic-cards {
    display: flex;
    gap: 20px; // 卡片之间的间距
    width: 100%;
    justify-content: space-between;

    .card-item {
      flex: 1;
      height: 140px; // 根据图片调整
      background-size: 100% 100%;
      border-radius: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: white;
      position: relative;
      overflow: hidden;
      cursor: pointer;
      transition: all 0.3s ease-in-out;
      padding: 0 10px;
      &:hover {
        margin-top: -12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
      }

      .item-content {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        > img {
          width: 46px;
          height: 46px;
          margin-bottom: 17px;
        }

        .card-number {
          font-family: Source Han Sans CN;
          font-weight: 700;
          font-size: 30px;
          line-height: 20px;
          letter-spacing: 0px;
          text-align: center;
          height: 20px;
          line-height: 20px;
        }

        .card-text {
          margin-top: 10px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 16px;
          line-height: 20px;
          letter-spacing: 0px;
          color: #ffffffe5;
          height: 20px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          max-width: 100%;
        }

        .card-icon {
          width: 70px; // 占位符
          height: 70px; // 占位符
          background-color: #ffffff33;
          border-radius: 16px;
          margin-left: 42px;
          display: flex;
          align-items: center;
          justify-content: center;
          .icon-box {
            display: inline-block;
            width: 50px;
            height: 50px;
            background-image: url("/@/assets/img/squareNew/topicIcon.png");
            background-size: 100% 100%;
          }
        }
      }

      &.card-item-0 {
        background-image: url("/@/assets/img/squareNew/z1.png");
      }
      &.card-item-1 {
        background-image: url("/@/assets/img/squareNew/z2.png");
      }
      &.card-item-2 {
        background-image: url("/@/assets/img/squareNew/z3.png");
      }
      &.card-item-3 {
        background-image: url("/@/assets/img/squareNew/z4.png");
      }
      &.card-item-4 {
        background-image: url("/@/assets/img/squareNew/z5.png");
      }
      &.card-item-5 {
        background-image: url("/@/assets/img/squareNew/z6.png");
      }
    }
  }
}
</style>
