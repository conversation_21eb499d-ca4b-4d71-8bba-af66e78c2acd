import request from "/@/utils/request"
// 获取分类列表
export function fetchGroupList() {
  return request({
    url: '/check/wb/manage/listClassify',
    method: 'get',
  })
}

// 新增分类
export function addClassify(data: any) {
  return request({
    url: '/check/wb/manage/addClassify',
    method: 'post',
    data
  })
}

// 编辑分类
export function editClassify(data: any) {
  return request({
    url: '/check/wb/manage/editClassify',
    method: 'post',
    data
  })
}

// 删除分类
export function delClassify(query?: Object) {
  return request({
    url: '/check/wb/manage/delClassify',
    method: 'get',
    params: query
  })
}


// 批量删除
export function batchDel(data: any, query?: string) {
  return request({
    url: '/check/wb/resource/batchDel' + (query || ''),
    method: 'post',
    data
  })
}

// 知识库文件列表
export function queryDirByParam(query?: Object) {
  return request({
    url: '/check/wb/resource/queryDirByParam',
    method: 'get',
    params: query
  })
}

// 查询当前登录用户的分类权限
export function userAuthority(query?: Object) {
  return request({
    url: '/check/wb/manage/userAuthority',
    method: 'get',
    params: query
  })
}