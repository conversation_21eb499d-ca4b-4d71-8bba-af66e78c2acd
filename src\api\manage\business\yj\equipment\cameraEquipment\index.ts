import request from "/@/utils/request"

export function fetchList(query?: Object) {
  return request({
    url: '/business/yjHkCameras/byPage',
    method: 'get',
    params: query
  })
}

export function addObj(obj?: Object) {
  return request({
    url: '/business/yjHkCameras',
    method: 'post',
    data: obj
  })
}

export function getObj(id?: string) {
  return request({
    url: '/business/yjHkCameras/' + id,
    method: 'get',
    // params: {id:id}
  })
}

export function delObjs(ids?: Object) {
  return request({
    url: '/business/yjHkCameras',
    method: 'delete',
    data: ids
  })
}

export function putObj(obj?: Object) {
  return request({
    url: '/business/yjHkCameras',
    method: 'put',
    data: obj
  })
}

export function cancelObjs(ids?: Array<string>) {
  return request({
    url: '/business/yjHkCameras/updateByIds',
    method: 'post',
    data: ids
  })
}