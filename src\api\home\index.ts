import request from '/@/utils/request';

// 文物保护概况
export function getCulturalData(query?: Object) {
	return request({
		url: '/business/wb/warn/culturalRelicsProtectionOverview',
		method: 'get',
        params: query
	});
}

// 文旅运营概况
export function getTourismData(query?: Object) {
	return request({
		url: '/business/wb/warn/tourismOperationsOverview',
		method: 'get',
        params: query
	});
}

// 应急安全概况
export function getEmergencyData(query?: Object) {
	return request({
		url: '/business/wb/warn/emergencySafetyOverview',
		method: 'get',
        params: query
	});
}

// 防汛指挥概况
export function getFloodData(query?: Object) {
	return request({
		url: '/business/wb/warn/floodControlCommandOverview',
		method: 'get',
        params: query
	});
}
