<template>
  <div class="scene-detail">
    <!-- 页面头部 -->
    <div class="detail-header">
      <div class="header-left">
        <el-button @click="goBack" class="back-btn">
          <el-icon>
            <ArrowLeft />
          </el-icon>
          返回列表
        </el-button>
        <div class="title-area">
          <h2 class="scene-title">{{ sceneInfo.name }}</h2>
          <el-tag :type="sceneInfo.status === '1' ? 'success' : 'danger'" size="small">
            {{ sceneInfo.status === '1' ? '启用' : '禁用' }}
          </el-tag>
        </div>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="goToConfig">
          <el-icon>
            <Setting />
          </el-icon>
          场景配置
        </el-button>
        <el-button @click="editScene">
          <el-icon>
            <Edit />
          </el-icon>
          编辑场景
        </el-button>
      </div>
    </div>

    <!-- 标签页内容 -->
    <div class="detail-content">
      <el-tabs v-model="activeTab" class="detail-tabs">
        <!-- 基本信息 -->
        <el-tab-pane label="基本信息" name="basic">
          <BasicInfo :scene-info="sceneInfo" @refresh="getSceneDetail" />
        </el-tab-pane>

        <!-- 简介/解说词 -->
        <el-tab-pane label="简介/解说词" name="introduction">
          <IntroductionInfo :scene-id="sceneId" />
        </el-tab-pane>

        <!-- 展览配置 -->
        <el-tab-pane label="展览配置" name="exhibition">
          <ExhibitionConfig :scene-id="sceneId" />
        </el-tab-pane>

        <!-- 详情描述 -->
        <el-tab-pane label="详情描述" name="description">
          <DescriptionConfig :scene-id="sceneId" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineAsyncComponent } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useMessage } from '/@/hooks/message';
import { ArrowLeft, Setting, Edit } from '@element-plus/icons-vue';

// 异步组件
const BasicInfo = defineAsyncComponent(() => import('./components/BasicInfo.vue'));
const IntroductionInfo = defineAsyncComponent(() => import('./components/IntroductionInfo.vue'));
const ExhibitionConfig = defineAsyncComponent(() => import('./components/ExhibitionConfig.vue'));
const DescriptionConfig = defineAsyncComponent(() => import('./components/DescriptionConfig.vue'));

const router = useRouter();
const route = useRoute();
const { error } = useMessage();

// 响应式数据
const activeTab = ref('basic');
const sceneId = ref(route.params.id as string);
const sceneInfo = ref<any>({});

// 方法
const goBack = () => {
  router.push('/backStageManage/sceneManage');
};

const goToConfig = () => {
  router.push(`/backStageManage/sceneConfig/${sceneId.value}`);
};

const editScene = () => {
  // TODO: 打开编辑弹窗
};

const getSceneDetail = async () => {
  try {
    // TODO: 调用API获取场景详情
    // 模拟数据
    sceneInfo.value = {
      id: sceneId.value,
      name: '武当山主峰天柱峰',
      description: '武当山最高峰，海拔1612米，是道教圣地的核心区域',
      coverImage: '',
      status: '1',
      sort: 1,
      tags: ['道教文化', '自然风光'],
      chapterCount: 3,
      palaceCount: 8,
      nodeCount: 25,
      createTime: '2023-12-01 10:00:00',
      updateTime: '2023-12-01 10:00:00',
    };
  } catch (err) {
    error('获取场景详情失败');
  }
};

onMounted(() => {
  getSceneDetail();
});
</script>

<style scoped lang="scss">
.scene-detail {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: white;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .back-btn {
      .el-icon {
        margin-right: 4px;
      }
    }

    .title-area {
      display: flex;
      align-items: center;
      gap: 12px;

      .scene-title {
        font-size: 20px;
        font-weight: 600;
        color: #303133;
        margin: 0;
      }
    }
  }

  .header-right {
    display: flex;
    gap: 12px;

    .el-button {
      .el-icon {
        margin-right: 4px;
      }
    }
  }
}

.detail-content {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;

  .detail-tabs {
    height: 100%;

    :deep(.el-tabs__header) {
      margin: 0;
      padding: 0 24px;
      border-bottom: 1px solid #e4e7ed;
      background: #fafafa;

      .el-tabs__nav-wrap {
        &::after {
          display: none;
        }
      }

      .el-tabs__item {
        height: 50px;
        line-height: 50px;
        font-size: 14px;
        font-weight: 500;

        &.is-active {
          color: #409eff;
        }
      }
    }

    :deep(.el-tabs__content) {
      height: calc(100% - 50px);
      padding: 0;

      .el-tab-pane {
        height: 100%;
        overflow: auto;
      }
    }
  }
}
</style>
