<template>
  <!--单行文本-->
  <el-input
    v-if="columnInfo.typeCode == 'oneLine'"
    :maxlength="columnInfo.columnCustomeRule.maxLength"
    :minlength="columnInfo.columnCustomeRule.minLength"
    v-model="columnValue"
    :placeholder="columnInfo.prompt"
    show-word-limit
  />
  <el-input
    v-else-if="columnInfo.typeCode == 'multiLine'"
    :maxlength="columnInfo.columnCustomeRule.maxLength"
    :minlength="columnInfo.columnCustomeRule.minLength"
    v-model="columnValue"
    :placeholder="columnInfo.prompt"
    show-word-limit
    type="textarea"
    :rows="4"
  />
  <editor
    v-else-if="columnInfo.typeCode == 'tichText'"
    v-model:get-html="columnValue"
    :placeholder="columnInfo.prompt"
    style="width: 100%"
  />
  <el-input-number
    v-else-if="columnInfo.typeCode == 'number'"
    v-model="columnValue"
    :placeholder="columnInfo.prompt"
    :precision="columnInfo.columnCustomeRule.point || 0"
    :step="1 / Math.pow(10, columnInfo.columnCustomeRule.point || 0)"
    :min="
      (columnInfo.columnCustomeRule.range &&
        columnInfo.columnCustomeRule.range.length &&
        columnInfo.columnCustomeRule.range[0]) ||
      0
    "
    :max="
      (columnInfo.columnCustomeRule.range &&
        columnInfo.columnCustomeRule.range.length &&
        columnInfo.columnCustomeRule.range[1]) ||
      100000
    "
  />
  <el-input-number
    v-else-if="columnInfo.typeCode == 'amount'"
    v-model="columnValue"
    :placeholder="columnInfo.prompt"
    :precision="columnInfo.columnCustomeRule.point || 1"
    :step="1 / Math.pow(10, columnInfo.columnCustomeRule.point || 0)"
    :min="
      (columnInfo.columnCustomeRule.range &&
        columnInfo.columnCustomeRule.range.length &&
        columnInfo.columnCustomeRule.range[0]) ||
      0
    "
    :max="
      (columnInfo.columnCustomeRule.range &&
        columnInfo.columnCustomeRule.range.length &&
        columnInfo.columnCustomeRule.range[1]) ||
      100000
    "
  />
  <el-date-picker
    v-else-if="columnInfo.typeCode == 'date'"
    v-model="columnValue"
    :type="timeConfig[columnInfo.columnCustomeRule.format]?.timeType"
    :placeholder="columnInfo.prompt"
    :format="timeConfig[columnInfo.columnCustomeRule.format]?.format"
    value-format="YYYY/MM/DD HH:mm:ss"
  />
  <div
    v-else-if="columnInfo.typeCode == 'dateRange' && columnValue"
    style="width: 100%; position: relative"
  >
    <el-date-picker
      v-model="columnValue[0]"
      :type="timeConfig[columnInfo.columnCustomeRule.format]?.timeType"
      placeholder="开始时间"
      :format="timeConfig[columnInfo.columnCustomeRule.format]?.format"
      value-format="YYYY/MM/DD HH:mm:ss"
      style="width: calc(50% - 10px) !important"
    />
    <span style="margin: 0px 5px">~</span>
    <el-date-picker
      v-model="columnValue[1]"
      :type="timeConfig[columnInfo.columnCustomeRule.format]?.timeType"
      placeholder="结束时间"
      :format="timeConfig[columnInfo.columnCustomeRule.format]?.format"
      value-format="YYYY/MM/DD HH:mm:ss"
      style="width: calc(50% - 10px) !important"
    />
  </div>
  <el-select
    v-else-if="columnInfo.typeCode == 'ratioOption'"
    :placeholder="columnInfo.prompt"
    clearable
    v-model="columnValue"
  >
    <el-option
      v-for="item in columnInfo.columnCustomeRule"
      :key="item.key"
      :label="item.key"
      :value="item.value"
    />
  </el-select>
  <el-select
    v-else-if="columnInfo.typeCode == 'multiOption'"
    :placeholder="columnInfo.prompt"
    clearable
    v-model="columnValue"
    multiple
  >
    <el-option
      v-for="item in columnInfo.columnCustomeRule"
      :key="item.key"
      :label="item.key"
      :value="item.value"
    />
  </el-select>

  <el-select
    v-else-if="
      columnInfo.typeCode == 'province' && columnInfo.columnCustomeRule.format == '省'
    "
    placeholder="请选择省别"
    clearable
    v-model="columnValue"
  >
    <el-option
      v-for="(name, key) in handleProvinceData(columnInfo)"
      :key="key"
      :label="name"
      :value="key"
    />
  </el-select>
  <ChinaArea
    v-else-if="
      columnInfo.typeCode == 'province' && columnInfo.columnCustomeRule.format != '省'
    "
    :columnInfo="columnInfo"
    :placeholder="'请选择' + columnInfo.columnCustomeRule.format"
    v-model="columnValue"
    :plus="false"
    :type="columnInfo.columnCustomeRule.format == '省市' ? 2 : 3"
    style="width: 100%"
  />
  <upload-img-list
    v-else-if="columnInfo.typeCode == 'image'"
    v-model:imageUrl="columnValue"
    :limit="columnInfo.columnCustomeRule.fileCount"
    :fileSize="columnInfo.columnCustomeRule.fileSize"
    height="80px"
    width="120px"
    style="width: 100%"
    uploadFileUrl="/datacenter/learning/material/uploadFiles"
  />
  <Upload
    v-else-if="columnInfo.typeCode == 'attach'"
    v-model="columnValue"
    :limit="columnInfo.columnCustomeRule.fileCount"
    :fileSize="columnInfo.columnCustomeRule.fileSize"
    :fileType="[]"
    returnType="array"
    style="width: 100%"
    uploadFileUrl="/datacenter/learning/material/uploadFiles"
  />
</template>

<script setup lang="ts" name="upload-file">
import { provinceObject } from "/@/utils/chinaArea";
import { timeConfig } from "/@/config/resourceConfig";
const ChinaArea = defineAsyncComponent(() => import("/@/components/ChinaArea/index.vue"));
const Upload = defineAsyncComponent(() => import("/@/components/Upload/index.vue"));
const props = defineProps({
  modelValue: {
    type: null as any,
    default: () => null,
  },
  columnInfo: {
    type: Object as any,
    default: () => {},
  },
});
const columnOBJ: any = ref({});
const columnValue: any = ref();
const emit = defineEmits(["update:modelValue", "change"]);

const handleProvinceData = (columnInfo) => {
  if (columnInfo.columnCustomeRule.province) {
    return {
      [columnInfo.columnCustomeRule.province]: provinceObject[columnInfo.columnCustomeRule.province]
    }
  }
  return provinceObject
}
watch(
  () => props.modelValue,
  (val: any) => {
    columnValue.value = val;
    if (["province"].includes(props.columnInfo.typeCode)) {
      columnValue.value = val || "";
    }
  },
  { deep: true, immediate: true }
);
watch(
  () => columnValue.value,
  (val: any) => {
    emit("change", val);
    emit("update:modelValue", val);
  },
  { deep: true, immediate: true }
);
</script>
<style lang="scss" scoped></style>
