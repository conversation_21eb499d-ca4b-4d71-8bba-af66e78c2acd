import request from '/@/utils/request';

/**
 * 档案类型列表
 *
 */
export function getListArchiveType(data: any) {
  return request({
    url: '/datacenter/archive/listArchiveType',
    method: 'post',
    data
  });
}

/**
 * 删除档案类型列表
 *
 */
export function delArchiveType(data: any) {
  return request({
    url: '/datacenter/archive/delArchiveType',
    method: 'post',
    data
  });
}

/**
 * 添加档案类型
 *
 */
export function addColumnGroup(data: any) {
  return request({
    url: '/datacenter/archive/addArchiveType',
    method: 'post',
    data
  });
}

/**
 * 复制档案类型
 *
 */
export function copyColumnGroup(data: any) {
  return request({
    url: '/datacenter/archive/copyArchiveType',
    method: 'post',
    data
  });
}

/**
 * 获取列表
 *
 */
export function getListColumns(data: any) {
  return request({
    url: '/datacenter/archive/pageColumns',
    method: 'post',
    data
  });
}


/**
 * 删除字段列表
 *
 */
export function removeColumn(data: any) {
  return request({
    url: '/datacenter/columnInfo/removeColumn',
    method: 'post',
    data
  });
}

/**
 * 添加字段
 *
 */
export function addColumnInfo(data: any) {
  return request({
    url: '/datacenter/archive/addColumn',
    method: 'post',
    data
  });
}
/**
 * 编辑字段
 *
 */
export function editColumnInfo(data: any) {
  return request({
    url: '/datacenter/columnInfo/editColumnInfo',
    method: 'post',
    data
  });
}
/**
 * 编辑字段
 *
 */
export function getArchiveTypeDetail(data: any) {
  return request({
    url: '/datacenter/archive/archiveTypeDetail',
    method: 'post',
    data
  });
}

/**
 * 发布
 *
 */
export function handlePublish(data: any) {
  return request({
    url: '/datacenter/columnInfo/publish',
    method: 'post',
    data
  });
}
