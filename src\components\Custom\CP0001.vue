<template>
  <div class="mt-content">
    <div v-for="(item, k) in props.data" align="center" :key="k" class="mtc-box">
      <div class="mtcb-title">{{ item.title }}</div>
      <div class="mtcb-value">
        <div class="mtcb-num">{{ item.num }}</div>
        <div class="mtcb-unit">{{ item.unit }}</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" name="CP0001" setup>
  const props = defineProps({
    data: {
      type: Array<any>,
      default: [
        { title: "系统运行时间", num: 100, unit: "天" },
        { title: "CPU核心数", num: 8, unit: "" },
        { title: "内存总量", num: 50.0, unit: "G" },
        { title: "系统平均负载", num: 0.08, unit: "" },
      ],
    },
  });
</script>
<style lang="scss" scoped>
  .mt-content {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .mtc-box {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 0;
      height: 90px;
      width: 215px;
      border-right: 2px solid #000000ab;
      &:last-child {
        border-right: none;
      }
      .mtcb-title {
        width: 100%;
        height: 40px;
        position: absolute;
        margin-bottom: 20px;
        font-family: PingFang SC, PingFang SC;
        font-weight: bold;
        font-size: 15px;
        color: #7c7c7c;
        letter-spacing: 0.1em;
      }
      .mtcb-value {
        width: 100%;
        height: 40px;
        position: absolute;
        margin-top: 20px;
        display: flex;
        align-items: baseline;
        justify-content: center;
        .mtcb-num {
          font-family: yjsz;
          font-weight: bold;
          font-size: 36px;
          color: #000000;
          margin-right: 5px;
          letter-spacing: 0.1em;
        }
        .mtcb-unit {
          font-family: PingFang SC, PingFang SC;
          font-weight: bold;
          font-size: 15px;
          color: #7c7c7c;
        }
      }
    }
  }
</style>
