import request from "/@/utils/request";

// 查询文件夹列表
export function getFolderList(query?: Object) {
  return request({
    url: "/datacenter/fold/child",
    method: "get",
    params: query,
  });
}
// 新增文件夹
export function addFolder(data?: Object) {
  return request({
    url: "/datacenter/fold/add",
    method: "post",
    data,
  });
}
// 编辑文件夹
export function editFolder(data?: Object) {
  return request({
    url: "/datacenter/fold/edit",
    method: "post",
    data,
  });
}
// 删除文件夹
export function delFolder(data?: Object) {
  return request({
    url: "/datacenter/fold/delete",
    method: "post",
    data,
  });
}
// 批量删除文件、文件夹
export function batchDelFolder(data?: Object) {
  return request({
    url: "/datacenter/fold/batchDel",
    method: "post",
    data,
  });
}
// 资源文件检索统计
export function countByCatalog(query?: Object) {
  return request({
    url: "/datacenter/learning/material/countByCatalog",
    method: "get",
    params: query,
  });
}
// 关键字检索
export function keywordSearch(data?: Object) {
  return request({
    url: "/datacenter/learning/material/keywordSearch",
    method: "post",
    data: data,
  });
}
// 高级检索
export function advancedSearch(data?: Object) {
  return request({
    url: "/datacenter/learning/material/advancedSearch",
    method: "post",
    data: data,
  });
}
// 检索结果按目录统计
export function searchCountByCatalog(data?: Object) {
  return request({
    url: "/datacenter/learning/material/searchCountByCatalog",
    method: "post",
    data: data,
  });
}
// AI检索
export function aiSearch(data?: Object) {
  return request({
    url: "/datacenter/learning/material/aiSearch",
    method: "post",
    data: data,
  });
}

// 获取关系图谱
export function getFileGraph(fileId?: Object) {
  return request({
    url: `/datacenter/learning/material/graph/${fileId}`,
    method: "get",
  });
}

// 获取AI对话列表
export function queryConversationList(params?: Object) {
  return request({
    url: "/datacenter/ai/conversation/list",
    method: "get",
    params,
  });
}

// 查询对话历史记录
export function queryConversationHistory(id: any) {
  return request({
    url: `/datacenter/ai/conversation/listBySessionId/${id}`,
    method: "get",
  });
}

// 保存对话记录
export function saveConversationData(data?: Object) {
  return request({
    url: `/datacenter/ai/conversation/save`,
    method: "post",
    data,
  });
}

// 删除会话
export function deleteConversation(id: any) {
  return request({
    url: `/datacenter/ai/conversation/delete/${id}`,
    method: "get",
  });
}
// 资源目录查询
export function queryResourceCatalogList(data?: Object) {
  return request({
    url: "/datacenter/catalog/listForResourceForSquare",
    method: "post",
    data,
  });
}
// 资源列表查询
export function queryResourceList(params?: Object) {
  return request({
    url: "/datacenter/learning/material/resource/page",
    method: "get",
    params,
  });
}
