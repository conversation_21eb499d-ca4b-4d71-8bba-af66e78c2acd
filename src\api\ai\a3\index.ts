import request from "/@/utils/request"

// 城墙整体平均聚集密度
export function getAverageData() {
  return request({
    url: '/ai/wlPersonnelGatheringDensityPrediction/aggregationDensityAverage',
    method: 'get',
  })
}
// 未来人员聚集密度top10位置
export function getTopTenData() {
  return request({
    url: '/ai/wlPersonnelGatheringDensityPrediction/personnelGatheringDensityTopTen',
    method: 'get',
  })
}
// 城墙整体未来48h人员聚集平均密度
export function get48Data(query?: Object) {
  return request({
    url: '/ai/wlPersonnelGatheringDensityPrediction/densityPersonnelGatheringAverage',
    method: 'get',
    params: query
  })
}
// 预计城墙整体未来48h人员聚集平均密度
export function getForecast48Data() {
  return request({
    url: '/ai/wlPersonnelGatheringDensityPrediction/expectDensityPersonnelGatheringAverage',
    method: 'get',
  })
}
// 获取各监测位置人员聚集密度预测结果列表
export function fetchList(query?: Object) {
  return request({
    url: '/ai/wlPersonnelGatheringDensityPrediction/personnelGatheringDensityPredictionByPage',
    method: 'get',
    params: query
  })
}



// 详情查询实际监测趋势
export function getObj(id?: string) {
  return request({
    url: '/ai/wlPersonnelGatheringDensityPrediction/selectedMonitoringGathering',
    method: 'get',
    params: {pointId:id}
  })
}
// 详情查询预计监测趋势
export function getExpectObj(id?: string) {
  return request({
    url: '/ai/wlPersonnelGatheringDensityPrediction/expectSelectedMonitoringGathering',
    method: 'get',
    params: {pointId:id}
  })
}
