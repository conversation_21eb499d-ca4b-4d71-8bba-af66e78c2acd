import request from "/@/utils/request"

export function fetchList(query?: Object) {
  return request({
    url: '/business/yjWarnInfoFire/byPage',
    method: 'get',
    params: query
  })
}

export function addObj(obj?: Object) {
  return request({
    url: '/business/yjWarnInfoFire',
    method: 'post',
    data: obj
  })
}

export function getObj(id?: string) {
  return request({
    url: '/business/yjWarnInfoFire/' + id,
    method: 'get'
  })
}

export function delObjs(ids?: Object) {
  return request({
    url: '/business/yjWarnInfoFire',
    method: 'delete',
    data: ids
  })
}

export function putObj(obj?: Object) {
  return request({
    url: '/business/yjWarnInfoFire',
    method: 'put',
    data: obj
  })
}

export function cancelObjs(ids?: Array<string>) {
  return request({
    url: '/business/yjWarnInfoFire/updateByIds',
    method: 'post',
    data: ids
  })
}