import request from '/@/utils/request'

export function getAreaFourColor(query?: Object) {
	return request({
		url: '/report/wbWarn/monitoringFourColorWarning',
		method: 'get',
        params: query
	})
}

export function getWarningChange(query?: Object) {
	return request({
		url: '/report/wbWarn/warningChanges',
		method: 'get',
        params: query
	})
}

export function getFourColorTotal(query?: Object) {
	return request({
		url: '/report/wbWarn/summaryStatisticsFourColorMonitoring',
		method: 'get',
        params: query
	})
}
export function getAreaDetailData(query?: Object) {
	return request({
		url: '/report/wbWarn/characteristicsDetailedData',
		method: 'get',
        params: query
	})
}
export function getAreaDetailChart(query?: Object) {
	return request({
		url: '/report/wbWarn/characteristicsDetailedChart',
		method: 'get',
        params: query
	})
}

export function getPointFourColor(query?: Object) {
	return request({
		url: '/report/wbPoint/pointsFourColorWarning',
		method: 'get',
        params: query
	})
}

export function getAbnormalPointType(query?: Object) {
	return request({
		url: '/report/wbPoint/abnormalMonitoringPointTypes',
		method: 'get',
        params: query
	})
}

export function getFourColorSummary(query?: Object) {
	return request({
		url: '/report/wbPoint/fourColorMonitoringSummaryStatistics',
		method: 'get',
        params: query
	})
}

export function getTrendAnalysis(query?: Object) {
	return request({
		url: '/report/wbPoint/trendAnalysis',
		method: 'get',
        params: query
	})
}