import request from "/@/utils/request";

// 密级列表
export function fetchList(query?: Object) {
  return request({
    url: "/datacenter/security/level/list",
    method: "get",
    params: query,
  });
}
// 增加密级
export function addObj(obj?: Object) {
  return request({
    url: "/datacenter/security/level/add",
    method: "post",
    data: obj,
  });
}
// 修改密级
export function updateObj(obj?: Object) {
  return request({
    url: "/datacenter/security/level/update",
    method: "post",
    data: obj,
  });
}
// 删除密级
export function delObj(id?: object) {
  return request({
    url: "/datacenter/security/level/delete",
    method: "post",
    data: id,
  });
}
