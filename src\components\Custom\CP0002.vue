<template>
  <div class="mt-content">
    <div class="mtcb-title">{{ data.title }}</div>
    <!-- <v-chart v-if="chartOption" :option="chartOption" :autoresize="true" class="chart-box"></v-chart> -->
    <div ref="mtChart" class="chart-box"></div>
  </div>
</template>

<script lang="ts" name="CP0002" setup>
import * as echarts from "echarts";
const props = defineProps({
  data: {
    type: Object,
    default: {
      title: "CPU使用率（1m）",
      color: "#ffff00",
      value: 40,
    },
  },
});
const mtChart: any = ref(null);
onMounted(() => {
  initChart();
});
const initChart = () => {
  let myChart = echarts.init(mtChart.value);
  mtChart.value.removeAttribute("_echarts_instance_");
  let option: any = {
    title: {
      text: 100 - props.data.value,
      top: "center",
      left: "center",
      textStyle: {
        color: "#000",
        fontSize: 25,
        fontWeight: "bolder",
        // fontFamily: 'ast',
      },
    },
    grid: {
      left: "0%",
      right: "0%",
      top: "0%",
      bottom: "0%",
      containLabel: false,
    },
    tooltip: {
      show: false,
    },
    legend: {
      show: false,
    },
    toolbox: {
      show: false,
    },
    series: [
      {
        type: "pie",
        top: "center",
        left: "center",
        width: "100%",
        height: "100%",
        clockwise: false,
        radius: ["75%", "95%"],
        label: {
          show: false,
        },
        emphasis: {
          label: {
            show: false,
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          {
            value: 100 - props.data.value,
            name: "a",
            itemStyle: {
              color: "#cccccc",
              borderWidth: 0,
            },
          },
          {
            value: props.data.value,
            name: "b",
            itemStyle: {
              color: props.data.color,
              borderWidth: 2,
            },
          },
        ],
      },
    ],
    ...props.data.option,
  };
  myChart.setOption(option);
};
</script>
<style lang="scss" scoped>
.mt-content {
  height: 100%;
  padding: 10px;
  .mtcb-title {
    width: 100%;
    height: 30px;
    font-family: PingFang SC, PingFang SC;
    font-weight: bold;
    font-size: 15px;
    color: #000000;
    letter-spacing: 0.1em;
    text-align: center;
  }
  .chart-box {
    width: 100%;
    height: calc(100% - 30px);
  }
}
</style>
