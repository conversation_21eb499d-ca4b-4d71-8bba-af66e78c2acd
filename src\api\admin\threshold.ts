import request from '/@/utils/request';

export function fetchList(query?: Object) {
	return request({
		url: '/admin/sysThresholdConf/getFourColorWarningDataForCulturalProtection',
		method: 'get',
		params: query,
	});
}

export function editObj(obj?: Object) {
	return request({
		url: '/admin/sysThresholdConf/editFourColorWarningDataForCulturalProtection',
		method: 'post',
		data: obj,
	});
}

export function getSysThresholdConfList(query?: Object) {
	return request({
		url: '/admin/sysThresholdConf/getSysThresholdConfList',
		method: 'get',
		params: query,
	});
}

export function batchEdit(obj?: Object) {
	return request({
		url: '/admin/sysThresholdConf/batchEdit',
		method: 'post',
		data: obj,
	});
}