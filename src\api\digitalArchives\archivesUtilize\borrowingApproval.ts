import request from "/@/utils/request";

/**
 * 借阅审批 -- 待审批
 *
 */
export function getListArchiveBorrowAuditing(params: any) {
  return request({
    url: "/datacenter/archive/borrow/auditing/page",
    method: "get",
    params: params,
  });
}
/**
 * 借阅审批 -- 审批历史
 *
 */
export function getListArchiveBorrowAudited(params: any) {
  return request({
    url: "/datacenter/archive/borrow/audited/page",
    method: "get",
    params: params,
  });
}
/**
 * 借阅审批 -- 审批
 *
 */
export function auditArchiveBorrow(data: any) {
  return request({
    url: "/datacenter/archive/borrow/audit",
    method: "post",
    data: data,
  });
}

/**
 * 借阅审批 -- 审批 用于首页
 *
 */
export function getAuditArchiveBorrow(params: any) {
  return request({
    url: "/datacenter/archive/borrow/all/page",
    method: "get",
    params,
  });
}
