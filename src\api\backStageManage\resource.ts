import request from "/@/utils/request";

// 根据建筑空间ID查询资源列表
export function getResourceList(params?: Object) {
    return request({
        url: "/exhibition/buildingResource/resourcePage",
        method: "get",
        params: params,
    });
}

// 新增资源
export function addResource(params?: Object) {
    return request({
        url: "/exhibition/buildingResource",
        method: "post",
        data: params,
    });
}

// 删除资源
export function delResource(id: any) {
    return request({
        url: `/exhibition/buildingResource/${id}`,
        method: "delete"
    });
}

// 获取资源基本信息
export function getResourceInfo(id: string) {
    return request({
        url: `/exhibition/buildingResource/baseInfo/${id}`,
        method: "get"
    });
}

// 获取资源详情
export function getResourceDetail(id: string) {
    return request({
        url: `/exhibition/buildingResource/details/${id}`,
        method: "get"
    });
}

// 上传场景资源文件
export function uploadResourceFile(file: File, data?: any) {
    const formData = new FormData();
    formData.append('file', file);

    // 添加其他参数
    if (data) {
        Object.keys(data).forEach(key => {
            formData.append(key, data[key]);
        });
    }

    return request({
        url: "/exhibition/buildingResourceFile/uploadFile",
        method: "post",
        data: formData,
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    });
}

// 保存基础信息
export function saveBasicInfo(data: any) {
    return request({
        url: '/exhibition/buildingResource',
        method: 'put',
        data: data
    });
}

// 保存解说词
export function saveCommentary(data: any) {
    return request({
        url: '/exhibition/buildingResource/updateCommentary',
        method: 'post',
        data: data
    });
}

// 保存资源展览配置（图片、音频、视频、模型）
export function saveExhibitionFiles(data: any) {
    return request({
        url: '/exhibition/buildingResource/config/saveMediaFiles',
        method: 'post',
        data: data
    });
}

// 保存展览配置-时间轴
export function saveTimelineConfig(data: any) {
    return request({
        url: '/exhibition/buildingResource/config/saveTimeAxis',
        method: 'post',
        data: data
    });
}

// 保存展览配置-长卷形式
export function saveScrollConfig(data: any) {
    return request({
        url: '/exhibition/buildingResource/config/saveScroll',
        method: 'post',
        data: data
    });
}

