import request from "/@/utils/request"

export function fetchList(query?: Object) {
  return request({
    url: '/business/fxWaterFloodPrevention/byPage',
    method: 'get',
    params: query
  })
}

export function addObj(obj?: Object) {
  return request({
    url: '/business/fxWaterFloodPrevention',
    method: 'post',
    data: obj
  })
}

export function getObj(id?: string) {
  return request({
    url: '/business/fxWaterFloodPrevention/' + id,
    method: 'get'
  })
}

export function delObjs(ids?: Object) {
  return request({
    url: '/business/fxWaterFloodPrevention',
    method: 'delete',
    data: ids
  })
}

export function putObj(obj?: Object) {
  return request({
    url: '/business/fxWaterFloodPrevention',
    method: 'put',
    data: obj
  })
}
// 获取物资类型
export function getMaterialsType(id?: string) {
  return request({
    url: '/business/fxWaterFloodPrevention/getFloodType',
    method: 'get'
  })
}

