/**
 * 元数据字段验证逻辑
 * 将表单验证逻辑抽离为可复用的组合式函数
 */
import { ref, Ref } from "vue";

export interface FieldRules {
  [key: string]: Array<{
    required?: boolean;
    message: string;
    trigger: string;
    validator?: (rule: any, value: any, callback: any) => void;
  }>;
}

export function useFieldValidation() {
  // 定义校验规则
  const rules: Ref<FieldRules> = ref({
    name: [
      { required: true, message: "字段名不能为空", trigger: "blur" },
      {
        trigger: "change",
        validator(rule: any, value: any, callback: any) {
          if (!value) {
            return callback();
          }
          // 英文字段名格式验证: 只能包含字母、数字和下划线，必须以字母开头
          const reg = /^[a-zA-Z][a-zA-Z0-9_]*$/;
          if (!reg.test(value)) {
            return callback(new Error("字段名只能包含字母、数字和下划线，必须以字母开头"));
          }
          callback();
        },
      },
    ],
    cnName: [{ required: true, message: "中文名称不能为空", trigger: "blur" }],
    typeCode: [{ required: true, message: "字段类型不能为空", trigger: "blur" }],
  });

  /**
   * 根据字段类型更新验证规则
   * @param fieldType 字段类型
   */
  const updateRulesByFieldType = (fieldType: string) => {
    const baseRules = {
      name: [
        { required: true, message: "字段名不能为空", trigger: "blur" },
        {
          trigger: "change",
          validator(rule: any, value: any, callback: any) {
            if (!value) {
              return callback();
            }
            // 英文字段名格式验证: 只能包含字母、数字和下划线，必须以字母开头
            const reg = /^[a-zA-Z][a-zA-Z0-9_]*$/;
            if (!reg.test(value)) {
              return callback(new Error("字段名只能包含字母、数字和下划线，必须以字母开头"));
            }
            callback();
          },
        },
      ],
      cnName: [{ required: true, message: "中文名称不能为空", trigger: "blur" }],
      typeCode: [{ required: true, message: "字段类型不能为空", trigger: "blur" }],
    };

    let updatedRules = { ...baseRules };

    // 根据字段类型添加特定验证规则
    if (["oneLine", "multiLine", "richText"].includes(fieldType)) {
      // 文本类型字段的验证规则
      updatedRules.input_minLength = [
        { required: true, message: "最小长度不能为空", trigger: "blur" },
      ];
      updatedRules.input_maxLength = [
        { required: true, message: "最大长度不能为空", trigger: "blur" },
      ];
    } else if (["number"].includes(fieldType)) {
      // 数字类型字段的验证规则
      updatedRules.number_point = [
        { required: true, message: "请选择小数位数", trigger: "blur" },
      ];
    } else if (["amount"].includes(fieldType)) {
      // 金额类型字段的验证规则
      updatedRules.number_point = [
        { required: true, message: "请选择小数位数", trigger: "blur" },
      ];
    } else if (["date", "dateRange"].includes(fieldType)) {
      // 日期类型字段的验证规则
      updatedRules.date_format = [
        { required: true, message: "请选择格式", trigger: "change" },
      ];
      updatedRules.date_range = [
        {
          message: "开始时间必须小于结束时间",
          trigger: "change",
          validator(rule: any, value: any, callback: any) {
            if (!value || value.length != 2) {
              return callback();
            }
            if (value[0] && value[1] && value[0] > value[1]) {
              return callback(new Error("开始时间必须小于结束时间"));
            }
            callback();
          },
        },
      ];
    } else if (["ratioOption", "multiOption"].includes(fieldType)) {
      // 选项类型字段的验证规则
      updatedRules.options_list = [
        {
          required: true,
          trigger: "change",
          validator(rule: any, value: any, callback: any) {
            if (!value || value.length === 0) {
              return callback(new Error("请添加选项"));
            }
            let keys: string[] = [];
            let values: string[] = [];
            let flag = true;
            value.forEach((element: any) => {
              if (!element.key) {
                flag = false;
                return callback(new Error("选项名称不能为空"));
              }
              if (!element.value) {
                flag = false;
                return callback(new Error("选项值不能为空"));
              }
              // 选项名称不能重复
              if (keys.includes(element.key)) {
                flag = false;
                return callback(new Error(`选项名称【${element.key}】重复`));
              }
              // 选项值不能重复
              if (values.includes(element.value)) {
                flag = false;
                return callback(new Error(`选项值【${element.value}】重复`));
              }
              keys.push(element.key);
              values.push(element.value);
            });
            if (flag) {
              callback();
            }
          },
        },
      ];
    } else if (["province"].includes(fieldType)) {
      // 省份类型字段的验证规则
      updatedRules.area_province = [
        { required: true, message: "请选择省别", trigger: "blur" },
      ];
      updatedRules.area_format = [
        { required: true, message: "请选择格式", trigger: "change" },
      ];
    } else if (["image", "attach"].includes(fieldType)) {
      // 附件类型字段的验证规则
      updatedRules.upload_fileCount = [
        { required: true, message: "请输入上传文件个数", trigger: "blur" },
      ];
      updatedRules.upload_fileSize = [
        { required: true, message: "请输入上传文件大小", trigger: "blur" },
      ];
    }

    rules.value = updatedRules;
    return updatedRules;
  };

  return {
    rules,
    updateRulesByFieldType,
  };
}
