import request from "/@/utils/request"

export function fetchList(query?: Object) {
  return request({
    url: '/business/fxResponsePlan/byPage',
    method: 'get',
    params: query
  })
}

export function addObj(obj?: Object) {
  return request({
    url: '/business/fxResponsePlan',
    method: 'post',
    data: obj
  })
}

export function getObj(id?: string) {
  return request({
    url: '/business/fxResponsePlan/' + id,
    method: 'get'
  })
}

export function delObjs(ids?: Object) {
  return request({
    url: '/business/fxResponsePlan',
    method: 'delete',
    data: ids
  })
}

export function putObj(obj?: Object) {
  return request({
    url: '/business/fxResponsePlan',
    method: 'put',
    data: obj
  })
}

