import request from '/@/utils/request'

export function fetchList(query?: Object) {
	return request({
		url: '/business/wl/hydrologyInfo/byPage',
		method: 'get',
		params: query,
	})
}

export function addObj(obj?: Object) {
	return request({
		url: '/business/wl/hydrologyInfo',
		method: 'post',
		data: obj,
	})
}

export function getObj(id?: string) {
	return request({
		url: '/business/wl/hydrologyInfo/' + id,
		method: 'get',
	})
}

export function delObjs(ids?: Object) {
	return request({
		url: '/business/wl/hydrologyInfo',
		method: 'delete',
		data: ids,
	})
}

export function putObj(obj?: Object) {
	return request({
		url: '/business/wl/hydrologyInfo',
		method: 'put',
		data: obj,
	})
}
