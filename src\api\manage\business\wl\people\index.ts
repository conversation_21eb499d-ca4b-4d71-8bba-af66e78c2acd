import request from '/@/utils/request'

export function fetchList(query?: Object) {
	return request({
		url: '/business/wl/people/byPage',
		method: 'get',
		params: query,
	})
}

export function addObj(obj?: Object) {
	return request({
		url: '/business/wl/people',
		method: 'post',
		data: obj,
	})
}

export function getObj(id?: string) {
	return request({
		url: '/business/wl/people/' + id,
		method: 'get',
	})
}

export function delObjs(ids?: Object) {
	return request({
		url: '/business/wl/people',
		method: 'delete',
		data: ids,
	})
}

export function putObj(obj?: Object) {
	return request({
		url: '/business/wl/people',
		method: 'put',
		data: obj,
	})
}

export function fetchFlv(query?: Object) {
	return request({
		url: '/business/wl/people/getMonitoringUrlById',
		method: 'get',
		params: query,
	})
}
