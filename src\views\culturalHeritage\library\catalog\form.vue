<template>
  <el-dialog :title="state.dialogTitle" v-model="state.visible" :width="500" :close-on-click-modal="false"
    :close-on-press-escape="false" :before-close="onClose" append-to-body class="catalog-form-dialog">
    <el-form ref="editorDialogFormRef" :model="state.form" :rules="dataRules" label-width="90px" v-loading="loading">
      <el-form-item label="上级目录" prop="catalogParentNo">
        <el-tree-select :disabled="state.dialogType === 'DoEdit'" v-model="state.form.catalogParentNo"
          :data="state.parentData" :render-after-expand="false" default-expand-all
          :props="{ value: 'catalogNo', label: 'catalogName', children: 'childCatalogs' }" class="w100" filterable
          clearable check-strictly placeholder="请选择上级目录" @current-change="treeSelectChangeHandle">
        </el-tree-select>
      </el-form-item>
      <el-form-item label="目录名称" prop="catalogName">
        <el-input v-model="state.form.catalogName" clearable placeholder="请输入目录名称" maxlength="10"
          show-word-limit></el-input>
      </el-form-item>
      <el-form-item label="目录状态" prop="catalogState">
        <el-radio-group v-model="state.form.catalogState">
          <el-radio border :label="1">启用</el-radio>
          <el-radio border :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="目录序号" prop="catalogSort">
        <el-input-number v-model="state.form.catalogSort" :min="0" controls-position="right" />
      </el-form-item>
      <el-form-item label="备注" prop="catalogRemark">
        <el-input type="textarea" v-model="state.form.catalogRemark" :rows="5" placeholder="请输入备注信息" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="onClose">取 消</el-button>
        <el-button type="primary" @click="onSubmit" :loading="state.submitLoading">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="systemMenuDialog">
import { updateObj, addObj } from "/@/api/resource/catalog/catalog";
import { useMessage } from "/@/hooks/message";
const props = defineProps({
  parentData: {
    type: Array as any,
    default: () => [],
  },
});

// 定义子组件向父组件传值/事件
const emit = defineEmits(["refresh"]);

// 定义变量内容
const loading = ref(false);
const editorDialogFormRef = ref();
// 定义需要的数据
const state = reactive({
  visible: false,
  dialogTitle: "",
  dialogType: "",
  submitLoading: false,
  form: {
    id: "",
    catalogNo: null,
    catalogParentNo: null,
    catalogName: "",
    catalogLevel: 1,
    catalogState: 1,
    catalogSort: 0,
    catalogRemark: "",
  },
  parentData: [] as any[], // 上级菜单数据
});

// 表单校验规则
const dataRules = reactive({
  catalogName: [{ required: true, message: "目录名称不能为空", trigger: "blur" }],
});

// 打开弹窗
const openDialog = (type: string, row?: any) => {
  state.visible = true;
  Object.assign(state.form, {
    catalogNo: null,
    catalogParentNo: null,
    catalogName: "",
    catalogLevel: 1,
    catalogState: 1,
    catalogSort: 0,
    catalogRemark: "",
  });
  if (type === "DoEdit") {
    Object.assign(state.form, row);
    state.dialogTitle = "编辑";
    state.dialogType = "DoEdit";
  } else if (type === "DoAdd") {
    state.dialogTitle = "新增";
    state.dialogType = "DoAdd";
    state.form.id = "";
    state.form.catalogParentNo = row?.catalogNo || "00";
    state.form.catalogLevel = row?.catalogLevel + 1 || 1;
  }
  // 渲染上级菜单列表树
  getAllMenuData();
  nextTick(() => { });
};

// 从后端获取菜单信息（含层级）
const getAllMenuData = () => {
  state.parentData = [];
  let menu = {
    catalogNo: "00",
    catalogName: "根菜单",
    childCatalogs: [],
  };
  menu.childCatalogs = props.parentData;
  state.parentData.push(menu);
};

// 提交表单
const onSubmit = async () => {
  const valid = await editorDialogFormRef.value.validate().catch(() => false);
  if (!valid) return;
  try {
    state.submitLoading = true;
    if (state.dialogType === "DoAdd") {
      await addObj(state.form);
      useMessage().success("新增成功");
    } else {
      await updateObj(state.form);
      useMessage().success("修改成功");
    }
    onClose();
    emit("refresh");
  } catch (err) {
    useMessage().error((err as any).msg);
  } finally {
    state.submitLoading = false;
  }
};

const treeSelectChangeHandle = (parent: any) => {
  state.form.catalogSort = (parent.catalogSort || 0) + 1;
};

// 关闭弹窗
const onClose = () => {
  state.visible = false;
};

// 暴露变量 只有暴漏出来的变量 父组件才能使用
defineExpose({
  openDialog,
});
</script>
