import request from "/@/utils/request"

// 获取分类列表
export function fetchGroupList() {
  return request({
    url: '/archive/wb/manage/listClassify',
    method: 'get',
  })
}

// 新增分类
export function addClassify(data: any) {
  return request({
    url: '/archive/wb/manage/addClassify',
    method: 'post',
    data
  })
}

// 编辑分类
export function editClassify(data: any) {
  return request({
    url: '/archive/wb/manage/editClassify',
    method: 'post',
    data
  })
}

// 删除分类
export function delClassify(query?: Object) {
  return request({
    url: '/archive/wb/manage/delClassify',
    method: 'get',
    params: query
  })
}

// 创建文件夹
export function addDirectory(data: any) {
  return request({
    url: '/archive/wb/resource/addDirectory',
    method: 'post',
    data
  })
}

// 重命名文件夹
export function updateDirName(data: any) {
  return request({
    url: '/archive/wb/resource/updateDirName',
    method: 'post',
    data
  })
}

// 批量删除
export function batchDel(data: any, query?: string) {
  return request({
    url: '/archive/wb/resource/batchDel' + (query || ''),
    method: 'post',
    data
  })
}

// 查询某个分类下面的档案目录树
export function queryDirectoryTree(query?: Object) {
  return request({
    url: '/archive/wb/resource/queryDirectoryTree',
    method: 'get',
    params: query
  })
}

// 根据文件夹id查询子节点列表
export function queryChildren(query?: Object) {
  return request({
    url: '/archive/wb/resource/queryChildren',
    method: 'get',
    params: query
  })
}

// 获取编目字段列表
export function listCatalog(query?: Object) {
  return request({
    url: '/archive/wb/catalog/listCatalog',
    method: 'get',
    params: query
  })
}

// 新增编目字段
export function addCatalog(data: any) {
  return request({
    url: '/archive/wb/catalog/addCatalog',
    method: 'post',
    data
  })
}

// 编辑编目字段
export function editCatalog(data: any) {
  return request({
    url: '/archive/wb/catalog/editCatalog',
    method: 'post',
    data
  })
}

// 删除编目字段
export function delCatalog(query?: Object) {
  return request({
    url: '/archive/wb/catalog/delCatalog',
    method: 'get',
    params: query
  })
}

// 查询分类编目设置列表
export function classifyCatalogList(query?: Object) {
  return request({
    url: '/archive/wb/catalog/classifyCatalogList',
    method: 'get',
    params: query
  })
}

// 保存分类编目设置
export function saveClassifyCatalog(data: any) {
  return request({
    url: '/archive/wb/catalog/saveClassifyCatalog',
    method: 'post',
    data
  })
}

// 档案编目
export function saveArchiveCatalog(data: any) {
  return request({
    url: '/archive/wb/catalog/saveArchiveCatalog',
    method: 'post',
    data
  })
}

// 查询档案编目内容
export function queryArchiveCatalog(data: any, query: Object) {
  return request({
    url: '/archive/wb/catalog/queryArchiveCatalog',
    method: 'post',
    params: query,
    data
  })
}

// 查询当前登录用户的分类权限
export function userAuthority(query?: Object) {
  return request({
    url: '/archive/wb/manage/userAuthority',
    method: 'get',
    params: query
  })
}

// 根据目录id查询目录信息
export function queryInfoByDirectoryId(query?: Object) {
  return request({
    url: '/archive/wb/resource/queryInfoByDirectoryId',
    method: 'get',
    params: query
  })
}

// 根据资源id查询es保存的档案资源
export function findByResourceId(query?: Object) {
  return request({
    url: '/archive/wb/es/findByResourceId',
    method: 'get',
    params: query
  })
}

// 保存es资源（OCR内容）
export function saveOcrContent(data?: Object) {
  return request({
    url: '/archive/wb/es/saveOcrContent',
    method: 'post',
    data
  })
}

// 编辑es资源（OCR内容）
export function updateById(data?: Object) {
  return request({
    url: '/archive/wb/es/updateById',
    method: 'post',
    data
  })
}