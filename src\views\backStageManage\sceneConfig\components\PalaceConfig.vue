<template>
  <div class="palace-config">
    <div class="config-content">
      <!-- 基本信息配置 -->
      <div class="config-section">
        <h4 class="section-title">基本信息</h4>
        <el-form :model="formData" :rules="formRules" ref="formRef" label-width="100px">
          <el-form-item label="宫观名称" prop="name">
            <el-input v-model="formData.name" placeholder="请输入宫观名称" />
          </el-form-item>
          <el-form-item label="描述" prop="description">
            <el-input v-model="formData.description" type="textarea" :rows="3" placeholder="请输入宫观描述" />
          </el-form-item>
          <el-form-item label="排序" prop="sort">
            <el-input-number v-model="formData.sort" :min="1" :max="999" />
          </el-form-item>
        </el-form>
      </div>

      <!-- 全景视频配置 -->
      <div class="config-section">
        <h4 class="section-title">全景视频配置</h4>
        <div class="video-config">
          <el-form :model="videoData" :rules="videoRules" ref="videoFormRef" label-width="100px">
            <el-form-item label="视频URL" prop="videoUrl">
              <el-input v-model="videoData.videoUrl" placeholder="请输入全景视频URL" @blur="handleVideoUrlChange" />
            </el-form-item>
            <el-form-item label="视频时长" prop="duration">
              <el-input-number v-model="videoData.duration" :min="1" :max="3600" placeholder="秒" style="width: 200px" />
              <span class="form-tip">单位：秒</span>
            </el-form-item>
            <el-form-item label="封面图片" prop="coverImage">
              <div class="cover-upload">
                <el-upload class="cover-uploader" :show-file-list="false" :on-success="handleCoverSuccess"
                  :before-upload="beforeCoverUpload" action="/api/upload">
                  <img v-if="videoData.coverImage" :src="videoData.coverImage" class="cover-image" />
                  <div v-else class="cover-placeholder">
                    <el-icon class="cover-icon">
                      <Plus />
                    </el-icon>
                    <div class="cover-text">上传封面</div>
                  </div>
                </el-upload>
              </div>
            </el-form-item>
          </el-form>

          <!-- 视频预览 -->
          <div v-if="videoData.videoUrl" class="video-preview">
            <h5 class="preview-title">视频预览</h5>
            <div class="video-container">
              <video ref="videoRef" :src="videoData.videoUrl" controls preload="metadata" class="preview-video"
                @loadedmetadata="handleVideoLoaded" @timeupdate="handleTimeUpdate">
                您的浏览器不支持视频播放
              </video>
              <div class="video-info">
                <span class="current-time">当前时间: {{ formatTime(currentTime) }}</span>
                <span class="total-duration">总时长: {{ formatTime(videoData.duration || 0) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 信息节点时间轴 -->
      <div class="config-section">
        <h4 class="section-title">
          信息节点时间轴
          <el-button type="primary" size="small" @click="addNode" style="margin-left: 16px">
            <el-icon>
              <Plus />
            </el-icon>
            添加节点
          </el-button>
        </h4>
        <div class="timeline-container">
          <div v-if="nodeList.length > 0" class="timeline">
            <!-- 时间轴背景 -->
            <div class="timeline-track">
              <div class="timeline-progress" :style="{ width: progressWidth }"></div>
            </div>

            <!-- 节点标记 -->
            <div v-for="node in nodeList" :key="node.id" class="timeline-node"
              :style="{ left: getNodePosition(node.timestamp) }" @click="seekToNode(node)">
              <div class="node-marker" :class="{ active: selectedNodeId === node.id }">
                <el-icon>
                  <Location />
                </el-icon>
              </div>
              <div class="node-tooltip">
                <div class="tooltip-title">{{ node.name }}</div>
                <div class="tooltip-time">{{ formatTime(node.timestamp) }}</div>
              </div>
            </div>
          </div>

          <!-- 节点列表 -->
          <div class="node-list">
            <div v-for="node in nodeList" :key="node.id" class="node-item"
              :class="{ active: selectedNodeId === node.id }" @click="selectNode(node)">
              <div class="node-info">
                <div class="node-name">{{ node.name }}</div>
                <div class="node-time">{{ formatTime(node.timestamp) }}</div>
              </div>
              <div class="node-actions">
                <el-button type="primary" link size="small" @click.stop="editNode(node)">编辑</el-button>
                <el-button type="danger" link size="small" @click.stop="deleteNode(node)">删除</el-button>
              </div>
            </div>
            <el-empty v-if="nodeList.length === 0" description="暂无信息节点" :image-size="80" />
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="config-actions">
        <el-button @click="resetForm">重置</el-button>
        <el-button type="primary" @click="saveConfig" :loading="saving">保存配置</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { useMessage } from '/@/hooks/message';
import { Plus, Location } from '@element-plus/icons-vue';

interface Props {
  palaceData: any;
}

interface Emits {
  (e: 'update', data: any): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { success, error } = useMessage();

// 响应式数据
const formRef = ref();
const videoFormRef = ref();
const videoRef = ref();
const saving = ref(false);
const currentTime = ref(0);
const selectedNodeId = ref('');

const formData = reactive({
  name: '',
  description: '',
  sort: 1,
});

const videoData = reactive({
  videoUrl: '',
  duration: 0,
  coverImage: '',
});

const nodeList = ref<any[]>([]);

const formRules = {
  name: [
    { required: true, message: '请输入宫观名称', trigger: 'blur' },
  ],
  sort: [
    { required: true, message: '请输入排序', trigger: 'blur' },
  ],
};

const videoRules = {
  videoUrl: [
    { required: true, message: '请输入视频URL', trigger: 'blur' },
    { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' },
  ],
};

// 计算属性
const progressWidth = computed(() => {
  if (!videoData.duration || !currentTime.value) return '0%';
  return `${(currentTime.value / videoData.duration) * 100}%`;
});

// 方法
const formatTime = (seconds: number) => {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

const getNodePosition = (timestamp: number) => {
  if (!videoData.duration) return '0%';
  return `${(timestamp / videoData.duration) * 100}%`;
};

const handleVideoUrlChange = () => {
  if (videoRef.value) {
    videoRef.value.load();
  }
};

const handleVideoLoaded = () => {
  if (videoRef.value) {
    videoData.duration = Math.floor(videoRef.value.duration);
  }
};

const handleTimeUpdate = () => {
  if (videoRef.value) {
    currentTime.value = videoRef.value.currentTime;
  }
};

const handleCoverSuccess = (response: any) => {
  if (response.code === 200) {
    videoData.coverImage = response.data.url;
    success('封面上传成功');
  }
};

const beforeCoverUpload = (file: File) => {
  const isImage = file.type.startsWith('image/');
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isImage) {
    error('只能上传图片文件!');
    return false;
  }
  if (!isLt2M) {
    error('图片大小不能超过 2MB!');
    return false;
  }
  return true;
};

const addNode = () => {
  // TODO: 打开添加节点弹窗
  emit('update', { ...props.palaceData, action: 'addNode' });
};

const editNode = (node: any) => {
  // TODO: 打开编辑节点弹窗
};

const deleteNode = (node: any) => {
  // TODO: 删除节点
};

const selectNode = (node: any) => {
  selectedNodeId.value = node.id;
};

const seekToNode = (node: any) => {
  if (videoRef.value) {
    videoRef.value.currentTime = node.timestamp;
  }
  selectNode(node);
};

const resetForm = () => {
  formRef.value?.resetFields();
  videoFormRef.value?.resetFields();
  loadPalaceData();
};

const saveConfig = async () => {
  try {
    await formRef.value?.validate();
    await videoFormRef.value?.validate();

    saving.value = true;

    const updateData = {
      ...props.palaceData,
      ...formData,
      ...videoData,
      nodes: nodeList.value,
    };

    // TODO: 调用API保存配置
    await new Promise(resolve => setTimeout(resolve, 1000));

    emit('update', updateData);
    success('保存成功');
  } catch (err) {
    if (err !== false) {
      error('保存失败');
    }
  } finally {
    saving.value = false;
  }
};

const loadPalaceData = () => {
  if (props.palaceData) {
    Object.assign(formData, {
      name: props.palaceData.name || '',
      description: props.palaceData.description || '',
      sort: props.palaceData.sort || 1,
    });

    Object.assign(videoData, {
      videoUrl: props.palaceData.videoUrl || '',
      duration: props.palaceData.duration || 0,
      coverImage: props.palaceData.coverImage || '',
    });

    nodeList.value = props.palaceData.children || [];
  }
};

// 监听数据变化
watch(() => props.palaceData, loadPalaceData, { immediate: true });

onMounted(() => {
  loadPalaceData();
});
</script>

<style scoped lang="scss">
.palace-config {
  height: 100%;
  overflow: auto;
}

.config-content {
  padding: 20px;
}

.config-section {
  margin-bottom: 32px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 2px solid #409eff;
    display: inline-block;
  }
}

.video-config {
  .form-tip {
    margin-left: 12px;
    color: #909399;
    font-size: 12px;
  }
}

.cover-upload {
  .cover-uploader {
    :deep(.el-upload) {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: all 0.3s;
      width: 200px;
      height: 112px;

      &:hover {
        border-color: #409eff;
      }
    }
  }

  .cover-image {
    width: 200px;
    height: 112px;
    object-fit: cover;
    display: block;
  }

  .cover-placeholder {
    width: 200px;
    height: 112px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #8c939d;

    .cover-icon {
      font-size: 28px;
      margin-bottom: 8px;
    }

    .cover-text {
      font-size: 14px;
    }
  }
}

.video-preview {
  margin-top: 20px;

  .preview-title {
    font-size: 14px;
    font-weight: 600;
    color: #303133;
    margin: 0 0 12px 0;
  }

  .video-container {
    .preview-video {
      width: 100%;
      max-width: 600px;
      height: auto;
      border-radius: 8px;
    }

    .video-info {
      display: flex;
      justify-content: space-between;
      margin-top: 8px;
      font-size: 12px;
      color: #909399;
    }
  }
}

.timeline-container {
  .timeline {
    position: relative;
    height: 60px;
    margin-bottom: 20px;
    background: #f5f7fa;
    border-radius: 8px;
    padding: 20px;

    .timeline-track {
      position: absolute;
      top: 50%;
      left: 20px;
      right: 20px;
      height: 4px;
      background: #e4e7ed;
      border-radius: 2px;
      transform: translateY(-50%);

      .timeline-progress {
        height: 100%;
        background: #409eff;
        border-radius: 2px;
        transition: width 0.3s;
      }
    }

    .timeline-node {
      position: absolute;
      top: 50%;
      transform: translate(-50%, -50%);
      cursor: pointer;

      .node-marker {
        width: 20px;
        height: 20px;
        background: #409eff;
        border: 2px solid white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 12px;
        transition: all 0.3s;

        &:hover,
        &.active {
          transform: scale(1.2);
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
        }
      }

      .node-tooltip {
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 12px;
        white-space: nowrap;
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.3s;
        margin-bottom: 8px;

        .tooltip-title {
          font-weight: 500;
        }

        .tooltip-time {
          color: #ccc;
          margin-top: 2px;
        }
      }

      &:hover .node-tooltip {
        opacity: 1;
      }
    }
  }

  .node-list {
    max-height: 300px;
    overflow-y: auto;

    .node-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      margin-bottom: 8px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        border-color: #409eff;
        background: #f0f9ff;
      }

      &.active {
        border-color: #409eff;
        background: #ecf5ff;
      }

      .node-info {
        .node-name {
          font-size: 14px;
          font-weight: 500;
          color: #303133;
          margin-bottom: 4px;
        }

        .node-time {
          font-size: 12px;
          color: #909399;
        }
      }

      .node-actions {
        display: flex;
        gap: 8px;
        opacity: 0;
        transition: opacity 0.3s;

        .el-button {
          padding: 4px 8px;
          font-size: 12px;
        }
      }

      &:hover .node-actions {
        opacity: 1;
      }
    }
  }
}

.config-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}
</style>
