import request from "/@/utils/request"

export function fetchList(query?: Object) {
  return request({
    url: '/business/fxWaterResponseDetails/byPage',
    method: 'get',
    params: query
  })
}

export function addObj(obj?: Object) {
  return request({
    url: '/business/fxWaterResponseDetails',
    method: 'post',
    data: obj
  })
}

export function getObj(id?: string) {
  return request({
    url: '/business/fxWaterResponseDetails/' + id,
    method: 'get',
  })
}

export function delObjs(ids?: Object) {
  return request({
    url: '/business/fxWaterResponseDetails',
    method: 'delete',
    data: ids
  })
}

export function putObj(obj?: Object) {
  return request({
    url: '/business/fxWaterResponseDetails',
    method: 'put',
    data: obj
  })
}

