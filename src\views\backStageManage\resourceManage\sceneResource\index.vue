<template>
  <div class="layout-padding scene-resource">
    <!-- 主要内容区域 -->
    <div class="resource-content" v-show="!isShowEdit">
      <div class="content-layout">
        <!-- 左侧：建筑空间树 -->
        <div class="building-tree-panel">
          <div class="tree-content">
            <el-scrollbar height="100%">
              <query-tree ref="queryTreeRef" placeholder="建筑空间搜索" :props="{
                label: 'name',
                children: 'children',
                value: 'id',
              }" :query="treeData.queryList" :show-expand="true" @node-click="handleBuildingNodeClick"
                @treeDataLoaded="handleTreeDataLoaded">
                <template #default="{ node, data }">
                  <span style="display: inline-flex; align-items: center; width: 100%;">
                    <span>{{ node.label }}</span>
                    <el-tag v-if="data.resourceCount > 0" size="small" type="primary" style="margin-left: auto;">
                      {{ data.resourceCount }}
                    </el-tag>
                  </span>
                </template>
              </query-tree>
            </el-scrollbar>
          </div>
        </div>

        <!-- 右侧：资源列表 -->
        <div class="resource-list-panel">
          <div class="panel-header">
            <div class="header-left">
              <el-button type="primary" @click="handleAddResource">
                新增资源
              </el-button>
            </div>
            <div class="header-right" v-if="otherState.selectedBuilding">
              <el-select v-model="state.queryForm.resourceType" placeholder="资源类型" clearable
                style="width: 120px; margin-left: 10px;" @change="searchResources">
                <el-option label="附属建筑" value="1" />
                <el-option label="文物" value="2" />
                <el-option label="文化资源" value="3" />
              </el-select>
              <el-input v-model="state.queryForm.resourceName" placeholder="搜索资源"
                style="width: 200px; margin-left: 10px;" @keyup.enter="searchResources">
                <template #suffix>
                  <el-icon class="search-icon" @click="searchResources">
                    <Search />
                  </el-icon>
                </template>
              </el-input>
              <el-button @click="resetQuery" style="margin-left: 10px;" :icon="Refresh">
                重置
              </el-button>
            </div>
          </div>

          <div class="resource-list-content">
            <!-- 空状态 -->
            <div v-if="!otherState.selectedBuilding" class="empty-state">
              <el-icon class="empty-icon">
                <OfficeBuilding />
              </el-icon>
              <p class="empty-text">请从左侧选择建筑空间查看资源</p>
            </div>

            <!-- 资源表格 -->
            <div v-else class="table-container">
              <el-table ref="resourceTableRef" :data="state.dataList || []" style="width: 100%"
                v-loading="state.loading" header-cell-class-name="custom-table-header">
                <el-table-column prop="resourceName" label="资源名称" show-overflow-tooltip>
                  <template #default="scope">
                    <span style="display: inline-flex; align-items: center;">
                      <span>{{ scope.row.resourceName }}</span>
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="type" label="资源类型" width="200" align="center">
                  <template #default="scope">
                    <div :class="'type-tag type-tag-' + scope.row.resourceType">
                      {{ getTypeName(scope.row.resourceType) }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="350" align="left" fixed="right">
                  <template #default="scope">
                    <el-button link type="primary" @click.stop="editResource(scope.row)" style="color:#4659D3;">
                      编辑
                    </el-button>
                    <el-button link type="danger" @click.stop="deleteResource(scope.row)">
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- 分页组件 -->
            <div v-if="otherState.selectedBuilding" class="pagination-section">
              <pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle"
                v-bind="state.pagination" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加场景资源弹窗 -->
    <el-dialog v-model="showAddResourceDialog" title="新增资源" width="600px" :show-close="true"
      :close-on-click-modal="false">
      <div class="dialog-content">
        <el-form :model="resourceForm" label-width="120px" class="resource-form">
          <!-- 基本信息 -->
          <div class="form-section">
            <div class="section-title">
              <img src="/@/assets/img/backManage/Union.png" alt="基本信息" class="section-icon" />
              基本信息
            </div>
            <el-form-item label="资源封面" prop="cover">
              <ImageUpload class="custom-upload" v-model:imageUrl="resourceForm.cover" borderRadius="6px" width="120px"
                height="90px" uploadFileUrl="/exhibition/buildingResourceFile/cover">
                <template #empty>
                  <el-icon>
                    <Plus />
                  </el-icon>
                  <span>请上传封面</span>
                </template>
              </ImageUpload>
              <div class="upload-tip" style="padding-left: 10px;">图片支持JPG/PNG格式且最大5M</div>
            </el-form-item>

            <el-form-item label="资源名称" required>
              <el-input v-model="resourceForm.resourceName" placeholder="请输入资源名称" />
            </el-form-item>
            <el-form-item label="资源类型" required>
              <el-select v-model="resourceForm.resourceType" placeholder="请选择资源类型" style="width: 100%">
                <el-option label="附属建筑" value="1" />
                <el-option label="文物" value="2" />
                <el-option label="文化资源" value="3" />
              </el-select>
            </el-form-item>
            <el-form-item label="标签" required>
              <el-input v-model="resourceForm.labels" placeholder="请输入标签，多个标签用逗号分隔" />
            </el-form-item>
            <el-form-item label="资源简介">
              <el-input v-model="resourceForm.resourceDescription" placeholder="请输入资源简介" />
            </el-form-item>
          </div>

          <!-- 文件上传 -->
          <div class="form-section">
            <div class="section-title">
              <img src="/@/assets/img/backManage/Union.png" alt="基本信息" class="section-icon" />文件上传
            </div>

            <!-- 上传区域 -->
            <el-upload ref="uploadRef" class="resource-uploader" drag :http-request="handleCustomUpload"
              :file-list="resourceForm.fileList" :on-success="handleUploadSuccess" :before-upload="beforeUpload"
              :disabled="uploadProgress.isUploading" :on-change="handleFileChange"
              :show-file-list="false">

              <!-- 未上传状态 -->
              <div class="upload-content" v-if="!uploadProgress.isUploading && !resourceForm.uploadedFile">
                <div class="upload-text">
                  <p>将文件拖拽到此处，或<span class="upload-link">点击上传</span></p>
                </div>
              </div>

              <!-- 上传进度展示 -->
              <div class="upload-progress" v-if="uploadProgress.isUploading">
                <div class="progress-content">
                  <div class="progress-text">
                    <span class="file-name">{{ uploadProgress.fileName }}</span>
                    <span class="progress-percent">{{ uploadProgress.percentage }}%</span>
                  </div>
                  <el-progress :percentage="uploadProgress.percentage" :show-text="false" />
                </div>
              </div>

              <!-- 已上传文件信息 -->
              <div class="uploaded-file-info" v-if="!uploadProgress.isUploading && resourceForm.uploadedFile">
                <div class="file-icon">
                  <el-icon size="24">
                    <Document />
                  </el-icon>
                </div>
                <div class="file-details">
                  <div class="file-name">{{ resourceForm.uploadedFile.name }}</div>
                </div>
                <div class="file-actions">
                  <el-button type="danger" size="small" :icon="Delete" circle @click.stop="handleRemoveFile" />
                </div>
              </div>
            </el-upload>

            <!-- 上传提示信息 -->
            <div class="upload-tips">
              <p>1.三维模型请将3DTiles或手工模型的zip格式压缩文件整体上传</p>
              <p>2.总大小不超过10GB</p>
            </div>
          </div>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelAddResource">取消</el-button>
          <el-button type="primary" @click="confirmAddResource">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 编辑面板 -->
    <EditPanel v-if="isShowEdit" @refresh="getDataList" @close="closeEditPanel" :currentId="editResourceId" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineAsyncComponent, nextTick, computed } from 'vue';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { BasicTableProps, useTable } from '/@/hooks/table';
const ImageUpload = defineAsyncComponent(() => import("/@/components/Upload/Image.vue"));
import {
  Plus,
  Search,
  OfficeBuilding,
  UploadFilled,
  Refresh,
  Loading,
  Document,
  Delete,
} from '@element-plus/icons-vue';
import { getBuildingTree } from '/@/api/backStageManage/building';
import { getResourceList, uploadResourceFile, addResource, delResource } from '/@/api/backStageManage/resource';

const QueryTree = defineAsyncComponent(() => import('/@/components/QueryTree/index.vue'));
const Pagination = defineAsyncComponent(() => import('/@/components/Pagination/index.vue'));
const EditPanel = defineAsyncComponent(() => import('./components/EditPanel.vue'));

const { success, error } = useMessage();
const { confirm } = useMessageBox();
const uploadRef = ref();
// 响应式数据
const queryTreeRef = ref();
const queryRef = ref();
const resourceTableRef = ref();
const showAddResourceDialog = ref(false);

// 使用 BasicTableProps 结构
const state: BasicTableProps = reactive<BasicTableProps>({
  queryForm: {
    buildingId: '',
    resourceName: '',
    resourceType: '',
    current: 1,
    size: 10,
  },
  createdIsNeed: false,
  pageList: getResourceList,
  props: {
    item: 'records',
    totalCount: 'total',
  },
  isPage: true,
});

const { getDataList, currentChangeHandle, sizeChangeHandle } = useTable(state);

// 其他状态数据
const otherState = reactive({
  selectedBuilding: null as any,
  selectDatas: [] as any[],
});

const resourceForm = reactive({
  buildingId: '',
  buildingName: '',
  labels: '',
  resourceName: '',
  resourceType: '',
  resourceDescription: '',
  cover: "",
  fileList: [] as any[],
  modelId: '',
  uploadedFile: null as any
});

// 上传进度状态
const uploadProgress = reactive({
  isUploading: false,
  percentage: 0,
  fileName: '',
});

const isShowEdit = ref(false);
const editResourceId = ref('');

// 建筑空间树数据配置
const treeData = reactive({
  queryList: (name: string) => {
    return getBuildingTree({ buildingName: name || '' });
  },
});



// 计算属性：是否可以新增资源（只有选中二级节点才能新增）
const canAddResource = computed(() => {
  return otherState.selectedBuilding &&
    otherState.selectedBuilding.children === undefined &&
    otherState.selectedBuilding.parentId !== '0';
});

// 方法
// 处理建筑空间树节点点击
const handleBuildingNodeClick = (item: any, node: any) => {
  node.isCurrent = false;
  // 只有叶子节点才能选中
  if (!item.children || item.children.length === 0) {
    node.isCurrent = true;
    otherState.selectedBuilding = item;
    // 设置查询参数并加载数据
    state.queryForm.buildingId = item.id;
    getDataList();
  }
};

// 处理树数据加载完成
const handleTreeDataLoaded = (treeData: any, treeRef: any) => {
  // 自动选择第一个叶子节点
  const findFirstLeaf = (nodes: any[]): any => {
    for (const node of nodes) {
      if (!node.children || node.children.length === 0) {
        return node;
      }
      if (node.children && node.children.length > 0) {
        const leaf = findFirstLeaf(node.children);
        if (leaf) return leaf;
      }
    }
    return null;
  };

  const firstLeaf = findFirstLeaf(treeData);
  if (firstLeaf) {
    otherState.selectedBuilding = firstLeaf;
    // 设置查询参数并加载数据
    state.queryForm.buildingId = firstLeaf.id;
    getDataList();
    nextTick(() => {
      treeRef?.setCurrentKey(firstLeaf.id);
    });
  }
};

// 重置查询
const resetQuery = () => {
  queryRef.value?.resetFields();
  getDataList();
};


const getTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    1: 'success',
    2: 'danger',
    3: 'warning'
  };
  return colorMap[type] || '';
};

const getTypeName = (type: string) => {
  const nameMap: Record<string, string> = {
    1: '附属建筑',
    2: '文物',
    3: '文化资源'
  };
  return nameMap[type] || '其他';
};

const searchResources = () => {
  getDataList();
};

// 打开编辑弹窗
const editResource = (resource: any) => {
  editResourceId.value = resource.resourceId;
  isShowEdit.value = true;
};

const closeEditPanel = () => {
  editResourceId.value = '';
  isShowEdit.value = false;
};

const deleteResource = async (resource: any) => {
  try {
    await confirm(`确认删除资源"${resource.resourceName}"吗？`);
    await delResource(resource.resourceId);
    success('删除成功');
    // 刷新列表
    getDataList();
  } catch {
    // 用户取消删除
  }
};

// 文件上传相关方法
const handleCustomUpload = async (options: any) => {
  try {
    // 开始上传，设置进度状态
    uploadProgress.isUploading = true;
    uploadProgress.percentage = 0;
    uploadProgress.fileName = options.file.name;

    const uploadData = {
      buildingId: resourceForm.buildingId,
      resourceType: resourceForm.resourceType,
    };

    // 模拟上传进度（因为实际API可能不支持进度回调）
    const progressInterval = setInterval(() => {
      if (uploadProgress.percentage < 90) {
        uploadProgress.percentage += Math.random() * 20;
        if (uploadProgress.percentage > 90) {
          uploadProgress.percentage = 90;
        }
      }
    }, 200);

    const response = await uploadResourceFile(options.file, uploadData);

    // 清除进度定时器
    clearInterval(progressInterval);

    // 完成上传进度
    uploadProgress.percentage = 100;

    // 延迟一下让用户看到100%
    await new Promise(resolve => setTimeout(resolve, 500));

    // 单文件上传，替换现有文件
    resourceForm.fileList = [{
      name: options.file.name,
      url: response.data?.url || response.data?.filePath,
      status: 'success',
      uid: options.file.uid || Date.now(),
    }];
    resourceForm.modelId = response.data?.id || '';

    // 设置已上传文件信息
    resourceForm.uploadedFile = {
      name: options.file.name,
      size: options.file.size,
      url: response.data?.url || response.data?.filePath,
      id: response.data?.id || ''
    };

    // 重置上传状态
    uploadProgress.isUploading = false;
    uploadProgress.percentage = 0;
    uploadProgress.fileName = '';

    success('文件上传成功');
    options.onSuccess(response);
  } catch (err) {
    // 重置上传状态
    uploadProgress.isUploading = false;
    uploadProgress.percentage = 0;
    uploadProgress.fileName = '';

    error('文件上传失败');
    options.onError(err);
  }
};

const handleUploadSuccess = () => {
  // 这个方法现在主要用于兼容，实际上传逻辑在handleCustomUpload中
  // 不再显示成功提示，避免重复提示
};

// 移除已上传的文件
const handleRemoveFile = () => {
  resourceForm.uploadedFile = null;
  resourceForm.fileList = [];
  resourceForm.modelId = '';
};

const handleFileChange = () => {
  uploadRef.value.clearFiles();
};

const beforeUpload = (file: File) => {
  const isLt50M = file.size / 1024 / 1024 < 50 * 200;
  if (!isLt50M) {
    error('文件大小不能超过 10GB!');
    return false;
  }
  return true;
};

// 处理新增资源按钮点击
const handleAddResource = () => {
  if (!canAddResource.value) {
    error('请选择具体的建筑空间（二级节点）才能新增资源');
    return;
  }
  // 设置默认的建筑空间
  resourceForm.buildingId = otherState.selectedBuilding.id;
  resourceForm.buildingName = otherState.selectedBuilding.buildingName;
  showAddResourceDialog.value = true;
};

// 弹窗操作方法
const cancelAddResource = () => {
  // 重置表单
  resourceForm.buildingId = '';
  resourceForm.buildingName = '';
  resourceForm.resourceName = '';
  resourceForm.resourceType = '';
  resourceForm.resourceDescription = '';
  resourceForm.fileList = [];
  resourceForm.modelId = "";
  resourceForm.labels = "";
  resourceForm.cover = "";
  resourceForm.uploadedFile = null;
  showAddResourceDialog.value = false;
};

const confirmAddResource = async () => {
  // 表单验证
  if (!resourceForm.resourceName) {
    error('请输入资源名称');
    return;
  }
  if (!resourceForm.resourceType) {
    error('请选择资源类型');
    return;
  }
  // if (resourceForm.fileList.length === 0) {
  //   error('请上传一个文件');
  //   return;
  // }
  try {
    // 准备提交数据
    let data = {
      "buildingId": resourceForm.buildingId,
      "resourceName": resourceForm.resourceName,
      "cover": resourceForm.cover,
      "resourceType": resourceForm.resourceType,
      "labels": resourceForm.labels,
      "description": resourceForm.resourceDescription,
      "modelId": resourceForm.modelId
    }
    await addResource(data);
    success("新增成功");

  } catch (err: any) {
    useMessage().error(err.msg || '操作失败');
  } finally {
    cancelAddResource();
  }
  // 刷新列表
  getDataList();
};

onMounted(() => {
  // queryTree 组件会自动加载数据
});
</script>

<style scoped lang="scss">
.scene-resource {
  height: calc(100%);
  width: calc(100%) !important;
  box-sizing: border-box;

  .resource-content {
    height: 100%;
    overflow: hidden;
    background-color: white;
  }

  .content-layout {
    display: flex;
    height: 100%;
  }

  .building-tree-panel {
    width: 250px;
    padding: 20px;
    background: white;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #e4e7ed;

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      border-bottom: 1px solid #e4e7ed;

      .panel-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin: 0;
      }

    }

    .tree-content {
      flex: 1;
      overflow: hidden;
      min-height: 0;
    }
  }

  .resource-list-panel {
    padding: 20px;
    width: calc(100% - 250px);
    background: white;
    border-radius: 8px;
    display: flex;
    flex-direction: column;

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 10px;

      .header-left {
        .panel-title {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          margin: 0 0 4px 0;
        }

        .resource-count {
          font-size: 12px;
          color: #909399;
        }
      }

      .header-right {
        display: flex;

        .search-icon {
          cursor: pointer;
          color: #909399;

          &:hover {
            color: var(--el-color-primary, #A12F2F);
          }
        }
      }
    }

    .resource-list-content {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #909399;

        .empty-icon {
          font-size: 64px;
          margin-bottom: 16px;
        }

        .empty-text {
          font-size: 16px;
          margin: 0 0 16px 0;
        }
      }

      .table-container {
        flex: 1;
        overflow: hidden;
        min-height: 0;
        .type-tag {
          font-size: 14px;
          padding: 4px 12px;
          border-radius: 3px;
          display: inline-block;
        }
        .type-tag-1 {
          background: #E3F9E9;
          color: #2BA471;
        }
        .type-tag-2 {
          background: #F2F3FF;
          color: #0052D9;

        }
        .type-tag-3 {
          background: #FFF0ED;
          color: #D54941;
        }
      }

      .pagination-section {
        flex-shrink: 0;
        padding: 16px 20px;
      }
    }

    // 资源表格样式
    .el-table {
      background-color: #fff;
      height: 100% !important;

      :deep(.custom-table-header) {
        background-color: var(--next-bg-main-color, #f5f7fa);
        color: rgba(0, 0, 0, 0.4);
        font-weight: 400;
        font-size: 14px;
        height: 50px;
      }

      :deep(.el-table .cell) {
        display: flex;
        align-items: center;

        .el-icon {
          margin-right: 5px;
          font-size: 16px;
          color: #606266;
        }

        .el-button--link {
          padding: 5px 8px;

          &.el-button--primary {
            color: var(--el-color-primary);
          }

          &.el-button--danger {
            color: var(--el-color-danger);
          }
        }
      }

      :deep(.el-table__body-wrapper) {
        overflow-y: auto;
      }

      :deep(.el-table) {

        th.is-center .cell,
        td.is-center .cell {
          justify-content: center !important;
        }

        th.is-right .cell,
        td.is-right .cell {
          justify-content: flex-end !important;
        }

        th.is-left .cell,
        td.is-left .cell {
          justify-content: flex-start !important;
        }
      }
    }
  }
}

.resource-form {
  .form-section {
    margin-bottom: 32px;

    &:last-child {
      margin-bottom: 0;
    }

    .section-title {
      font-size: 14px;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.9);
      padding-bottom: 8px;
      position: relative;
      display: flex;
      align-items: center;

      .section-icon {
        width: 16px;
        height: 16px;
        margin-right: 8px;
        object-fit: contain;
      }
    }
  }

  .el-form-item {
    margin-bottom: 20px;

    :deep(.el-form-item__label) {
      font-weight: 500;
      color: #606266;
    }

    :deep(.el-input__wrapper) {
      border-radius: 4px;
    }

    :deep(.el-select .el-input__wrapper) {
      border-radius: 4px;
    }
  }
}

.resource-uploader {
  width: 100%;

  :deep(.el-upload-dragger) {
    width: 100%;
    height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 1px dashed #d9d9d9;
    border-radius: 8px;
    background: #fafafa;
    transition: all 0.3s;

    &:hover {
      border-color: var(--el-color-primary, #A12F2F);
    }

    .upload-content {
      text-align: center;

      .upload-icon {
        font-size: 48px;
        color: #c0c4cc;
        margin-bottom: 16px;
        transition: color 0.3s;
      }

      .upload-text {
        p {
          font-size: 14px;
          color: #606266;

          .upload-link {
            color: var(--el-color-primary, #A12F2F);
            cursor: pointer;

            &:hover {
              text-decoration: underline;
            }
          }
        }

        .upload-tip {
          margin-left: 10px;
          font-size: 12px;
          color: #909399;
          text-align: left;
        }
      }
    }

    // 已上传文件信息样式
    .uploaded-file-info {
      display: flex;
      align-items: center;
      padding: 16px;
      width: 100%;

      .file-icon {
        margin-right: 12px;
        color: #409eff;
      }

      .file-details {
        flex: 1;

        .file-name {
          font-size: 14px;
          font-weight: 500;
          color: #303133;
          margin-bottom: 4px;
          word-break: break-all;
        }

        .file-size {
          font-size: 12px;
          color: #909399;
        }
      }

      .file-actions {
        margin-left: 12px;
      }
    }

    &:hover .upload-icon {
      color: var(--el-color-primary, #A12F2F);
    }
  }

  :deep(.el-upload__tip) {
    margin-top: 8px;
    font-size: 12px;
    color: #909399;
  }

  // 上传进度样式
  .upload-progress {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    height: 140px;
    padding: 20px;

    .progress-icon {
      font-size: 32px;
      color: var(--el-color-primary, #A12F2F);
      margin-bottom: 16px;
      animation: rotate 2s linear infinite;
    }

    .progress-content {
      width: 100%;
      max-width: 300px;

      .progress-text {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        font-size: 14px;

        .file-name {
          color: #303133;
          font-weight: 500;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-right: 12px;
        }

        .progress-percent {
          color: var(--el-color-primary, #A12F2F);
          font-weight: 600;
          min-width: 40px;
          text-align: right;
        }
      }
    }
  }

  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }
}

// 上传提示样式
.upload-tips {
  margin-top: 12px;
  padding: 8px 0;

  p {
    margin: 0 0 4px 0;
    font-size: 12px;
    color: #909399;
    line-height: 1.4;

    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
