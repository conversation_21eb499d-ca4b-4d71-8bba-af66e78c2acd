<template>
  <div class="share-info-container">
    <div class="title-box">分享信息</div>
    <!-- 有效期设置 -->
    <div class="form-item">
      <label class="form-label">有效期</label>
      <el-select
        disabled
        v-model="shareForm.expireDay"
        class="expire-select"
        placeholder="请选择有效期"
        style="width: 100%"
      >
        <el-option label="永久" :value="0" />
        <el-option label="1天" :value="1" />
        <el-option label="7天" :value="7" />
        <el-option label="30天" :value="30" />
      </el-select>
    </div>

    <!-- 资源权限 -->
    <div class="form-item">
      <label class="form-label">资源权限</label>
      <div class="permission-options">
        <el-radio-group v-model="shareForm.sharePermission" disabled>
          <el-radio value="1" label="1">仅查看</el-radio>
          <el-radio value="2" label="2">可下载</el-radio>
        </el-radio-group>
      </div>
    </div>

    <!-- 资源权限 -->
    <div class="form-item">
      <label class="form-label">分享链接</label>
      <div class="permission-options">
        <el-input
          v-model="shareForm.shareUrl"
          style="width: 100%"
          :rows="2"
          type="textarea"
          placeholder="分享链接"
          disabled
        />
      </div>
    </div>

    <!-- 分享链接生成后的提示信息 -->
    <div class="share-info">
      <div class="info-box flex items-center">
        <el-icon class="info-icon"><Warning /></el-icon>
        <span class="info-text">
          有效期 {{ shareForm.expireDay === 0 ? '永久' : `${shareForm.expireDay} 天` }}，链接将在
          <span class="expire-date">{{ shareForm.expireTime }}</span>
          失效
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive } from "vue";
import { useRoute } from "vue-router";
const route = useRoute();

// 分享表单数据
const shareForm = reactive({
  expireDay: 0, // 默认7天
  sharePermission: "1", // 默认仅查看
  shareUrl: "",
  expireTime: ""
});

onMounted(() => {
  let shareParams = route.query.shareParams ? JSON.parse(route.query.shareParams) : {};
  shareForm.expireDay = Number(shareParams.expireDay) || 0;
  shareForm.sharePermission = shareParams.sharePermission || "1";
  shareForm.shareUrl = window.location.href;
  shareForm.expireTime = shareParams.expireTime || "";
});

</script>

<style lang="scss" scoped>
.share-info-container {
  background-color: #fff;
  padding: 15px 22px 200px 22px;
  margin-top: 28px;
  .title-box {
    font-weight: 600;
    font-size: 20px;
    color: #393939;
    margin-bottom: 28px;
  }
  .form-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 24px;

    .form-label {
      width: 65px;
      font-size: 14px;
      color: #606266;
      line-height: 32px;
      flex-shrink: 0;
      text-align: right;
      margin-right: 16px;
    }

    .expire-select {
      width: 200px;
    }

    .permission-options {
      flex: 1;

      :deep(.el-radio-group) {
        display: flex;
        align-items: center;

        .el-radio {
          margin-right: 24px;

          &:last-child {
            margin-right: 0;
          }
        }
      }
    }

    .password-section {
      display: flex;
      align-items: center;
      flex: 1;
      gap: 12px;

      .password-switch {
        flex-shrink: 0;
      }

      .password-input {
        flex: 1;
        max-width: 348px;

        .refresh-btn {
          padding: 0;
          min-height: auto;

          .el-icon {
            font-size: 16px;
            color: #909399;

            &:hover {
            }
          }
        }
      }
    }
  }
}

.share-info {
  margin-top: 20px;
  background-color: #FFF5E8;
  border-radius: 8px;
  width: 100%;
  .info-box {
    display: flex;
    align-items: flex-start;
    padding: 12px 16px;
    border-radius: 4px;

    .info-icon {
      font-size: 16px;
      margin-right: 8px;
      margin-top: 4px;
      flex-shrink: 0;
    }

    .info-text {
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      vertical-align: middle;
      color: #554242;

      .expire-date {
        color: var(--el-color-primary);
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// Element Plus 组件样式覆盖
:deep(.el-select) {
  .el-input__wrapper {
    border-radius: 4px;
  }
}

:deep(.el-radio) {
  .el-radio__label {
    font-size: 14px;
    color: #606266;
  }

  &.is-checked {
    .el-radio__label {
    }
  }
}

:deep(.el-switch) {
  .el-switch__core {
    border-radius: 10px;
  }
}

:deep(.el-input) {
  .el-input__wrapper {
    border-radius: 4px;

    &:hover {
      box-shadow: 0 0 0 1px #c0c4cc inset;
    }
  }
}

:deep(.el-button) {
  border-radius: 4px;
  font-size: 14px;
}
</style>
