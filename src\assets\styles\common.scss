.custom-main-container {
    display: flex;
    width: 100%;
    height: 100%;
    .left-tree-container {
        width: 260px;
        margin-right: 12px;
        border-radius: 24px;
        background: #fff;
        .title-box {
            padding: 13px 19px;
            border-bottom: 1px solid #c0c0c0;
            .title-left-box {
                color: #554242;
                font-family: PingFang SC;
                font-size: 18px;
                font-style: normal;
                font-weight: 500;
                line-height: normal;
            }
        }
        .tree-content {
            height: calc(100% - 51px);
            display: flex;
            flex-direction: column;
            > div {
                flex-basis: 50%;
                padding: 7px 12px 24px;
                height: 50%;
                .sub-title {
                    color: #554242;
                    font-family: PingFang SC;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 24px;
                    margin-bottom: 12px;
                    position: relative;
                    padding-left: 8px;
                }
                .sub-title::before {
                    content: "";
                    position: absolute;
                    width: 2px;
                    height: 12px;
                    background: #554242;
                    left: 0;
                    top: 50%;
                    transform: translateY(-50%);
                }
                .scrollbar-box {
                    height: calc(100% - 31px);
                    border-radius: 10px;
                    background: #fafafa;
                    padding: 12px 8px;
                }
            }
        }
    }
    .single-tree {
        .content-box {
            padding: 24px 20px;
            height: calc(100% - 51px);
            .sub-title {
                width: 220px;
                height: 32px;
                flex-shrink: 0;
                border-radius: 4px;
                background: #e0272726;
                display: flex;
                align-items: center;
                padding: 0 12px;
                margin-bottom: 12px;
                .sub-title-left {
                    flex-grow: 1;
                    color: #fd4949;
                    font-family: PingFang SC;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 500;
                }
                .sub-title-right {
                    display: flex;
                    align-items: center;
                    width: 14px;
                    height: 14px;
                    .el-dropdown {
                        width: 100%;
                        height: 100%;
                        .el-icon {
                            color: #fd4949;
                        }
                    }
                }
            }
            .el-scrollbar {
                height: calc(100% - 44px);
            }
        }
    }
    .right-content {
        flex-grow: 1;
        width: calc(100% - 272px);
        border-radius: 20px;
        background: #fff;
        .title-box {
            padding: 20px 0 10px 24px;
            border-bottom: 1px solid #c0c0c0;
            .title-left-box {
                color: #554242;
                font-family: Source Han Sans CN;
                font-size: 18px;
                font-style: normal;
                font-weight: 400;
                line-height: 20px;
            }
        }
        .content-box {
            height: calc(100% - 57px);
            padding: 24px;
            .search-btn-box {
                display: flex;
                .btn-group-box {
                    flex-grow: 1;
                    .el-button {
                        border-radius: 5px;
                        // border: 1px solid #2897e8;
                        // background: #ebf6ff;
                        padding: 5px 16px;
                        > span {
                            // color: #2897e8;
                            text-align: center;
                            font-family: "PingFang SC";
                            font-size: 14px;
                            font-style: normal;
                            font-weight: 500;
                            line-height: 22px;
                        }
                    }
                    .el-button--primary {
                        // background: #2897e8;
                        font-family: PingFang SC;
                        font-size: 14px;
                        font-style: normal;
                        font-weight: 500;
                        > span {
                            color: #fff;
                        }
                    }
                }
                .search-box {
                    .el-form {
                        .el-form-item {
                            margin-bottom: 0 !important;
                        }
                    }
                }
            }
            .el-form {
                .el-row {
                    .el-col {
                        padding: 0!important;
                    }
                }
            }
        }

        .tabs-content {
            height: 100%;
            .el-tabs {
                height: 100%;
                .el-tabs__header {
                    padding: 11px 0 0 0;
                    margin: 0;
                    .el-tabs__nav-wrap {
                        .el-tabs__nav-scroll {
                            padding-left: 24px;
                        }
                    }
                }
                .el-tabs__content {
                    height: calc(100% - 51px);
                    .el-tab-pane {
                        height: 100%;
                    }
                }
            }
        }
    }
}

.custom-tree-control {
    background-color: transparent;
    .el-tree-node.is-current {
        >.el-tree-node__content {
            background-color: #e9e9e9;
        }
    }
    // .el-tree-node.is-current.is-expanded {

    // }
    .el-tree-node {
        //height: 32px;
        border-radius: 4px;
        // padding: 6px 12px;
        .el-tree-node__content {
            height: 100%;
            padding: 6px 12px;
            margin-bottom: 4px;

            .el-icon {
                padding: 0;
                width: 16px;
                height: 16px;
                margin-right: 4px;
            }
        }
        .el-tree-node__content:hover {
            background: #e9e9e9;
        }
    }
    .el-tree-node {
        .el-tree-node__children {
            .is-current {
                background-color: #e9e9e9 !important;
            }
            .is-current:focus {
                .el-tree-node__content {
                    background-color: #e9e9e9 !important;
                }
            }
        }
    }
}

.custom-table-control {
    .el-table__inner-wrapper {
        .el-table__header-wrapper {
            .el-table__header {
                thead {
                    // border-bottom: 1px solid #eceaf3;
                    border-bottom: 1px solid #E7E7E7;
                    background: #F3F3F3;
                    tr {
                        th {
                            height: 46px;
                            background: #F3F3F3 !important;

                            .cell {
                                font-family: Source Han Sans CN;
                                font-weight: 400;
                                font-size: 14px;
                                line-height: 22px;
                                letter-spacing: 0%;
                                color: #00000066;
                            }
                        }
                    }
                }
            }
        }
        .el-table__body-wrapper {
            .el-scrollbar {
                .el-scrollbar__wrap {
                    .el-scrollbar__view {
                        .el-table__body {
                            tbody {
                                tr {
                                    td {
                                        height: 46px;
                                        border-bottom: 1px solid #E7E7E7;
                                        padding: 0;
                                        .cell {

                                            font-family: Source Han Sans CN;
                                            font-weight: 400;
                                            font-size: 14px;
                                            line-height: 22px;
                                            letter-spacing: 0%;
                                            color: #000000E5;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

.custom-tabs-control {
    height: 100%;
    .el-tabs {
        height: 100%;
        .el-tabs__header {
            padding: 11px 0 0 0;
            margin: 0;
            .el-tabs__nav-wrap {
                .el-tabs__nav-scroll {
                    padding-left: 24px;
                }
            }
        }
        .el-tabs__content {
            height: calc(100% - 51px);
            .el-tab-pane {
                height: 100%;

                .content-box {
                    height: 100%;
                    padding: 24px;

                    .search-btn-box {
                        display: flex;
                        .btn-group-box {
                            flex-grow: 1;
                            .el-button {
                                border-radius: 5px;
                                border: 1px solid #2897e8;
                                background: #ebf6ff;
                                padding: 5px 16px;
                                > span {
                                    color: #2897e8;
                                    text-align: center;
                                    font-family: "PingFang SC";
                                    font-size: 14px;
                                    font-style: normal;
                                    font-weight: 500;
                                    line-height: 22px;
                                }
                            }
                            .el-button--primary {
                                background: #2897e8;
                                font-family: PingFang SC;
                                font-size: 14px;
                                font-style: normal;
                                font-weight: 500;
                                > span {
                                    color: #fff;
                                }
                            }
                        }
                        .search-box {
                            .el-form {
                                .el-form-item {
                                    margin-bottom: 0 !important;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

.table-outerbox-padding {
  padding: 25px 20px 30px!important;
  border-radius: 5px!important;
  border:none!important;
  position: relative;
}

.tree-outerbox-padding {
  padding: 20px!important;
  border-radius: 5px!important;
  border:none!important;
}

.custom-pagination-control {
    position: absolute;
    bottom: 30px;
    right: 20px;
    left: 20px;
    width: calc(100% - 40px);

    .el-pagination__total {
        flex-grow: 1;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        color: rgba(0, 0, 0, 0.6);
    }
    .el-pagination__sizes {
        .el-select {
            width: 112px;
            height: 32px;
            .select-trigger {
                width: 100%;
                .el-input {
                    width: 100%;
                }
            }
        }
    }
    .btn-prev, .btn-next {
        background-color: transparent!important;
        i {
            color:rgba(0, 0, 0, 0.26);
        }
    }
    .btn-next {
        margin-right: 0!important;
    }
    .el-pager {
        .number {
            width: 32px;
            height: 32px;
            border-radius: 3px;
            border-width: 1px;
            border: 1px solid rgba(220, 220, 220, 1);
            background-color: rgba(255, 255, 255, 1)!important;
            font-family: Source Han Sans CN;
            font-weight: 400;
            font-size: 14px;
            line-height: 22px;
            text-align: center;
            color: rgba(0, 0, 0, 0.9);

        }
        .active {
            border: none!important;
        }
        
    }
    .el-pagination__jump {
        .el-pagination__goto, .el-pagination__classifier {
            font-family: Source Han Sans CN;
            font-weight: 400;
            font-size: 14px;
            line-height: 22px;
            color: rgba(0, 0, 0, 0.6);
        }
        .el-input {
            width: 60px;
            margin-right: 0;
            .el-input__wrapper {
                width: 100%;
            }
        }
    }

}