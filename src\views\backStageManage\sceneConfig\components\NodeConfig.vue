<template>
  <div class="node-config">
    <div class="config-content">
      <!-- 基本信息配置 -->
      <div class="config-section">
        <h4 class="section-title">基本信息</h4>
        <el-form :model="formData" :rules="formRules" ref="formRef" label-width="120px">
          <el-form-item label="节点名称" prop="name">
            <el-input v-model="formData.name" placeholder="请输入节点名称" />
          </el-form-item>
          <el-form-item label="节点描述" prop="description">
            <el-input
              v-model="formData.description"
              type="textarea"
              :rows="3"
              placeholder="请输入节点描述"
            />
          </el-form-item>
          <el-form-item label="触发时间" prop="timestamp">
            <div class="timestamp-input">
              <el-input-number
                v-model="timestampMinutes"
                :min="0"
                :max="59"
                placeholder="分"
                @change="updateTimestamp"
              />
              <span class="time-separator">:</span>
              <el-input-number
                v-model="timestampSeconds"
                :min="0"
                :max="59"
                placeholder="秒"
                @change="updateTimestamp"
              />
              <span class="form-tip">节点在视频播放到此时间时触发显示</span>
            </div>
          </el-form-item>
          <el-form-item label="显示位置" prop="position">
            <el-select v-model="formData.position" placeholder="请选择显示位置">
              <el-option label="左上角" value="top-left" />
              <el-option label="上方中央" value="top-center" />
              <el-option label="右上角" value="top-right" />
              <el-option label="左侧中央" value="middle-left" />
              <el-option label="屏幕中央" value="center" />
              <el-option label="右侧中央" value="middle-right" />
              <el-option label="左下角" value="bottom-left" />
              <el-option label="下方中央" value="bottom-center" />
              <el-option label="右下角" value="bottom-right" />
            </el-select>
          </el-form-item>
          <el-form-item label="显示时长" prop="duration">
            <el-input-number
              v-model="formData.duration"
              :min="1"
              :max="60"
              placeholder="秒"
              style="width: 200px"
            />
            <span class="form-tip">节点信息显示的持续时间（秒）</span>
          </el-form-item>
          <el-form-item label="排序" prop="sort">
            <el-input-number v-model="formData.sort" :min="1" :max="999" />
          </el-form-item>
        </el-form>
      </div>

      <!-- 内容配置 -->
      <div class="config-section">
        <h4 class="section-title">内容配置</h4>
        <el-form :model="contentData" :rules="contentRules" ref="contentFormRef" label-width="120px">
          <el-form-item label="内容类型" prop="contentType">
            <el-radio-group v-model="contentData.contentType" @change="handleContentTypeChange">
              <el-radio label="text">纯文本</el-radio>
              <el-radio label="image">图片+文字</el-radio>
              <el-radio label="video">视频+文字</el-radio>
              <el-radio label="audio">音频+文字</el-radio>
              <el-radio label="model">模型+文字</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 文本内容 -->
          <el-form-item label="文本内容" prop="textContent">
            <el-input
              v-model="contentData.textContent"
              type="textarea"
              :rows="4"
              placeholder="请输入文本内容"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>

          <!-- 图片内容 -->
          <el-form-item v-if="contentData.contentType === 'image'" label="图片" prop="imageUrl">
            <div class="media-upload">
              <el-upload
                class="image-uploader"
                :show-file-list="false"
                :on-success="handleImageSuccess"
                :before-upload="beforeImageUpload"
                action="/api/upload"
              >
                <img v-if="contentData.imageUrl" :src="contentData.imageUrl" class="uploaded-image" />
                <div v-else class="upload-placeholder">
                  <el-icon class="upload-icon"><Plus /></el-icon>
                  <div class="upload-text">上传图片</div>
                </div>
              </el-upload>
            </div>
          </el-form-item>

          <!-- 视频内容 -->
          <el-form-item v-if="contentData.contentType === 'video'" label="视频URL" prop="videoUrl">
            <el-input v-model="contentData.videoUrl" placeholder="请输入视频URL" />
          </el-form-item>

          <!-- 音频内容 -->
          <el-form-item v-if="contentData.contentType === 'audio'" label="音频URL" prop="audioUrl">
            <el-input v-model="contentData.audioUrl" placeholder="请输入音频URL" />
          </el-form-item>

          <!-- 模型内容 -->
          <el-form-item v-if="contentData.contentType === 'model'" label="模型URL" prop="modelUrl">
            <el-input v-model="contentData.modelUrl" placeholder="请输入3D模型URL" />
          </el-form-item>
        </el-form>
      </div>

      <!-- 样式配置 -->
      <div class="config-section">
        <h4 class="section-title">样式配置</h4>
        <el-form :model="styleData" label-width="120px">
          <el-form-item label="背景颜色">
            <el-color-picker v-model="styleData.backgroundColor" />
          </el-form-item>
          <el-form-item label="文字颜色">
            <el-color-picker v-model="styleData.textColor" />
          </el-form-item>
          <el-form-item label="字体大小">
            <el-slider
              v-model="styleData.fontSize"
              :min="12"
              :max="24"
              :step="1"
              show-input
              style="width: 300px"
            />
          </el-form-item>
          <el-form-item label="透明度">
            <el-slider
              v-model="styleData.opacity"
              :min="0.1"
              :max="1"
              :step="0.1"
              show-input
              style="width: 300px"
            />
          </el-form-item>
          <el-form-item label="边框圆角">
            <el-slider
              v-model="styleData.borderRadius"
              :min="0"
              :max="20"
              :step="1"
              show-input
              style="width: 300px"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 预览效果 -->
      <div class="config-section">
        <h4 class="section-title">预览效果</h4>
        <div class="preview-container">
          <div class="preview-area" :class="`position-${formData.position}`">
            <div
              class="node-preview"
              :style="{
                backgroundColor: styleData.backgroundColor,
                color: styleData.textColor,
                fontSize: styleData.fontSize + 'px',
                opacity: styleData.opacity,
                borderRadius: styleData.borderRadius + 'px',
              }"
            >
              <div v-if="contentData.contentType === 'image' && contentData.imageUrl" class="preview-image">
                <img :src="contentData.imageUrl" alt="预览图片" />
              </div>
              <div v-if="contentData.textContent" class="preview-text">
                {{ contentData.textContent }}
              </div>
              <div v-if="!contentData.textContent" class="preview-placeholder">
                节点内容预览
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="config-actions">
        <el-button @click="resetForm">重置</el-button>
        <el-button type="primary" @click="saveConfig" :loading="saving">保存配置</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { useMessage } from '/@/hooks/message';
import { Plus } from '@element-plus/icons-vue';

interface Props {
  nodeData: any;
}

interface Emits {
  (e: 'update', data: any): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { success, error } = useMessage();

// 响应式数据
const formRef = ref();
const contentFormRef = ref();
const saving = ref(false);

const formData = reactive({
  name: '',
  description: '',
  timestamp: 0,
  position: 'center',
  duration: 5,
  sort: 1,
});

const contentData = reactive({
  contentType: 'text',
  textContent: '',
  imageUrl: '',
  videoUrl: '',
  audioUrl: '',
  modelUrl: '',
});

const styleData = reactive({
  backgroundColor: '#ffffff',
  textColor: '#303133',
  fontSize: 14,
  opacity: 0.9,
  borderRadius: 8,
});

// 时间输入相关
const timestampMinutes = ref(0);
const timestampSeconds = ref(0);

const formRules = {
  name: [
    { required: true, message: '请输入节点名称', trigger: 'blur' },
  ],
  timestamp: [
    { required: true, message: '请设置触发时间', trigger: 'blur' },
  ],
  position: [
    { required: true, message: '请选择显示位置', trigger: 'change' },
  ],
  duration: [
    { required: true, message: '请设置显示时长', trigger: 'blur' },
  ],
};

const contentRules = {
  textContent: [
    { required: true, message: '请输入文本内容', trigger: 'blur' },
  ],
  imageUrl: [
    { required: true, message: '请上传图片', trigger: 'change' },
  ],
  videoUrl: [
    { required: true, message: '请输入视频URL', trigger: 'blur' },
  ],
  audioUrl: [
    { required: true, message: '请输入音频URL', trigger: 'blur' },
  ],
  modelUrl: [
    { required: true, message: '请输入模型URL', trigger: 'blur' },
  ],
};

// 方法
const updateTimestamp = () => {
  formData.timestamp = timestampMinutes.value * 60 + timestampSeconds.value;
};

const parseTimestamp = (timestamp: number) => {
  timestampMinutes.value = Math.floor(timestamp / 60);
  timestampSeconds.value = timestamp % 60;
};

const handleContentTypeChange = () => {
  // 清空其他类型的内容
  if (contentData.contentType !== 'image') contentData.imageUrl = '';
  if (contentData.contentType !== 'video') contentData.videoUrl = '';
  if (contentData.contentType !== 'audio') contentData.audioUrl = '';
  if (contentData.contentType !== 'model') contentData.modelUrl = '';
};

const handleImageSuccess = (response: any) => {
  if (response.code === 200) {
    contentData.imageUrl = response.data.url;
    success('图片上传成功');
  }
};

const beforeImageUpload = (file: File) => {
  const isImage = file.type.startsWith('image/');
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isImage) {
    error('只能上传图片文件!');
    return false;
  }
  if (!isLt2M) {
    error('图片大小不能超过 2MB!');
    return false;
  }
  return true;
};

const resetForm = () => {
  formRef.value?.resetFields();
  contentFormRef.value?.resetFields();
  loadNodeData();
};

const saveConfig = async () => {
  try {
    await formRef.value?.validate();
    await contentFormRef.value?.validate();
    
    saving.value = true;
    
    const updateData = {
      ...props.nodeData,
      ...formData,
      content: contentData,
      style: styleData,
    };
    
    // TODO: 调用API保存配置
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    emit('update', updateData);
    success('保存成功');
  } catch (err) {
    if (err !== false) {
      error('保存失败');
    }
  } finally {
    saving.value = false;
  }
};

const loadNodeData = () => {
  if (props.nodeData) {
    Object.assign(formData, {
      name: props.nodeData.name || '',
      description: props.nodeData.description || '',
      timestamp: props.nodeData.timestamp || 0,
      position: props.nodeData.position || 'center',
      duration: props.nodeData.duration || 5,
      sort: props.nodeData.sort || 1,
    });
    
    if (props.nodeData.content) {
      Object.assign(contentData, props.nodeData.content);
    }
    
    if (props.nodeData.style) {
      Object.assign(styleData, props.nodeData.style);
    }
    
    parseTimestamp(formData.timestamp);
  }
};

// 监听数据变化
watch(() => props.nodeData, loadNodeData, { immediate: true });

onMounted(() => {
  loadNodeData();
});
</script>

<style scoped lang="scss">
.node-config {
  height: 100%;
  overflow: auto;
}

.config-content {
  padding: 20px;
}

.config-section {
  margin-bottom: 32px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 2px solid #409eff;
    display: inline-block;
  }
}

.timestamp-input {
  display: flex;
  align-items: center;
  gap: 8px;

  .el-input-number {
    width: 80px;
  }

  .time-separator {
    font-size: 16px;
    font-weight: bold;
    color: #606266;
  }

  .form-tip {
    margin-left: 12px;
    color: #909399;
    font-size: 12px;
  }
}

.form-tip {
  margin-left: 12px;
  color: #909399;
  font-size: 12px;
}

.media-upload {
  .image-uploader {
    :deep(.el-upload) {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: all 0.3s;
      width: 200px;
      height: 150px;

      &:hover {
        border-color: #409eff;
      }
    }
  }

  .uploaded-image {
    width: 200px;
    height: 150px;
    object-fit: cover;
    display: block;
  }

  .upload-placeholder {
    width: 200px;
    height: 150px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #8c939d;

    .upload-icon {
      font-size: 28px;
      margin-bottom: 8px;
    }

    .upload-text {
      font-size: 14px;
    }
  }
}

.preview-container {
  .preview-area {
    position: relative;
    width: 100%;
    height: 300px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    overflow: hidden;

    &.position-top-left .node-preview { top: 20px; left: 20px; }
    &.position-top-center .node-preview { top: 20px; left: 50%; transform: translateX(-50%); }
    &.position-top-right .node-preview { top: 20px; right: 20px; }
    &.position-middle-left .node-preview { top: 50%; left: 20px; transform: translateY(-50%); }
    &.position-center .node-preview { top: 50%; left: 50%; transform: translate(-50%, -50%); }
    &.position-middle-right .node-preview { top: 50%; right: 20px; transform: translateY(-50%); }
    &.position-bottom-left .node-preview { bottom: 20px; left: 20px; }
    &.position-bottom-center .node-preview { bottom: 20px; left: 50%; transform: translateX(-50%); }
    &.position-bottom-right .node-preview { bottom: 20px; right: 20px; }

    .node-preview {
      position: absolute;
      max-width: 300px;
      padding: 16px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

      .preview-image {
        margin-bottom: 12px;

        img {
          width: 100%;
          max-height: 120px;
          object-fit: cover;
          border-radius: 4px;
        }
      }

      .preview-text {
        line-height: 1.5;
        word-break: break-all;
      }

      .preview-placeholder {
        color: #909399;
        font-style: italic;
      }
    }
  }
}

.config-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}
</style>
