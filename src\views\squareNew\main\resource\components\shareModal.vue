<template>
  <el-dialog
    v-model="visible"
    title="资源分享"
    width="660px"
    :before-close="handleClose"
    :close-on-click-modal="false"
    class="share-modal"
  >
    <div class="share-content">
      <!-- 有效期设置 -->
      <div class="form-item">
        <label class="form-label">有效期</label>
        <el-select
          v-model="shareForm.expireTime"
          class="expire-select"
          placeholder="请选择有效期"
          style="width: 400px"
        >
          <el-option v-for="item in timeValues" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>

      <!-- 资源权限 -->
      <div class="form-item">
        <label class="form-label">资源权限</label>
        <div class="permission-options">
          <el-radio-group v-model="shareForm.permission">
            <el-radio value="1" label="1">仅查看</el-radio>
            <el-radio value="2" label="2">可下载</el-radio>
          </el-radio-group>
        </div>
      </div>

      <!-- 密码保护 -->
      <div class="form-item">
        <label class="form-label">密码保护</label>
        <div class="password-section">
          <el-switch
            v-model="shareForm.passwordEnabled"
            class="password-switch"
            @change="onPasswordToggle"
          />
          <el-input
            v-if="shareForm.passwordEnabled"
            v-model="shareForm.password"
            placeholder="请输入密码"
            class="password-input"
            maxlength="20"
            readonly
          >
            <template #suffix>
              <el-button link @click="generateRandomPassword" class="refresh-btn">
                <el-icon><Refresh /></el-icon>
              </el-button>
            </template>
          </el-input>
          <el-button
            v-if="shareForm.passwordEnabled"
            class="password-btn"
            @click="generateRandomPassword"
            type="primary"
          >
            生成密码
          </el-button>
        </div>
      </div>

      <!-- 资源权限 -->
      <div class="form-item">
        <label class="form-label">分享链接</label>
        <div class="permission-options flex">
          <el-input
            v-model="shareForm.shareUrl"
            style="width: 400px"
            :rows="2"
            type="textarea"
            readonly
            placeholder="分享链接"
          />
          <el-button
            v-if="shareForm.shareUrl"
            class="copy-btn"
            @click="copyStr(shareForm.shareUrl)"
            type="primary"
            plain
          >
            复制链接
          </el-button>
        </div>
      </div>

      <!-- 分享链接生成后的提示信息 -->
      <div v-if="showData.expireTime" class="share-info">
        <div class="info-box">
          <el-icon class="info-icon"><Warning /></el-icon>
          <span class="info-text">
            有效期 {{ showData.expireDay ? showData.expireDay + '天' : '永久' }}，链接将在
            <span class="expire-date">{{ showData.expireTime }}</span>
            失效
          </span>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitShare">
          生成链接
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import { ElMessage } from "element-plus";
import { Refresh } from "@element-plus/icons-vue";
import { shareUrl, share } from "/@/api/squareNew";
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  resourceId: {
		type: String,
		default: "",
	},
  tabName: {
		type: String,
		default: "",
	},
});

const timeValues = [
  { label: '永久', value: 0 },
  { label: '1天', value: 1 },
  { label: '7天', value: 7 },
  { label: '30天', value: 30 },
];

const showData = ref({
  expireDay: "",
  expireTime: ""
});

// Emits
const emit = defineEmits(["update:visible", "close"]);

// 响应式数据
const visible = computed({
  get: () => props.visible,
  set: (val) => emit("update:visible", val),
});

// 分享表单数据
const shareForm = reactive({
  expireTime: 7, // 默认7天
  permission: "1", // 默认仅查看
  passwordEnabled: false,
  password: "",
  shareUrl: ""
});
// 方法
const handleClose = () => {
  visible.value = false;
  emit("close");
};

// 密码开关切换
const onPasswordToggle = (enabled: boolean) => {
  if (enabled && !shareForm.password) {
    generateRandomPassword();
  }
};

// 生成随机密码
const generateRandomPassword = () => {
  const chars = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678";
  let password = "";
  for (let i = 0; i < 4; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  shareForm.password = password;
};

// 生成链接
const submitShare = () => {
  shareUrl({
    tableName: props.tabName,
    resourceId: props.resourceId,
    expireSeconds: shareForm.expireTime * 86400,
    password: shareForm.password
  }).then((res) => {
    let shareParams = {
      sharePermission: shareForm.permission,
      passwordEnabled: shareForm.passwordEnabled,
      expireDay: res.data.expireDay,
      expireTime: res.data.expireTime
    };
    if (res.code == 0) {
      showData.value = res.data;
      shareForm.shareUrl = window.location.href + '&token=' + res.data.token + '&shareParams=' + JSON.stringify(shareParams);
    }
  });
}

// 复制功能
const copyStr = (str: string) => { // 复制链接
  if (!str) {
    ElMessage.warning("暂无链接内容")
    return
  }
  let inputDom = document.createElement('input');  // 创建一个input元素
  inputDom.setAttribute('readonly', 'readonly'); // 防止手机上弹出软键盘
  inputDom.value = str; // 给input元素赋值
  document.body.appendChild(inputDom); // 添加到body
  inputDom.select(); //选中input元素的内容
  document.execCommand('Copy'); // 执行浏览器复制命令
  inputDom.style.display = 'none';
  inputDom.remove(); // 移除input元素
  ElMessage.success({
    message: '复制到剪贴板成功',
    type: 'success'
  });
  // 执行分享接口，记录分享次数
  share({
    tabName: props.tabName,
    resourceId: props.resourceId,
  })
}
</script>

<style lang="scss" scoped>
.share-modal {
  :deep(.el-dialog__header) {
    padding: 20px 20px 10px;
    border-bottom: 1px solid #ebeef5;
  }

  :deep(.el-dialog__body) {
    padding: 20px;
  }

  :deep(.el-dialog__footer) {
    padding: 10px 20px 20px;
    border-top: 1px solid #ebeef5;
  }
}

.share-content {
  .form-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 24px;

    .form-label {
      width: 80px;
      font-size: 14px;
      color: #606266;
      line-height: 32px;
      flex-shrink: 0;
      text-align: right;
      margin-right: 16px;
    }

    .expire-select {
      width: 200px;
    }

    .permission-options {
      flex: 1;

      :deep(.el-radio-group) {
        display: flex;
        align-items: center;

        .el-radio {
          margin-right: 24px;

          &:last-child {
            margin-right: 0;
          }
        }
      }
      .copy-btn {
        margin-left: 12px;
      }
    }

    .password-section {
      display: flex;
      align-items: center;
      flex: 1;
      gap: 12px;

      .password-switch {
        flex-shrink: 0;
      }

      .password-input {
        flex: 1;
        max-width: 348px;

        .refresh-btn {
          padding: 0;
          min-height: auto;

          .el-icon {
            font-size: 16px;
            color: #909399;

            &:hover {
            }
          }
        }
      }
    }
  }
}

.share-info {
  margin-top: 20px;
  background-color: #ebf5ff;
  border-radius: 8px;
  width: 575px;
  margin-left: 23px;
  .info-box {
    display: flex;
    align-items: flex-start;
    padding: 12px 16px;
    border-radius: 4px;

    .info-icon {
      font-size: 16px;
      margin-right: 8px;
      margin-top: 4px;
      flex-shrink: 0;
    }

    .info-text {
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      letter-spacing: 0%;
      vertical-align: middle;
      color: #554242;

      .expire-date {
        color: #f56c6c;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// Element Plus 组件样式覆盖
:deep(.el-select) {
  .el-input__wrapper {
    border-radius: 4px;
  }
}

:deep(.el-radio) {
  .el-radio__label {
    font-size: 14px;
    color: #606266;
  }

  &.is-checked {
    .el-radio__label {
    }
  }
}

:deep(.el-switch) {
  .el-switch__core {
    border-radius: 10px;
  }
}

:deep(.el-input) {
  .el-input__wrapper {
    border-radius: 4px;

    &:hover {
      box-shadow: 0 0 0 1px #c0c4cc inset;
    }
  }
}

:deep(.el-button) {
  border-radius: 4px;
  font-size: 14px;
}
</style>
