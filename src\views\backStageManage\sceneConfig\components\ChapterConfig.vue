<template>
  <div class="chapter-config">
    <div class="config-content">
      <!-- 基本信息配置 -->
      <div class="config-section">
        <h4 class="section-title">基本信息</h4>
        <el-form :model="formData" :rules="formRules" ref="formRef" label-width="100px">
          <el-form-item label="章节名称" prop="name">
            <el-input v-model="formData.name" placeholder="请输入章节名称" />
          </el-form-item>
          <el-form-item label="章节描述" prop="description">
            <el-input v-model="formData.description" type="textarea" :rows="3" placeholder="请输入章节描述" />
          </el-form-item>
          <el-form-item label="排序" prop="sort">
            <el-input-number v-model="formData.sort" :min="1" :max="999" />
          </el-form-item>
        </el-form>
      </div>

      <!-- 章节封面 -->
      <div class="config-section">
        <h4 class="section-title">章节封面</h4>
        <div class="cover-upload">
          <el-upload class="cover-uploader" :show-file-list="false" :on-success="handleCoverSuccess"
            :before-upload="beforeCoverUpload" action="/api/upload">
            <img v-if="formData.coverImage" :src="formData.coverImage" class="cover-image" />
            <div v-else class="cover-placeholder">
              <el-icon class="cover-icon">
                <Plus />
              </el-icon>
              <div class="cover-text">上传封面</div>
            </div>
          </el-upload>
          <div class="upload-tips">
            <p>建议尺寸：16:9，支持 JPG、PNG 格式，大小不超过 2MB</p>
          </div>
        </div>
      </div>

      <!-- 宫观列表 -->
      <div class="config-section">
        <h4 class="section-title">
          宫观列表
          <el-button type="primary" size="small" @click="addPalace" style="margin-left: 16px">
            <el-icon>
              <Plus />
            </el-icon>
            添加宫观
          </el-button>
        </h4>
        <div class="palace-list">
          <div v-for="palace in palaceList" :key="palace.id" class="palace-item" @click="selectPalace(palace)"
            :class="{ active: selectedPalaceId === palace.id }">
            <div class="palace-info">
              <div class="palace-name">{{ palace.name }}</div>
              <div class="palace-meta">
                <span class="node-count">{{ palace.children?.length || 0 }} 个节点</span>
                <el-tag v-if="palace.videoUrl" size="small" type="success" class="video-tag">
                  已配置视频
                </el-tag>
              </div>
            </div>
            <div class="palace-actions">
              <el-button type="primary" link size="small" @click.stop="editPalace(palace)">编辑</el-button>
              <el-button type="danger" link size="small" @click.stop="deletePalace(palace)">删除</el-button>
            </div>
          </div>
          <el-empty v-if="palaceList.length === 0" description="暂无宫观" :image-size="80" />
        </div>
      </div>

      <!-- 章节统计 -->
      <div class="config-section">
        <h4 class="section-title">章节统计</h4>
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon palace-icon">
              <el-icon>
                <OfficeBuilding />
              </el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ palaceList.length }}</div>
              <div class="stat-label">宫观数量</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon node-icon">
              <el-icon>
                <Location />
              </el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ totalNodes }}</div>
              <div class="stat-label">信息节点</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon video-icon">
              <el-icon>
                <VideoPlay />
              </el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ videoCount }}</div>
              <div class="stat-label">配置视频</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="config-actions">
        <el-button @click="resetForm">重置</el-button>
        <el-button type="primary" @click="saveConfig" :loading="saving">保存配置</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { Plus, OfficeBuilding, Location, VideoPlay } from '@element-plus/icons-vue';

interface Props {
  chapterData: any;
}

interface Emits {
  (e: 'update', data: any): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { success, error } = useMessage();
const { confirm } = useMessageBox();

// 响应式数据
const formRef = ref();
const saving = ref(false);
const selectedPalaceId = ref('');

const formData = reactive({
  name: '',
  description: '',
  sort: 1,
  coverImage: '',
});

const palaceList = ref<any[]>([]);

const formRules = {
  name: [
    { required: true, message: '请输入章节名称', trigger: 'blur' },
  ],
  sort: [
    { required: true, message: '请输入排序', trigger: 'blur' },
  ],
};

// 计算属性
const totalNodes = computed(() => {
  return palaceList.value.reduce((total, palace) => {
    return total + (palace.children?.length || 0);
  }, 0);
});

const videoCount = computed(() => {
  return palaceList.value.filter(palace => palace.videoUrl).length;
});

// 方法
const handleCoverSuccess = (response: any) => {
  if (response.code === 200) {
    formData.coverImage = response.data.url;
    success('封面上传成功');
  } else {
    error('封面上传失败');
  }
};

const beforeCoverUpload = (file: File) => {
  const isJPGOrPNG = file.type === 'image/jpeg' || file.type === 'image/png';
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isJPGOrPNG) {
    error('封面图片只能是 JPG 或 PNG 格式!');
    return false;
  }
  if (!isLt2M) {
    error('封面图片大小不能超过 2MB!');
    return false;
  }
  return true;
};

const addPalace = () => {
  emit('update', { ...props.chapterData, action: 'addPalace' });
};

const selectPalace = (palace: any) => {
  selectedPalaceId.value = palace.id;
};

const editPalace = (palace: any) => {
  // TODO: 打开编辑宫观弹窗
};

const deletePalace = async (palace: any) => {
  try {
    await confirm(`确认删除宫观"${palace.name}"吗？删除后不可恢复！`);
    // TODO: 调用删除API
    success('删除成功');
    loadChapterData();
  } catch {
    // 用户取消删除
  }
};

const resetForm = () => {
  formRef.value?.resetFields();
  loadChapterData();
};

const saveConfig = async () => {
  try {
    await formRef.value?.validate();

    saving.value = true;

    const updateData = {
      ...props.chapterData,
      ...formData,
      children: palaceList.value,
    };

    // TODO: 调用API保存配置
    await new Promise(resolve => setTimeout(resolve, 1000));

    emit('update', updateData);
    success('保存成功');
  } catch (err) {
    if (err !== false) {
      error('保存失败');
    }
  } finally {
    saving.value = false;
  }
};

const loadChapterData = () => {
  if (props.chapterData) {
    Object.assign(formData, {
      name: props.chapterData.name || '',
      description: props.chapterData.description || '',
      sort: props.chapterData.sort || 1,
      coverImage: props.chapterData.coverImage || '',
    });

    palaceList.value = props.chapterData.children || [];
  }
};

// 监听数据变化
watch(() => props.chapterData, loadChapterData, { immediate: true });

onMounted(() => {
  loadChapterData();
});
</script>

<style scoped lang="scss">
.chapter-config {
  height: 100%;
  overflow: auto;
}

.config-content {
  padding: 20px;
}

.config-section {
  margin-bottom: 32px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 2px solid #409eff;
    display: inline-block;
  }
}

.cover-upload {
  .cover-uploader {
    :deep(.el-upload) {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: all 0.3s;
      width: 300px;
      height: 169px;

      &:hover {
        border-color: #409eff;
      }
    }
  }

  .cover-image {
    width: 300px;
    height: 169px;
    object-fit: cover;
    display: block;
  }

  .cover-placeholder {
    width: 300px;
    height: 169px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #8c939d;

    .cover-icon {
      font-size: 32px;
      margin-bottom: 8px;
    }

    .cover-text {
      font-size: 14px;
    }
  }

  .upload-tips {
    margin-top: 8px;

    p {
      margin: 0;
      color: #909399;
      font-size: 12px;
      line-height: 1.4;
    }
  }
}

.palace-list {
  max-height: 400px;
  overflow-y: auto;

  .palace-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    margin-bottom: 12px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      border-color: #409eff;
      background: #f0f9ff;
    }

    &.active {
      border-color: #409eff;
      background: #ecf5ff;
    }

    .palace-info {
      flex: 1;

      .palace-name {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
        margin-bottom: 8px;
      }

      .palace-meta {
        display: flex;
        align-items: center;
        gap: 12px;

        .node-count {
          font-size: 12px;
          color: #909399;
        }

        .video-tag {
          margin: 0;
        }
      }
    }

    .palace-actions {
      display: flex;
      gap: 8px;
      opacity: 0;
      transition: opacity 0.3s;

      .el-button {
        padding: 4px 8px;
        font-size: 12px;
      }
    }

    &:hover .palace-actions {
      opacity: 1;
    }
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;

  .stat-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 16px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    }

    .stat-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: white;

      &.palace-icon {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }

      &.node-icon {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }

      &.video-icon {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      }
    }

    .stat-content {
      .stat-number {
        font-size: 24px;
        font-weight: 700;
        color: #303133;
        line-height: 1;
        margin-bottom: 4px;
      }

      .stat-label {
        font-size: 14px;
        color: #606266;
      }
    }
  }
}

.config-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}
</style>
