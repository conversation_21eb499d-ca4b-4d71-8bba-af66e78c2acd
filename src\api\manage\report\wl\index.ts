import request from '/@/utils/request'

export function getOverviewPass(query?: Object) {
	return request({
		url: '/report/wlPassengerFlowTotal/overviewPassengerFlow',
		method: 'get',
        params: query
	})
}

export function getFlowPass(query?: Object) {
	return request({
		url: '/report/wlPassengerFlowTotal/passengerFlowAnalysisByPage',
		method: 'get',
        params: query
	})
}

export function getTimeTrend(query?: Object) {
	return request({
		url: '/report/wlPassengerFlowTotal/timeSharingTrend',
		method: 'get',
        params: query
	})
}

export function getPeopleTrend(query?: Object) {
	return request({
		url: '/report/wlPassengerFlowTotal/populationTrend',
		method: 'get',
        params: query
	})
}

export function getParkingSummary(query?: Object) {
	return request({
		url: '/report/wlEtcpEntrance/parkingSummaryAnalysis',
		method: 'get',
        params: query
	})
}

export function getRegionalScope() {
	return request({
		url: '/report/wlEtcpEntrance/regionalScope',
		method: 'get'
	})
}

export function getMinuteHourTrend(query?: Object) {
	return request({
		url: '/report/wlEtcpEntrance/trafficTimeSharingTrend',
		method: 'get',
        params: query
	})
}

export function getDalityTrend(query?: Object) {
	return request({
		url: '/report/wlEtcpEntrance/dailyTrend',
		method: 'get',
        params: query
	})
}

export function getMonthTrend(query?: Object) {
	return request({
		url: '/report/wlEtcpEntrance/monthlyTrend',
		method: 'get',
        params: query
	})
}

export function getYearTrend(query?: Object) {
	return request({
		url: '/report/wlEtcpEntrance/yearTrend',
		method: 'get',
        params: query
	})
}

export function getPeopleCluster(query?: Object) {
	return request({
		url: '/report/wlPeopleAccumulation/personnelAggregationAnalysis',
		method: 'get',
		params: query
	})
}

export function getPeopleClusterDensity(query?: Object) {
	return request({
		url: '/report/wlPeopleAccumulation/personnelGatheringDensityTrend',
		method: 'get',
		params: query
	})
}

export function getPeopleClusterTime(query?: Object) {
	return request({
		url: '/report/wlPeopleAccumulation/personnelGatheringPeriodTrend',
		method: 'get',
		params: query
	})
}