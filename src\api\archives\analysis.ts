import request from "/@/utils/request"

// 新建会话
export function aiCreateSession(data?: Object, query?: Object) {
  return request({
    url: '/archive/ai/analysis/createSession',
    method: 'post',
    data,
    params: query,
  })
}

// 提问
export function aiSendQuestion(query?: Object) {
  return request({
    url: '/archive/ai/analysis/sendQuestion',
    method: 'get',
    params: query,
  })
}

// 查询我的会话列表
export function aiMySessionList(query?: Object) {
  return request({
    url: '/archive/ai/analysis/mySessionList',
    method: 'get',
    params: query,
  })
}

// 查询我的会话详情列表
export function aiMySessionItemList(query?: Object) {
  return request({
    url: '/archive/ai/analysis/mySessionItemList',
    method: 'get',
    params: query,
  })
}

// 删除会话
export function delSession(query?: Object) {
  return request({
    url: '/archive/ai/analysis/delSession',
    method: 'get',
    params: query,
  })
}