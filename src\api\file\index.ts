import request from '/@/utils/request';
import { AxiosPromise } from 'axios';
import { FileInfo, AddFileFolder, EditFileFolder, FolderData, FolderFileData, FormDataType, CommentMaterialType, CommentSceneType, ResourcesForm } from './types';

/**
 * 上传文件
 *
 * @param file
 */
// export function uploadFileApi(file: File): AxiosPromise<FileInfo> {
//   const formData = new FormData();
//   formData.append('file', file);
//   return request({
//     url: '/materialData/getUrl',
//     method: 'post',
//     data: formData,
//     headers: {
//       'Content-Type': 'multipart/form-data'
//     }
//   });
// }
export function uploadFileApi(file: any, config: any) {
  const formData = new FormData();
  formData.append('file', file);
  return request({
    url: '/learning/material/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data;charset=UTF-8'
    },
    // onUploadProgress:config.onUploadProgress
  });
}

export function uploadFileApiFn(file: any, config: any) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('fileFlag', 'true');
  return request({
    url: '/learning/material/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data;charset=UTF-8'
    },
    // onUploadProgress:config.onUploadProgress
  });
}



/**
 * 上传文件夹获取桶路径
 *
 * @param folderData 文件完整路径
 */
export function uploadFolderApi(folderData: FolderData): AxiosPromise<FolderData> {
  return request({
    url: '/learning/material/addLocal',
    method: 'post',
    data: folderData
  });
}

/**
 * 上传文件夹每个文件接口
 *
 * @param folderFileData 文件完整路径
 */
export function uploadFolderFileApi(folderFileData: FolderFileData): AxiosPromise<FolderFileData> {
  return request({
    url: '/learning/material/upload',
    method: 'post',
    data: folderFileData,
    headers: {
      'Content-Type': 'multipart/form-data;charset=UTF-8'
    },
  });
}


/**
 * 上传表单提交
 *
 * @param formData 文件完整路径
 */
export function submitFormApi(formData: FormDataType): AxiosPromise<FormDataType> {
  return request({
    url: '/learning/material',
    method: 'post',
    data: formData
  });
}

/**
 * 修改信息提交
 *
 * @param formData 文件完整路径
 */
export function editFormApi(formData: FormDataType): AxiosPromise<FormDataType> {
  return request({
    url: '/learning/material',
    method: 'put',
    data: formData
  });
}

/**
 * 删除文件夹
 *
 * @param id 文件夹的ID
 */
export function deleteFileApi(id?: string) {
  return request({
    url: '/learning/folder/' + id,
    method: 'delete'
  });
}

/**
 * 获取所有的素材和多级文件夹数据
 *
 */
export function getFileFolderData(folderType: string) {
  return request({
    url: '/learning/folder/folderTree',
    method: 'get',
    params: { 'type': folderType }
  });
}

/**
 * 获取当前文件夹下的素材
 *@param resourcesForm
 */
export function getMaterialData(resourcesForm: ResourcesForm) {
  return request({
    url: '/learning/material/list',
    method: 'get',
    params: resourcesForm
  });
}

/**
 * 获取本人全部的私有素材数据
 *
 */
export function getOneselfMaterialData(ResourcesForm: any) {
  return request({
    url: `/learning/material/oneself/list`,
    method: 'get',
    params: ResourcesForm
  });
}

/**
 * 删除素材
 *
 * @param id 素材ID
 */
export function deleteMaterialApi(id?: string) {
  return request({
    url: '/learning/material/' + id,
    method: 'delete'
  });
}

/**
 * 获取是否拥有下载权限
 *
 * @param id 素材ID
 */
export function getDownloadPermissionApi(id?: string) {
  return request({
    url: '/learning/material/downloadPermission/' + id,
    method: 'get'
  });
}

/**
 * 下载素材
 *
 * @param id 素材ID
 */
export function downloadMaterialApi(id?: string) {
  return request({
    url: '/learning/material/downloadZip/' + id,
    method: 'get'
  });
}

/**
 * 获取素材详情
 *
 * @param id 素材ID
 */
export function getMaterialDetailApi(id?: string) {
  return request({
    url: '/learning/material/' + id,
    method: 'get'
  });
}
/**
 * 素材点赞
 *
 */
export function changeMaterialPraise(id: string, num: number) {
  return request({
    url: '/learning/praise/material/' + id + '/' + num,
    method: 'post',
  });
}

/**
 * 素材发表评论
 *@param commentMaterialType
 */
export function sendmaterialCommentApi(commentMaterialType: CommentMaterialType): AxiosPromise<CommentMaterialType> {
  return request({
    url: '/learning/comment',
    method: 'post',
    data: commentMaterialType
  });
}

/**
 * 获取当前资源详情中的评论列表
 *
 */
export function getMaterialCommentListApi(materialId: string) {
  return request({
    url: '/learning/comment/list',
    method: 'get',
    params: { 'materialId': materialId }
  });
}


/**
 * 获取当前文件夹下的场景
 *@param sceneForm
 */
export function getSceneData(sceneForm: any) {
  return request({
    url: '/learning/scene/list',
    method: 'get',
    params: sceneForm
  });
}

/**
 * 获取本人全部的私有场景数据
 *@param sceneForm
 */
export function getOneselfSceneData(sceneForm: any) {
  return request({
    url: '/learning/scene/oneself/list',
    method: 'get',
    params: sceneForm
  });
}

/**
 * 删除场景
 *
 * @param id 素材ID
 */
export function deleteSceneApi(id?: string) {
  return request({
    url: '/learning/scene/' + id,
    method: 'delete'
  });
}

/**
 * 新建文件夹
 *
 * @param FileFolder
 */
export function addFileFolder(FileFolder: AddFileFolder): AxiosPromise<AddFileFolder> {
  return request({
    url: '/learning/folder',
    method: 'post',
    data: FileFolder
    // params: JSON.stringify({folderName:data.folderName,pid:data.pid})
  });
}

/**
 * 编辑文件夹名称
 *
 * @param editFileFolder
 */
export function editFileFolder(editFileFolder: EditFileFolder): AxiosPromise<EditFileFolder> {
  return request({
    url: '/learning/folder',
    method: 'put',
    data: editFileFolder
    // params: JSON.stringify({folderName:data.folderName,pid:data.pid})
  });
}

/**
 * 获取场景详情
 *
 * @param id 场景ID
 */
export function getSceneDetailApi(id?: string) {
  return request({
    url: '/learning/scene/' + id,
    method: 'get'
  });
}

/**
 * 场景点赞
 *
 */
export function changeScenePraise(id: string, num: number) {
  return request({
    url: '/learning/praise/scene/' + id + '/' + num,
    method: 'post',
  });
}

/**
 * 场景发表评论
 *@param commentSceneType
 */
export function sendSceneCommentApi(commentSceneType: CommentSceneType): AxiosPromise<CommentSceneType> {
  return request({
    url: '/learning/comment/scene',
    method: 'post',
    data: commentSceneType
  });
}

/**
 * 获取当前场景详情中的评论列表
 *
 */
export function getSceneCommentListApi(sceneId: string) {
  return request({
    url: '/learning/comment/scene/list',
    method: 'get',
    params: { 'sceneId': sceneId }
  });
}


/**
 * 获取关键词树形列表
 *
 */
export function getKeywordsListApi() {
  return request({
    url: '/learning/dict/tree',
    method: 'get',
  });
}


/**
 * 素材移动文件接口
 *
 */
export function moveToFolderMaterialApi(param) {
  return request({
    url: '/learning/material/editFolder',
    method: 'put',
    data: param
  });
}

/**
 * 素材移动文件接口
 *
 */
export function moveToFolderSceneApi(param) {
  return request({
    url: '/learning/scene/editFolder',
    method: 'put',
    data: param
  });
}


/**
 * 获取视频文件分享状态信息
 *
 */
export function getShareInfo(fileId: any) {
  return request({
    url: `/datacenter/material/share/info?id=${fileId}`,
    method: 'get'
  });
}

/**
 * 启用视频文件分享
 *
 */
export function enableShare(param: any) {
  return request({
    url: '/datacenter/material/certificate',
    method: 'post',
    data: param
  });
}

/**
 * 禁用视频文件分享
 *
 */
export function disableShare(param: any) {
  return request({
    url: '/datacenter/material/share/disabled',
    method: 'post',
    data: param
  });
}

/**
 * 验证视频分享用户、验证码
 *
 */
export function validateVideoShare(id: any, param: any) {
  return request({
    url: `/datacenter/material/video/${id}`,
    method: 'post',
    data: param
  });
}

/**
 * 获取视频流
 *
 */
export function getVideoStream(id: any, param: any) {
  return request({
    url: `/datacenter/material/video/${id}?username=${param.username}&password=${param.password}`,
    method: 'get'
  });
}

/**
 * 中断视频流
 *
 */
export function cancelVideoStream(param: any) {
  return request({
    url: `/datacenter/material/video/cancel`,
    method: 'post',
    data: param
  });
}

/**
 * 获取视频文件分享状态
 *
 */
export function getShareState(id: any) {
  return request({
    url: `/datacenter/material/video/status?id=${id}`,
    method: 'get'
  });
}