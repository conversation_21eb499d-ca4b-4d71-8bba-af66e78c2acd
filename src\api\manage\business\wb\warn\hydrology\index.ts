import request from '/@/utils/request'

export function fetchList(query?: Object) {
	return request({
		url: '/business/wb/hydrologyResult/byPage',
		method: 'get',
		params: query,
	})
}

export function addObj(obj?: Object) {
	return request({
		url: '/business/wb/hydrologyResult',
		method: 'post',
		data: obj,
	})
}

export function getObj(id?: string) {
	return request({
		url: '/business/wb/hydrologyResult/' + id,
		method: 'get',
	})
}

export function delObjs(ids?: Object) {
	return request({
		url: '/business/wb/hydrologyResult',
		method: 'delete',
		data: ids,
	})
}

export function putObj(obj?: Object) {
	return request({
		url: '/business/wb/hydrologyResult',
		method: 'put',
		data: obj,
	})
}
