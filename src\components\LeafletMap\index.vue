<template>
  <div :id="props.mapContainerId" class="cesiumContainer"></div>
  <!-- <el-button @click="fullScreenState"></el-button> -->
</template>

<script setup lang="ts">
  import L from "leaflet";
  import "leaflet/dist/leaflet.css";
  // import fullscreen from "leaflet-fullscreen";
  import { onMounted, watch } from "vue";
  import marker_icon from "/@/assets/img/custom/marker-icon.png";

  const emit = defineEmits(["mapClick"]);
  const map = ref();
  const marker = ref();
  const fullScreenState = ref();

  const props = defineProps({
    point: {
      type: Object as any,
      default: null,
    },
    isEditor: {
      type: Boolean,
      default: true,
    },
    mapContainerId: {
      type: String,
      default: "cesiumContainer",
    },
  });

  watch(
    () => props.point,
    (val: any) => {
      if (val?.x > 0 && map.value && marker.value) {
        marker.value.setLatLng({ lng: val.x, lat: val.y });
        map.value.setView([val.y, val.x]);
      }
    },
    {
      immediate: true,
      deep: true,
    }
  );
  onMounted(async () => {
    let response = await fetch("./config/config.json");
    let data = await response.json();
    // 初始化地图
    map.value = L.map(props.mapContainerId, {
      fullscreenControl: true,
      fullscreenControlOptions: {
        position: "topleft",
      },
      center: [data.map3d.center.y, data.map3d.center.x],
      zoom: 12,
      minZoom: 0,
      maxZoom: 17,
    });
    let myIcon = L.icon({
      iconUrl: marker_icon,
      iconSize: [25, 41],
      iconAnchor: [11, 41],
      popupAnchor: [0, -30],
      // shadowUrl: "my‐icon‐shadow.png",
      // shadowSize: [68, 95],
      // shadowAnchor: [22, 94],
    });
    // 添加图层
    const mapType = "vec"; // cva_w
    L.tileLayer("https://t{s}.tianditu.gov.cn/" + mapType + "_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=" + mapType + "&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILECOL={x}&TILEROW={y}&TILEMATRIX={z}&tk=b72aa81ac2b3cae941d1eb213499e15e", {
      subdomains: ["0", "1", "2", "3", "4", "5", "6", "7"],
      attribution: '&copy; <a href="http://lbs.tianditu.gov.cn/home.html">天地图 GS(2022)3124号 - 甲测资字1100471</a>',
    }).addTo(map.value);
    const mapLabelType = "cva";
    L.tileLayer("https://t{s}.tianditu.gov.cn/" + mapLabelType + "_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=" + mapLabelType + "&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILECOL={x}&TILEROW={y}&TILEMATRIX={z}&tk=b72aa81ac2b3cae941d1eb213499e15e", {
      subdomains: ["0", "1", "2", "3", "4", "5", "6", "7"],
    }).addTo(map.value);
    // // 添加全屏控制器
    // L.control
    //   .fullscreen({
    //     title: "全屏",
    //     content: '<i class="fas fa-expand"></i>',
    //     forceSeparateButton: true,
    //     forcePseudoFullscreen: true,
    //     fullscreenElement: document.getElementById("map-container"),
    //     position: "topright",
    //   })
    //   .addTo(map.value);
    // var popup = L.popup().setLatLng([34.1706, 108.946]).setContent("坐标：").openOn(map.value);
    // var popup = L.popup().setLatLng([34.1706, 108.946])
    //   .bindPopup(`经纬度:108.946,34.1706`, { offset: [0, -7] })
    //   .openPopup(map.value);
    // 添加标注信息
    marker.value = L.marker([0, 0], { icon: myIcon }).addTo(map.value);
    if (props.point) {
      let lng = Number(props.point.x.toFixed(6));
      let lat = Number(props.point.y.toFixed(6));
      marker.value
        .setLatLng({ lng, lat })
        .bindPopup(`坐标：${lng}，${lat}`, { offset: [0, 0], className: "popup-title", autoClose: props.isEditor, closeButton: props.isEditor })
        .openPopup();
      map.value.setView([lat, lng], 12);
    }
    if (props.isEditor) {
      map.value.on("click", onMapClick);
    }
  });

  const onMapClick = (e: any) => {
    // popup
    //   .setLatLng(e.latlng)
    //   .setContent("坐标：" + e.latlng.lng.toFixed(6) + "，" + e.latlng.lat.toFixed(6))
    //   .openOn(map);
    let lng = Number(e.latlng.lng.toFixed(6));
    let lat = Number(e.latlng.lat.toFixed(6));
    marker.value
      .setLatLng(e.latlng)
      .bindPopup(`坐标：${lng}，${lat}`, { offset: [0, 0], className: "popup-title", autoClose: props.isEditor, closeButton: props.isEditor })
      .openPopup();
    emit("mapClick", [lng, lat]);
  };
  onUnmounted(() => {
    map.value.off();
    map.value.remove();
  });
</script>

<style lang="scss" scoped>
  .cesiumContainer {
    position: relative;
    width: 100%;
    height: 100%;
    left: 0px;
    top: 0px;
    margin: 0px;
    padding: 0px;
    ::v-deep(.leaflet-control-attribution) {
      display: none;
    }
    ::v-deep(.popup-title) {
      font-weight: bolder;
      font-size: 14px;
      width: 250px;
      .leaflet-popup-content {
        width: 220px !important;
        margin: 12px 10px;
      }
    }
  }
</style>
