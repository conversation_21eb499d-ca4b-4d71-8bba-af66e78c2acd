<template>
  <div class="department-resources-container">
    <div class="title-box">部门资源</div>
    <div class="resources-grid">
      <div
        class="resource-card"
        v-for="item in deptResourceList"
        :key="item.business_id"
        @click="handleClick(item)"
      >
        <div class="card-image">
          <img :src="item.assets_cover || noCoverImg" alt="" :class="{ 'no-cover': !item.assets_cover }" @error="onImgError" />
        </div>
        <div class="card-info">
          <div class="card-content">
            <div class="card-title">
              <div class="title-left">{{ item.assets_name }}</div>
              <div class="title-right">
                <div class="card-stats">
                  <div class="stat-item">
                    <i class="icon-eye"></i>
                    <span>{{ item.view_count || 0 }}</span>
                  </div>
                  <div class="stat-item">
                    <i class="icon-download"></i>
                    <span>{{ item.downloaded || 0 }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="card-directory">资源目录: {{ item?.third_catalog_name?.split(",").join(' > ') }}</div>
            <div class="card-keywords">
              <div class="keywords-title">关键词: </div>
              <div class="keywords-content">
                {{ item?.assets_tag || '-'}}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { getDeptResource } from "/@/api/squareNew/index";
import noCoverImg from "/@/assets/img/squareNew/no_cover.png";

const deptResourceList = ref<any>([]);
const emit = defineEmits(['goResourceDetail']);

const handleClick = (record: any) => {
  emit('goResourceDetail', record)
};

const queryDeptResource = () => {
  getDeptResource().then((res) => {
    if (res.ok) {
      deptResourceList.value = res.data || [];
    }
  });
};
onMounted(() => {
  queryDeptResource();
});

const onImgError = (e: any) => {
  e.target.src = noCoverImg;
  e.target.classList.add("no-cover");
};
</script>

<style scoped lang="scss">
.department-resources-container {
  width: 100%;
  box-sizing: border-box;
  // background-color: #f7f2f2; // Match the background color of the main-container
  margin-top: 100px;

  .title-box {
    font-family: Source Han Serif CN;
    font-weight: 700;
    font-size: 50px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
    color: #141414;
    margin-bottom: 45px;
  }

  .resources-grid {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 100px;
    .resource-card {
      flex-basis: calc(50% - 10px);
      height: 186px; // Fixed height for each card
      background: #fff;
      display: flex;
      align-items: stretch; // Ensure image and content stretch vertically
      overflow: hidden;
      box-sizing: border-box;
      margin-right: 20px;
      margin-bottom: 20px;
      padding: 18px 24px;
      cursor: pointer;
      border: 1px solid rgba(0, 0, 0, 0.10);
      transition: all 0.3s;
      &:hover{
        box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.15);
      }

      &:nth-last-child(2),
      &:last-child {
        margin-bottom: 0;
      }
      .card-image {
        width: 150px;
        height: 150px;
        margin-right: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        .no-cover {
          width: 110px;
          height: auto;
          object-fit: contain;
        }
      }
      .card-info {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between; // Push content and stats to ends
        position: relative;

        .card-content {
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          p {
            margin: 0; // Reset default margin
            line-height: 1.4;
          }
          .card-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 16px;

            font-family: Source Han Serif CN;
            font-weight: 700;
            font-size: 20px;
            line-height: 24.61px;
            letter-spacing: 0%;
            color: #393939;
            display: flex;

            .title-left {
              flex-grow: 1;
            }
            .title-right {
            }
          }
          .card-directory,
          .card-keywords {
            font-family: Source Han Sans CN;
            font-weight: 400;
            font-size: 16px;
            line-height: 20px;
            letter-spacing: 0px;
            color: #262626b2;
            margin-bottom: 10px;
          }
          .card-keywords {
            display: flex;
            margin-bottom: 0;
            .keywords-title {
              margin-right: 5px;
            }
            .keywords-content {
              color: #862626;
            }
          }
        }

        .card-stats {
          display: flex;
          justify-content: flex-end; // Align stats to the right
          align-items: center; // Center vertically with icon/text
          // padding-top: 10px; // Some spacing from the content above
          .stat-item {
            display: flex;
            align-items: center;
            font-size: 14px;
            color: rgba(134, 38, 38, 1);
            margin-right: 18px;
            &:last-child {
              margin-right: 0;
            }
            span {
              white-space: nowrap; // Prevent wrapping for numbers
            }
            i {
              width: 30px;
              height: 30px;
              margin-right: 4px;
              background-size: contain;
              background-repeat: no-repeat;
              display: flex;
              align-items: center;
              justify-content: center;
              &.icon-eye {
                &::before {
                  content: "";
                  display: inline-block;
                  background-image: url("/@/assets/img/squareNew/eyePrimaryIcon.png");
                  width: 22px;
                  height: 15px;
                  background-size: 100% 100%;
                }
              }
              &.icon-download {
                &::before {
                  content: "";
                  display: inline-block;
                  background-image: url("/@/assets/img/squareNew/downloadIcon.png");
                  width: 24px;
                  height: 18px;
                  background-size: 100% 100%;
                }
              }
            }
          }
        }
      }
      &:nth-of-type(2n) {
        margin-right: 0;
      }
    }
  }
}
</style>
