<template>
    <div class="backstage-layout">
        <!-- 顶部导航栏 -->
        <div class="backstage-header">
            <div class="header-left">
                <div class="logo-area">
                    <img src="/src/assets/logo.png" alt="Logo" class="logo" />
                    <h1 class="system-title">数字展览运维管理系统</h1>
                </div>
            </div>
            <div class="header-right">
                <div class="header-actions">
                    <!-- 设置按钮 -->
                    <el-button type="text" class="header-btn" @click="openSettings">
                        <el-icon class="header-icon">
                            <Setting />
                        </el-icon>
                    </el-button>

                    <!-- 用户信息 -->
                    <el-dropdown class="user-dropdown" @command="handleUserCommand">
                        <div class="user-info">
                            <el-avatar :size="32" :src="userInfo.avatar" class="user-avatar">
                                <el-icon>
                                    <User />
                                </el-icon>
                            </el-avatar>
                            <span class="user-name">{{ userInfo.name }}</span>
                            <el-icon class="dropdown-icon">
                                <ArrowDown />
                            </el-icon>
                        </div>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item command="profile">个人信息</el-dropdown-item>
                                <el-dropdown-item command="settings">系统设置</el-dropdown-item>
                                <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </div>
            </div>
        </div>

        <div class="backstage-content">
            <!-- 左侧导航菜单 -->
            <div class="sidebar">
                <el-menu :default-active="activeMenu" :default-openeds="defaultOpeneds" class="sidebar-menu"
                    @select="handleMenuSelect" :unique-opened="false">
                    <!-- 场景管理 -->
                    <el-menu-item index="sceneManage"
                        style="padding-left: 20px !important; margin: 0px !important; border-radius: 0px !important;">
                        <el-icon>
                            <VideoPlay />
                        </el-icon>
                        <span>场景管理</span>
                    </el-menu-item>

                    <!-- 资源管理 -->
                    <el-sub-menu index="resourceManage">
                        <template #title>
                            <el-icon>
                                <FolderOpened />
                            </el-icon>
                            <span>资源管理</span>
                        </template>
                        <el-menu-item index="resourceManage/sceneResource">
                            <el-icon>
                                <Collection />
                            </el-icon>
                            <span>场景资源</span>
                        </el-menu-item>
                        <el-menu-item index="resourceManage/buildingSpace">
                            <el-icon>
                                <OfficeBuilding />
                            </el-icon>
                            <span>建筑空间</span>
                        </el-menu-item>
                    </el-sub-menu>
                </el-menu>
            </div>

            <!-- 主内容区域 -->
            <div class="main-content">
                <div v-if="showWelcome" class="welcome-content">
                    <div class="welcome-card">
                        <h2 class="welcome-title">欢迎使用数字武当后台管理系统</h2>
                        <p class="welcome-desc">请从左侧菜单选择功能模块开始使用</p>
                        <div class="quick-actions">
                            <el-button type="primary" size="large" @click="handleMenuSelect('sceneManage')">
                                <el-icon>
                                    <VideoPlay />
                                </el-icon>
                                场景管理
                            </el-button>
                            <el-button size="large" @click="handleMenuSelect('resourceManage/sceneResource')">
                                <el-icon>
                                    <Collection />
                                </el-icon>
                                场景资源
                            </el-button>
                            <el-button size="large" @click="handleMenuSelect('resourceManage/buildingSpace')">
                                <el-icon>
                                    <OfficeBuilding />
                                </el-icon>
                                建筑空间
                            </el-button>
                        </div>
                    </div>
                </div>
                <router-view v-else />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, reactive, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useMessage } from '/@/hooks/message';

import {
    VideoPlay,
    FolderOpened,
    OfficeBuilding,
    Collection,
    Setting,
    User,
    ArrowDown
} from '@element-plus/icons-vue';

const router = useRouter();
const route = useRoute();
const { success } = useMessage();

// 用户信息
const userInfo = reactive({
    name: '管理员',
    avatar: '',
    role: 'admin'
});

// 默认展开的菜单
const defaultOpeneds = ['resourceManage'];

// 当前激活的菜单
const activeMenu = computed(() => {
    const path = route.path;
    if (path.includes('sceneManage') || path.includes('sceneConfig')) {
        return 'sceneManage';
    } else if (path.includes('sceneResource')) {
        return 'resourceManage/sceneResource';
    } else if (path.includes('buildingSpace')) {
        return 'resourceManage/buildingSpace';
    }
    return 'sceneManage';
});

// 是否显示欢迎页面
const showWelcome = computed(() => {
    const path = route.path;
    return path === '/backStageManage' || path === '/backStageManage/';
});

// 菜单选择处理
const handleMenuSelect = (index: string) => {
    if (index.includes('/')) {
        router.push(`/backStageManage/${index}`);
    } else {
        router.push(`/backStageManage/${index}`);
    }
};

// 设置按钮点击
const openSettings = () => {
    success('设置功能开发中...');
};

// 用户下拉菜单处理
const handleUserCommand = (command: string) => {
    switch (command) {
        case 'profile':
            success('个人信息功能开发中...');
            break;
        case 'settings':
            success('系统设置功能开发中...');
            break;
        case 'logout':
            router.push('/login');
            break;
    }
};

onMounted(() => {
    // 如果是根路径，不自动跳转，显示欢迎页面
    // 用户可以通过菜单或快捷按钮进行导航
});
</script>

<style scoped lang="scss">
.backstage-layout {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f5f7fa;
}

.backstage-header {
    height: 64px;
    background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border-bottom: 1px solid #e8e8e8;
    z-index: 1000;

    .header-left {
        .logo-area {
            display: flex;
            align-items: center;
            gap: 12px;

            .logo {
                width: 32px;
                height: 32px;
                border-radius: 4px;
            }

            .system-title {
                color: #262626;
                font-size: 18px;
                font-weight: 600;
                margin: 0;
            }
        }
    }

    .header-right {
        .header-actions {
            display: flex;
            align-items: center;
            gap: 16px;

            .header-btn {
                padding: 8px;
                border-radius: 6px;
                transition: all 0.3s;
                border: none;
                background: transparent;

                .header-icon {
                    font-size: 18px;
                    color: #595959;
                }

                &:hover {
                    background-color: #f5f5f5;

                    .header-icon {
                        color: #1890ff;
                    }
                }
            }

            .user-dropdown {
                .user-info {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    padding: 6px 12px;
                    border-radius: 6px;
                    cursor: pointer;
                    transition: all 0.3s;

                    &:hover {
                        background-color: #f5f5f5;
                    }

                    .user-avatar {
                        border: 2px solid #e8e8e8;
                    }

                    .user-name {
                        font-size: 14px;
                        color: #262626;
                        font-weight: 500;
                    }

                    .dropdown-icon {
                        font-size: 12px;
                        color: #8c8c8c;
                        transition: transform 0.3s;
                    }

                    &:hover .dropdown-icon {
                        transform: rotate(180deg);
                    }
                }
            }
        }
    }
}

.backstage-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.sidebar {
    width: 260px;
    background: #ffffff;
    border-right: 1px solid #e8e8e8;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.04);

    .sidebar-menu {
        border-right: none;
        height: 100%;
        background: #ffffff;

        // 一级菜单项
        .el-menu-item {
            height: 48px;
            line-height: 48px;
            font-size: 14px;
            color: #595959;
            padding-left: 24px !important;
            margin: 4px 12px;
            border-radius: 6px;
            transition: all 0.3s;

            &:hover {
                background-color: #f0f9ff;
                color: #1890ff;
            }

            &.is-active {
                background-color: #e6f7ff;
                color: #1890ff;
                font-weight: 500;
            }

            .el-icon {
                margin-right: 12px;
                font-size: 16px;
            }
        }

        // 子菜单
        .el-sub-menu {
            .el-sub-menu__title {
                height: 48px;
                line-height: 48px;
                font-size: 14px;
                color: #595959;
                padding-left: 24px !important;
                margin: 4px 12px;
                border-radius: 6px;
                transition: all 0.3s;

                &:hover {
                    background-color: #f0f9ff;
                    color: #1890ff;
                }

                .el-icon {
                    margin-right: 12px;
                    font-size: 16px;
                }

                .el-sub-menu__icon-arrow {
                    right: 20px;
                    font-size: 12px;
                    color: #8c8c8c;
                }
            }

            &.is-opened {
                .el-sub-menu__title {
                    background-color: #f0f9ff;
                    color: #1890ff;
                    font-weight: 500;
                }
            }

            .el-menu {
                background-color: #fafafa;
                margin: 0 12px 8px 12px;
                border-radius: 6px;

                .el-menu-item {
                    height: 40px;
                    line-height: 40px;
                    font-size: 13px;
                    color: #8c8c8c;
                    padding-left: 48px !important;
                    margin: 0;
                    border-radius: 0;
                    background: transparent;

                    &:first-child {
                        border-radius: 6px 6px 0 0;
                    }

                    &:last-child {
                        border-radius: 0 0 6px 6px;
                    }

                    &:only-child {
                        border-radius: 6px;
                    }

                    &:hover {
                        background-color: #e6f7ff;
                        color: #1890ff;
                    }

                    &.is-active {
                        background-color: #1890ff;
                        color: #ffffff;
                        font-weight: 500;
                    }

                    .el-icon {
                        margin-right: 8px;
                        font-size: 14px;
                    }
                }
            }
        }
    }
}

.main-content {
    flex: 1;
    padding: 15px;
    overflow: auto;
    background-color: #f5f5f5;

    .welcome-content {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;

        .welcome-card {
            background: #ffffff;
            border-radius: 12px;
            padding: 48px;
            text-align: center;
            box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);
            max-width: 600px;
            width: 100%;

            .welcome-title {
                font-size: 28px;
                color: #262626;
                font-weight: 600;
                margin: 0 0 16px 0;
            }

            .welcome-desc {
                font-size: 16px;
                color: #8c8c8c;
                margin: 0 0 40px 0;
                line-height: 1.6;
            }

            .quick-actions {
                display: flex;
                justify-content: center;
                gap: 16px;
                flex-wrap: wrap;

                .el-button {
                    height: 48px;
                    padding: 0 24px;
                    border-radius: 8px;
                    font-size: 14px;
                    font-weight: 500;
                    min-width: 140px;

                    .el-icon {
                        margin-right: 8px;
                        font-size: 16px;
                    }

                    &.el-button--primary {
                        background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
                        border: none;
                        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);

                        &:hover {
                            transform: translateY(-2px);
                            box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
                        }
                    }

                    &:not(.el-button--primary) {
                        border: 1px solid #d9d9d9;
                        color: #595959;

                        &:hover {
                            border-color: #1890ff;
                            color: #1890ff;
                            transform: translateY(-2px);
                            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                        }
                    }
                }
            }
        }
    }
}
</style>