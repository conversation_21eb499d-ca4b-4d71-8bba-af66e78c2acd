<template>
	<div class="upload-box">
		<el-upload
			:action="baseURL + other.adaptationUrl(props.uploadFileUrl)"
			:id="uuid"
			ref="imgUpload"
			:class="['upload', self_disabled ? 'disabled' : '', drag ? 'no-border' : '']"
			multiple
			:limit="limit"
			:headers="headers"
			:file-list="fileList"
			:disabled="self_disabled"
			:show-file-list="true"
			:before-upload="beforeUpload"
			:on-success="handleUploadSuccess"
			:on-error="uploadError"
			:on-remove="handleRemove"
			:drag="drag"
			:accept="fileType.join(',')"
			:on-preview="handlePreview"
		>
			<div class="upload-empty">
				<slot name="empty">
					<el-icon><Plus /></el-icon>
					<span>上传视频</span>
				</slot>
			</div>
			<template #tip>
				<div style="color: red">只能上传{{ fileSize }}M大小，格式为{{ fileTypeTip }}的文件</div>
			</template>
		</el-upload>
		<!-- <template v-if="imgViewVisible">
			<el-image-viewer :teleported="true" @close="imgViewVisible = false" :initial-index="previewIndex" :url-list="previewFileList" />
		</template> -->
	</div>
</template>

<script setup lang="ts" name="UploadImg">
import { ref, computed, inject } from 'vue'
import { ElNotification, formContextKey } from 'element-plus'
import type { UploadProps, UploadFile } from 'element-plus'
import { generateUUID } from '/@/utils/other'
import other from '/@/utils/other'
import { Session } from '/@/utils/storage'

interface previewFile {
	url: string
	name: string
}

interface UploadFileProps {
	imageUrl?: Array<string> // 图片地址 ==> 必传
	uploadFileUrl?: string // 上传图片的 api 方法，一般项目上传都是同一个 api 方法，在组件里直接引入即可 ==> 非必传
	drag?: boolean // 是否支持拖拽上传 ==> 非必传（默认为 true）
	disabled?: boolean // 是否禁用上传组件 ==> 非必传（默认为 false）
	fileSize?: number // 图片大小限制 ==> 非必传（默认为 5M）
	fileType?: File.ImageMimeType[] // 图片类型限制 ==> 非必传（默认为 ["image/jpeg", "image/png", "image/gif"]）
	height?: string // 组件高度 ==> 非必传（默认为 150px）
	width?: string // 组件宽度 ==> 非必传（默认为 150px）
	borderRadius?: string // 组件边框圆角 ==> 非必传（默认为 8px）
	iconSize?: number
	dir?: string // 文件目录
	limit?: number
}

// 接受父组件参数
const props = withDefaults(defineProps<UploadFileProps>(), {
	imageUrl: () => [],
	uploadFileUrl: '/admin/sys-file/upload',
	drag: false,
	disabled: false,
	fileSize: 500, // 大小限制(MB)
	fileType: () => ['video/mp4'],
	height: '150px',
	width: '150px',
	borderRadius: '8px',
	dir: '',
	limit: 5,
})

const { proxy } = getCurrentInstance()

// 生成组件唯一id
const uuid = ref('id-' + generateUUID())
const number = ref(0)
const fileList = ref([]) as any
const uploadList = ref([]) as any
const imgUpload = ref()

// 查看图片
const imgViewVisible = ref(false)
// 初始预览图像索引
const previewIndex = ref(0)
// 获取 el-form 组件上下文
const formContext = inject(formContextKey, void 0)
// 判断是否禁用上传和删除
const self_disabled = computed(() => {
	return props.disabled || formContext?.disabled
})

// 用于预览的图片链接列表
const previewFileList = computed(() => {
	return fileList.value.map((item: any) => {
		if (item.url.includes('http') || item.url.includes('https')) {
			return item.url
		} else {
			return proxy.baseURL + item.url
		}
	})
})

// 请求头处理
const headers = computed(() => {
	return {
		Authorization: 'Bearer ' + Session.get('token'),
		'TENANT-ID': Session.getTenant(),
	}
})

const fileTypeTip = computed(() => {
	return props.fileType
		.map((type) => {
			const parts = type.split('/')
			return parts[1]
		})
		.join(',')
})

watch(
	() => props.imageUrl,
	(val) => {
		if (val && val.length) {
			let temp = 1
			// 首先将值转为数组
			const list = Array.isArray(val) ? val : props?.imageUrl?.split(',')
			// 然后将数组转为对象数组
			fileList.value = list.map((item: any) => {
				if (typeof item === 'string') {
					item = { name: item, url: item }
				}
				item.uid = item.uid || new Date().getTime() + temp++
				return item
			})
		} else {
			fileList.value = []
			return []
		}
	},
	{ deep: true, immediate: true }
)

/**
 * @description 图片上传
 * @param options upload 所有配置项
 * */
interface UploadEmits {
	(e: 'update:imageUrl', value: Array<string>): void
}
const emit = defineEmits<UploadEmits>()

/**
 * @description 点击文件列表中已上传的文件时的钩子
 * */
const handlePreview = (uploadFile: UploadFile) => {
	const currentIndex = fileList.value.findIndex((item) => item.url === uploadFile.url) || 0

	previewIndex.value = currentIndex
	imgViewVisible.value = true
}

/**
 * @description 文件上传之前判断
 * @param rawFile 选择的文件
 * */
const beforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
	const imgSize = rawFile.size / 1024 / 1024 < props.fileSize
	const imgType = props.fileType.includes(rawFile.type as File.ImageMimeType)
	if (!imgType) {
		ElNotification({
			title: '温馨提示',
			message: '上传视频不符合所需的格式！',
			type: 'warning',
		})
		return false
	}

	if (!imgSize) {
		setTimeout(() => {
			ElNotification({
				title: '温馨提示',
				message: `上传视频大小不能超过 ${props.fileSize}M！`,
				type: 'warning',
			})
		}, 0)

		return false
	}

	number.value++
	return true
}

// 上传成功回调
function handleUploadSuccess(res: any, file: any) {
	if (res.code === 0) {
		uploadList.value.push({ name: file.name, url: res.data.url })
		uploadedSuccessfully()
	} else {
		number.value--
		ElNotification({
			title: '温馨提示',
			message: res.msg,
			type: 'error',
		})
		imgUpload.value.handleRemove(file)
		uploadedSuccessfully()
	}
}

// 上传结束处理
const uploadedSuccessfully = () => {
	if (number.value > 0 && uploadList.value.length === number.value) {
		fileList.value = fileList.value.filter((f: previewFile) => f.url !== undefined).concat(uploadList.value)
		uploadList.value = []
		number.value = 0
		// emit('update:imageUrl', listToString(fileList.value))
		emit('update:imageUrl', fileList.value)
	}
}

/**
 * @description 文件列表移除文件时的钩子
 * */
const handleRemove = (file: any) => {
	fileList.value = fileList.value.filter((f: previewFile) => !(f === file.url))
	// emit('update:imageUrl', listToString(fileList.value))
	emit('update:imageUrl', fileList.value)
}

/**
 * @description 图片上传错误
 * */
const uploadError = () => {
	ElNotification({
		title: '温馨提示',
		message: '图片上传失败，请您重新上传！',
		type: 'error',
	})
}

/**
 * 将对象数组转为字符串，以逗号分隔。
 * @param list 待转换的对象数组。
 * @param separator 分隔符，默认为逗号。
 * @returns {string} 返回转换后的字符串。
 */
const listToString = (list: { url: string }[], separator = ','): string => {
	let strs = ''
	separator = separator || ','
	for (let i in list) {
		if (list[i].url) {
			strs += list[i].url + separator
		}
	}
	return strs !== '' ? strs.substring(0, strs.length - 1) : ''
}
</script>
<style scoped lang="scss">
.is-error {
	.upload {
		:deep(.el-upload),
		:deep(.el-upload-dragger) {
			border: 1px dashed var(--el-color-danger) !important;
			&:hover {
				border-color: var(--el-color-primary) !important;
			}
		}
	}
}
:deep(.disabled) {
	.el-upload,
	.el-upload-dragger {
		cursor: not-allowed !important;
		background: var(--el-disabled-bg-color);
		border: 1px dashed var(--el-border-color-darker) !important;
		&:hover {
			border: 1px dashed var(--el-border-color-darker) !important;
		}
	}
}
.upload-box {
	.no-border {
		:deep(.el-upload) {
			border: none !important;
		}
	}
	:deep(.upload) {
		.el-upload {
			position: relative;
			display: flex;
			align-items: center;
			justify-content: center;
			width: v-bind(width);
			height: v-bind(height);
			overflow: hidden;
			border: 1px dashed var(--el-border-color-darker);
			border-radius: v-bind(borderRadius);
			transition: var(--el-transition-duration-fast);
			&:hover {
				border-color: var(--el-color-primary);
				.upload-handle {
					opacity: 1;
				}
			}
			.el-upload-dragger {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 100%;
				height: 100%;
				padding: 0;
				overflow: hidden;
				background-color: transparent;
				border: 1px dashed var(--el-border-color-darker);
				border-radius: v-bind(borderRadius);
				&:hover {
					border: 1px dashed var(--el-color-primary);
				}
			}
			.el-upload-dragger.is-dragover {
				background-color: var(--el-color-primary-light-9);
				border: 2px dashed var(--el-color-primary) !important;
			}
			.upload-image {
				width: 100%;
				height: 100%;
				object-fit: contain;
			}
			.upload-empty {
				position: relative;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				font-size: 12px;
				line-height: 30px;
				color: var(--el-color-info);
				.el-icon {
					font-size: 28px;
					color: var(--el-text-color-secondary);
				}
			}
			.upload-handle {
				position: absolute;
				top: 0;
				right: 0;
				box-sizing: border-box;
				display: flex;
				align-items: center;
				justify-content: center;
				width: 100%;
				height: 100%;
				cursor: pointer;
				background: rgb(0 0 0 / 60%);
				opacity: 0;
				transition: var(--el-transition-duration-fast);
				.handle-icon {
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					padding: 0 6%;
					color: aliceblue;
					.el-icon {
						margin-bottom: 40%;
						font-size: 130%;
						line-height: 130%;
					}
					span {
						font-size: 85%;
						line-height: 85%;
					}
				}
			}
		}
	}
	.el-upload__tip {
		line-height: 18px;
		text-align: center;
	}
}
</style>
