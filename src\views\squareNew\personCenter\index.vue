<template>
  <div class="person-center-container">
    <div class="person-center-outer">
      <div class="person-center">
        <!-- 顶部用户信息区域 -->
        <header class="person-center__header">
          <div class="user-info">
            <img v-if="userInfo?.avatar" :src="other.adaptationUrl(userInfo.avatar)" alt="用户头像" class="user-info__avatar" />
            <img v-else src="/@/assets/img/resourceSetupCenter/systemManagement/avatIcon.png" alt="用户头像" class="user-info__avatar" />
            <div class="user-info__details">
              <span class="user-info__name">
                <span class="name-box">{{userInfo.name}}</span>
                <span class="phone-box ml-6">{{userInfo.phone}}</span>
              </span>
              <span class="rank-info-box">
                <span class="user-info__role">{{(userInfo?.postList?.[0]?.postName || '-') + ' | ' + (userInfo?.deptName || '-')}}</span>
                <span class="user-info__login-time">更新时间: {{userInfo.updateTime}}</span>
              </span>
            </div>
          </div>
          <div class="date-info">
            <span class="date-info__day">{{ currentDate.day }}</span>
            <div class="date-info__details">
              <span class="date-info__weekday">{{ currentDate.weekday }}</span>
              <span class="date-info__year-month">{{ currentDate.yearMonth }}</span>
            </div>
          </div>
        </header>

        <!-- 主内容区域 -->
        <main class="person-center__main-content">
          <!-- 左侧导航栏 -->
          <aside class="sidebar">
            <ul class="sidebar__nav">
              <li
                :class="`sidebar__nav-item ${
                  curLayer == item.component ? 'sidebar__nav-item--active' : ''
                }`"
                v-for="item in sidebarList"
                :key="item.component"
                @click="curLayer = item.component"
              >
                {{ item.label }}
              </li>
            </ul>
          </aside>

          <!-- 右侧信息展示区 -->
          <section class="info-display">
            <component :is="layouts[curLayer]" :userInfo="userInfo" :type="curLayer" />
          </section>
        </main>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, defineAsyncComponent } from "vue";
import moment from "moment";
import { getObj } from "/@/api/admin/user";
import { useUserInfo } from "/@/stores/userInfo";
import other from "/@/utils/other";

const userInfo = ref<any>({});
const currentDate = ref({
  day: '',
  weekday: '',
  yearMonth: ''
});

watch(() => useUserInfo().userInfos.user, (newValue) => {
  initUserInfo(newValue.userId);
});

const sidebarList = ref([
  {
    label: "我的信息",
    component: "userInfo",
  },
  {
    label: "我的申请",
    component: "myApply",
  },
  {
    label: "我的点赞",
    component: "myLike",
  },
  {
    label: "我的收藏",
    component: "myCollect",
  },
  {
    label: "我的下载",
    component: "myDownload",
  },
  {
    label: "我的分享",
    component: "myShare",
  },
]);
const curLayer = ref("userInfo");
const layouts: any = {
  userInfo: defineAsyncComponent(() => import("./userInfo.vue")),
  myApply: defineAsyncComponent(() => import("./myApply.vue")),
  myLike: defineAsyncComponent(() => import("./myCollect.vue")),
  myCollect: defineAsyncComponent(() => import("./myCollect.vue")),
  myShare: defineAsyncComponent(() => import("./myCollect.vue")),
  myDownload: defineAsyncComponent(() => import("./myCollect.vue")),
};

const updateCurrentDate = () => {
  const now = moment();
  currentDate.value = {
    day: now.format('DD'),
    weekday: '星期' + '日一二三四五六'.charAt(now.day()),
    yearMonth: now.format('YYYY年MM月')
  };
};

onMounted(() => {
  updateCurrentDate();
}); // 初始化用户信息
const initUserInfo = (userId: any) => {
  getObj(userId).then((res) => {
    userInfo.value = res.data || {};
  })
};
</script>

<style lang="scss" scoped>
@charset "UTF-8";

// 颜色变量
$primary-color: #a65851;
$secondary-color: #6a6a6a;
$text-color: #333;
$light-text-color: #999;
$border-color: #eee;
$background-color: #f7f3ed;
$sidebar-active-bg: #8c3f39;
$sidebar-active-text: #fff;

// 字体大小变量
$font-size-small: 12px;
$font-size-medium: 14px;
$font-size-large: 16px;
$font-size-xlarge: 24px;
$font-size-xxlarge: 48px;
.person-center-container {
  margin-bottom: 20px;
}
.person-center {
  font-family: "Microsoft YaHei", sans-serif;
  color: $text-color;
  border-radius: 8px;
  min-width: 1400px;
  margin: 0 210px;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 40px 20px;
    background-color: #fff;
    border-bottom: 1px solid $border-color;
    height: 200px;
    box-sizing: border-box;
    margin-bottom: 20px;

    .user-info {
      display: flex;
      align-items: center;

      &__avatar {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        margin-right: 20px;
        object-fit: cover;
      }

      &__details {
        display: flex;
        flex-direction: column;

        .user-info__name {
          font-weight: 400;
          color: #393939;
          margin-bottom: 20px;
          .name-box {
            font-weight: 900;
            font-size: 24px;
          }
          .phone-box {
            font-size: 14px;
          }
        }

        .rank-info-box {
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 14px;
          line-height: 20px;
          letter-spacing: 0px;
          color: #554242;
          .user-info__role {
            margin-right: 30px;
          }

          .user-info__login-time {
          }
        }
      }
    }

    .date-info {
      display: flex;
      align-items: center;
      color: $secondary-color;

      &__day {
        font-family: Source Han Sans CN;
        font-weight: 500;
        font-size: 60px;
        line-height: 150%;
        letter-spacing: 0px;
        color: #554242;
        margin-right: 16px;
      }

      &__details {
        display: flex;
        flex-direction: column;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 18px;
        line-height: 20px;
        letter-spacing: 0px;
        color: #554242;

        .date-info__weekday {
          margin-bottom: 8px;
        }

        .date-info__year-month {
        }
      }
    }
  }

  &__main-content {
    display: flex;
    min-height: 500px; /* 示例高度 */
    height: calc(100% - 220px);
    .sidebar {
      width: 260px;
      background-color: #fff;
      margin-right: 20px;

      &__nav {
        list-style: none;
        padding: 0;
        margin: 0;

        &-item {
          padding: 14px 24px;
          cursor: pointer;
          transition: all 0.3s ease;
          font-weight: 500;
          font-size: 16px;
          letter-spacing: 0%;
          vertical-align: middle;
          color: #7b7b7b;
          border-bottom: 1px solid #E2E2E2;

          &:hover {
            background-image: url("/@/assets/img/squareNew/navBg.png");
            background-size: 100% 100%;
            color: #fff;
          }

          &--active {
            background-image: url("/@/assets/img/squareNew/navBg.png");
            background-size: 100% 100%;
            color: #fff;
            font-weight: 500;
          }
        }
      }
    }

    .info-display {
      flex: 1;
      background-color: #fff;
      padding: 24px;
      overflow: auto; /* Add this to enable scrolling at the container level */

      &__title {
        margin-bottom: 10px;
        text-align: center;
        position: relative;
        font-weight: 900;
        font-size: 24px;
        line-height: 24.61px;
        letter-spacing: 0%;
        text-align: center;
        vertical-align: middle;
        color: #393939;

        &::before,
        &::after {
          content: ""; // En dash
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          width: 34px;
          height: 17px;
          background-size: 100% 100%;
        }

        &::before {
          left: calc(50% - 90px); /* Adjust based on title width */
          background-image: url("/@/assets/img/squareNew/userLeftBg.png");
        }

        &::after {
          background-image: url("/@/assets/img/squareNew/userRightBg.png");
          right: calc(50% - 90px); /* Adjust based on title width */
        }
      }

      .account-profile,
      .basic-info {
        .title-box {
          margin-bottom: 20px;
          font-weight: 700;
          font-size: 16px;
          line-height: 24.61px;
          letter-spacing: 0%;
          vertical-align: bottom;
          color: #862626;
          position: relative;
          padding-left: 12px;
          &::before {
            content: "";
            position: absolute;

            width: 2px;
            height: 20px;
            background: #862626;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
          }
        }

        &__item {
          display: flex;
          flex-wrap: wrap;
          align-items: baseline;
          margin-bottom: 15px;

          .label {
            width: 70px;
            text-align: left;
            margin-right: 10px;
            font-weight: 500;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0%;
            color: #a1a1a1;
          }

          .value {
            flex: 1;
            min-width: 150px;
            font-weight: 500;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0%;
            color: #393939;
          }

          &.account-profile__item {
            .label {
              width: 70px;
              text-align: left;
            }
          }

          .account-profile__avatar {
            width: 60px;
            height: 60px;
            border-radius: 4%;
            margin-right: 20px;
          }

          .account-profile__change-avatar {
            color: $primary-color;
            text-decoration: none;
            font-size: $font-size-small;

            &:hover {
              text-decoration: underline;
            }
          }
        }
      }
    }
  }
}
</style>
