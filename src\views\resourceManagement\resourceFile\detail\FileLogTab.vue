<template>
  <div class="layout-padding-auto file-log-tab">
    <el-row class="mb0">
      <el-col :span="5">
        <el-button
          @click="onOperation('DoExport')"
          :disabled="selectDatas.length === 0"
          type="primary"
          >批量导出</el-button
        >
      </el-col>
      <el-col :span="19" class="text-right">
        <el-form
          :model="state.queryForm"
          ref="queryRef"
          :inline="true"
          @keyup.enter="getDataList"
          class="custom-form-inline"
          style="height: 45px"
        >
          <el-form-item label="操作类型：" prop="state" style="width: 300px">
            <el-select
              v-model="state.queryForm.state"
              clearable
              placeholder="请选择申请权限"
            >
              <el-option label="查看" value="1" />
              <el-option label="修改" value="23" />
              <el-option label="删除" value="24" />
              <el-option label="评论" value="3" />
              <el-option label="统计" value="28" />
            </el-select>
          </el-form-item>
          <el-form-item style="margin-right: 0">
            <el-button icon="search" type="primary" @click="getDataList">查询</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <div style="display: flex; flex-direction: column; height: calc(100% - 140px)">
      <el-table
        :data="state.dataList || []"
        v-loading="state.loading"
        show-overflow-tooltip
        border
        :cell-style="tableStyle.cellStyle"
        :header-cell-style="tableStyle.headerCellStyle"
        @selection-change="selectionChangHandle"
        class="custom-table-control"
      >
        <el-table-column type="selection" width="40" align="center" />
        <el-table-column
          v-for="(item, k) in tableColumn"
          align="center"
          :key="k"
          :prop="item.key"
          :label="item.title"
          :width="item.width || 'auto'"
        >
          <template #default="scope">
            <div v-if="item.key === 'czdx'" style="height: 32px; line-height: 32px">
              <span
                >【{{ props.record.assetsNum || "暂无编码" }}】{{
                  props.record.name
                }}</span
              >
            </div>
            <div v-else style="height: 32px; line-height: 32px">
              <span>{{ scope.row[item.key] || "--" }}</span>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        v-bind="state.pagination"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { operateLogList, getListAllUser } from "/@/api/resource/filelist/file";
import { BasicTableProps, useTable } from "/@/hooks/table";
const props = defineProps({
  record: {
    type: Object,
    default: null,
  },
});
const queryRef = ref();
const selectDatas = ref([]);
const userList = ref<any>([]);
const tableColumn: any = ref([
  { title: "操作对象", key: "czdx" },
  { title: "操作类型", key: "operateTypeName", width: "120px" },
  { title: "操作内容", key: "title" },
  { title: "操作人员", key: "createUser" },
  { title: "操作时间", key: "createTime" },
]);
const state: BasicTableProps = reactive<BasicTableProps>({
  queryForm: {
    tableName: undefined,
    businessId: "",
    userId: undefined,
    state: undefined
  },
  // descs: ["gmt_create"],
  pageList: operateLogList,
  createdIsNeed: false,
  props: {
    item: "records",
    totalCount: "total",
  },
  isPage: true,
});
const {
  tableStyle,
  getDataList,
  currentChangeHandle,
  sizeChangeHandle,
  downBlobFile,
} = useTable(state);
// 清空搜索条件
const resetQuery = () => {
  queryRef.value?.resetFields();
  getDataList();
};
// 多选事件
const selectionChangHandle = (objs: any) => {
  selectDatas.value = objs;
};
const onOperation = (type: string) => {
  switch (type) {
    case "DoExport":
      let ids = selectDatas.value.map((obj: any) => {
        return obj.id;
      });
      let params = { businessId: props.record.id, ids: ids.join(",") };
      downBlobFile("/datacenter/operate/log/export", params, "operateLog.xlsx");
      break;
  }
};

const queryListAllUser = () => {
  getListAllUser({}).then((res) => {
    if (res.ok) {
      userList.value = res.data || [];
    }
  });
};

onMounted(() => {
  state.queryForm.businessId = props.record.id;
  getDataList();
  queryListAllUser()
  // 重置表单数据
  nextTick(() => {});
});
</script>

<style scoped lang="scss">
.file-log-tab {
  background: #fff;
	border-radius: 5px;
  padding: 20px;
  height: 100%;
}
.group-header {
  margin-bottom: 10px;

  .group-title {
    font-size: 16px;
    font-weight: 600;
    padding-left: 15px;
    position: relative;
    color: #554242;
    letter-spacing: 0.1em;

    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 15%;
      width: 3px;
      height: 70%;
      border-radius: 6px;
      background: var(--el-color-primary);
    }
  }

  .group-add {
    cursor: pointer;
    color: var(--el-color-primary);
  }
}
.group-list {
  height: calc(100% - 70px);
  overflow-y: auto;

  .group-item {
    cursor: pointer;
    padding: 8px 10px;
    border-radius: 6px;

    &.active {
      background: var(--el-color-primary-light-9);
    }

    .group-name {
      width: 200px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      span {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .group-icon {
        margin-right: 10px;
        width: 20px;
      }
    }
    &:hover {
      background-color: #cccccc65;
    }
  }
}

.operation-btns {
  button.el-button.el-button--primary {
    --el-button-text-color: #409eff;
  }
}
</style>
