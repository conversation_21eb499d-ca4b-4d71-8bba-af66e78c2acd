import request from "/@/utils/request"

// 获取分类列表
export function fetchGroupList() {
  return request({
    url: '/archive/wb/manage/listClassify',
    method: 'get',
  })
}

// 查询资源分类统计
export function groupByClassify(query?: Object) {
  return request({
    url: '/archive/wb/resource/groupByClassify',
    method: 'get',
    params: query,
  })
}

// 高级检索
export function superiorSearch(data?: any) {
  return request({
    url: '/archive/wb/resource/superiorSearch',
    method: 'POST',
    data,
  })
}

// 高级检索-分类统计
export function groupByClassifyByParam(data?: any) {
  return request({
    url: '/archive/wb/resource/groupByClassifyByParam',
    method: 'POST',
    data,
  })
}

// 全文检索
export function fullTextSearch(data?: any) {
  return request({
    url: '/archive/wb/resource/fullTextSearch',
    method: 'POST',
    data,
  })
}

// 全文检索-分类统计
export function groupByClassifyByFullText(data?: any) {
  return request({
    url: '/archive/wb/resource/groupByClassifyByFullText',
    method: 'POST',
    data,
  })
}