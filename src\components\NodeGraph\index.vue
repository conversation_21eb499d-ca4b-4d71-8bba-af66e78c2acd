<template>
	<div id="fileGraph" class="file-graph"></div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { relationDefaultConfig } from '/@/config/resourceConfig';
const props = defineProps({
	data: {
		type: Object,
		default: () => {},
	},
	colorList: {
		type: Array,
		default: () => ['242,243,255', '227,249,233', '255,216,210', '217,225,255'],
	},
});
const visGraphObj = ref<any>(null); // 关系图谱对象

onMounted(() => {});

// 构建关系图谱
const setGraph = () => {
	if (props.data?.nodes?.length) {
		props.data.nodes.forEach((item: any, index: number) => {
			item.color = props.colorList[index % props.colorList.length];
		});
		let domGraph = document.getElementById('fileGraph');
		visGraphObj.value = new VisGraph(domGraph, relationDefaultConfig);
		visGraphObj.value.drawData(props.data);
		runLayout();
	} else {
		clearGraph();
	}
};

// 关系图谱布局
const runLayout = () => {
	let fastLayout = new LayoutFactory(visGraphObj.value.getGraphData()).createLayout('fastFR');
	fastLayout.initAlgo(); // 初始化布局算法
	let layoutConf = {
		froce: 0.95,
		linkDistance: 190,
		linkStrength: 0.09,
		charge: -300,
		gravity: 0.009,
		noverlap: true,
	};
	fastLayout.resetConfig(layoutConf); // 设置布局算法参数
	let runLoopNum = 0;
	while (runLoopNum++ < 1000) {
		fastLayout.runLayout(); // 执行布局计算
	}
	visGraphObj.value.setZoom('auto'); // 自动缩放
};

// 清空关系图谱
const clearGraph = () => {
	if (visGraphObj.value) {
		visGraphObj.value.clearAll();
		document.getElementById('fileGraph')!.innerHTML = '';
		visGraphObj.value = null;
	}
};

onBeforeUnmount(() => {
	clearGraph();
});


defineExpose({
	clearGraph,
	setGraph,
});
</script>

<style lang="scss" scoped>
.file-graph {
	width: 100%;
	height: 100%;
}
</style>
