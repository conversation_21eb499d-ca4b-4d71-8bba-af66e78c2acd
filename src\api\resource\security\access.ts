import request from "/@/utils/request";

// 分页查询
export function fetchList(query?: Object) {
  return request({
    url: "/datacenter/security/access/page",
    method: "get",
    params: query,
  });
}
// 新增权限组
export function addObj(obj?: Object) {
  return request({
    url: "/datacenter/security/access/add",
    method: "post",
    data: obj,
  });
}
// 更新权限组
export function updateObj(obj?: Object) {
  return request({
    url: "/datacenter/security/access/update",
    method: "post",
    data: obj,
  });
}
// 删除权限组
export function delObj(id?: Object) {
  return request({
    url: "/datacenter/security/access/delete",
    method: "post",
    data: id,
  });
}
