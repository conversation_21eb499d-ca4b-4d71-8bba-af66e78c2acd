import request from "/@/utils/request";

// 查询服务器基本信息
export function queryServiceBaseInfo(data?: Object) {
  return request({
    url: "/datacenter/data/resource/cardCount",
    method: "post",
    data,
  });
}

// 查询资源趋势图
export function queryTrendChart(data?: Object) {
  return request({
    url: "/datacenter/data/resource/trendChart",
    method: "post",
    data,
  });
}

// 查询资源趋势图
export function queryDeptRangeList(data?: Object) {
  return request({
    url: "/datacenter/data/resource/deptRange",
    method: "post",
    data,
  });
}

// 查询资源数量分布
export function queryResourceDistribution(data?: Object) {
  return request({
    url: "/datacenter/data/resource/resourceDistribution",
    method: "post",
    data,
  });
}

// 查询资源数量分布
export function queryFileDistribution(data?: Object) {
  return request({
    url: "/datacenter/data/resource/fileDistribution",
    method: "post",
    data,
  });
}

// 查询资源操作排名
export function queryResourceActionRank(params?: Object) {
  return request({
    url: "/datacenter/resource/action/rank",
    method: "get",
    params,
  });
}


// 查询资源热门搜索排名
export function queryKeywordRank(params?: Object) {
  return request({
    url: "/datacenter/operate/log/keywordRank",
    method: "get",
    params,
  });
}

