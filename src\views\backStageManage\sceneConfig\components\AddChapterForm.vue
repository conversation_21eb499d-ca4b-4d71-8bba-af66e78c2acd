<template>
  <div class="chapter-form">
    <el-form ref="formRef" :model="formData" :rules="formRules" :label-position="'top'" label-width="100px" class="form-content">
      <!-- 基本信息 -->
      <div class="form-section">
        <h4 class="section-title">
          <el-icon><Files /></el-icon>
          基本信息
        </h4>

        <el-form-item label="章节名称" prop="nodeName" required>
          <el-input v-model="formData.nodeName" placeholder="请输入名称" maxlength="50" show-word-limit />
        </el-form-item>

        <el-form-item label="章节序号" prop="serialNumber" required>
          <el-input-number v-model="formData.serialNumber" placeholder="请输入序号" :min="1"/>
        </el-form-item>

        <el-form-item label="章节简介" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            placeholder="请输入内容"
            :rows="4"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { Files } from '@element-plus/icons-vue';

interface ChapterFormData {
  nodeName: string;
  description: string;
  serialNumber: number;
}

interface Emits {
  (e: 'data-change', data: ChapterFormData): void;
}

const emit = defineEmits<Emits>();

// 响应式数据
const formRef = ref();

const formData = reactive<ChapterFormData>({
  nodeName: '',
  description: '',
  serialNumber: 1,
});

// 表单验证规则
const formRules = {
  nodeName: [
    { required: true, message: '请输入章节名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
};

// 监听表单数据变化
watch(formData, (newData) => {
  emit('data-change', { ...newData });
}, { deep: true });

// 获取表单数据
const getFormData = () => {
  return { ...formData };
};

// 设置表单数据
const setFormData = (data: Partial<ChapterFormData>) => {
  Object.assign(formData, data);
};

// 验证表单
const validateForm = async () => {
  if (!formRef.value) return false;
  try {
    await formRef.value.validate();
    return true;
  } catch {
    return false;
  }
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  Object.assign(formData, {
    nodeName: '',
    description: '',
    coverFileId: '',
    coverUrl: ''
  });
};

// 暴露方法
defineExpose({
  getFormData,
  setFormData,
  validateForm,
  resetForm
});
</script>

<style lang="scss" scoped>
.chapter-form {
  padding: 0 20px;

  .form-content {
    .form-section {
      margin-bottom: 24px;

      .section-title {
        display: flex;
        align-items: center;
        margin: 0 0 16px 0;
        font-size: 14px;
        font-weight: 600;
        color: #1d2129;
      }
    }

    .upload-container {
      .upload-hint {
        margin-top: 8px;
        font-size: 12px;
        color: #86909c;
      }
    }
  }
}

:deep(.el-form-item__label) {
  color: #1d2129;
  font-weight: 500;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
}
</style>