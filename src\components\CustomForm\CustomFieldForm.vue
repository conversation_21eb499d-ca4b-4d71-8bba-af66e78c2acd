<template>
  <el-form
    ref="customFormRef"
    class="custom-fieldForm-container"
    :model="form"
    :rules="dataRules"
    label-width="120px"
  >
    <el-row :gutter="24" class="mb20">
      <el-col
        :span="['multiLine', 'tichText'].includes(item.typeCode) ? 24 : 24 / colSpan"
        v-for="(item, index) in columnInfoList"
        :key="index"
        class="mb20"
      >
        <el-form-item :label="item.cnName" :prop="item.name">
          <template #label>
            <span :title="item.cnName">{{ item.cnName }}</span>
          </template>
          <CustomFieldEditor
            v-if="props.canEditor && !item.columnRule.readOnly"
            v-model="form[item.name]"
            :columnInfo="item"
          />
          <span v-else-if="['dateRange', 'multiOption'].includes(item.typeCode)">{{
            form[item.name] && form[item.name]?.length > 0
              ? item.typeCode == "dateRange"
                ? form[item.name].join(" 至 ")
                : form[item.name].join("，")
              : "--"
          }}</span>
          <div v-else-if="['tichText'].includes(item.typeCode)" style="width: 100%">
            <editor
              v-model:get-html="form[item.name]"
              :placeholder="item.prompt"
              style="width: 100%"
              :isPreview="true"
              :disable="true"
            />
          </div>
          <span
            v-else-if="
              ['province'].includes(item.typeCode) && form[item.name]?.length > 0
            "
          >
            <span v-for="(code, index) in form[item.name].split(',')" :key="index">
              {{ (index > 0 ? "  >>  " : "") + CodeToText[code] }}
            </span>
          </span>
          <span v-else-if="['image'].includes(item.typeCode)">
            <ImageUpload
              v-for="(img_obj, index) in form[item.name]"
              :key="index"
              v-model:imageUrl="img_obj.url"
              :disabled="true"
              borderRadius="0%"
              width="120px"
              height="80px"
              style="float: left; margin-right: 15px"
            >
              <template #empty>
                <el-icon><Picture /></el-icon>
                <span>请上传数据</span>
              </template>
            </ImageUpload>
          </span>
          <span
            v-else-if="['attach'].includes(item.typeCode) && form[item.name]?.length > 0"
          >
            <div v-for="(file_obj, index) in form[item.name]" :key="index">
              {{ file_obj.name || "--" }}
            </div>
          </span>
          <span v-else>{{
            form[item.name + "_label"]
              ? form[item.name + "_label"]
              : form[item.name] || "--"
          }}</span>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts" name="upload-file">
import other from "/@/utils/other";
import { CodeToText } from "/@/utils/chinaArea";
import { inject } from "vue";

const ImageUpload = defineAsyncComponent(() => import("/@/components/Upload/Image.vue"));
const CustomFieldEditor = defineAsyncComponent(() => import("./CustomFieldEditor.vue"));
const props = defineProps({
  colSpan: {
    type: Number,
    default: 3,
  },
  columnList: {
    type: Object as any,
    default: () => [],
  },
  canEditor: {
    type: Boolean,
    default: true,
  },
});
const customFormRef: any = ref();
const form: any = ref({});
const dataRules: any = ref({});
const columnInfoList: any = ref([]);
const formData = inject("formData");

onMounted(() => {
  // debugger
  // columnInfoList.value = props.columnList;
  // columnInfoList.value.forEach((obj: any) => {
  //   obj.columnRule = Array.isArray(obj.columnRule)
  //     ? obj.columnRule
  //     : obj.columnRule?.length
  //     ? JSON.parse(obj.columnRule)
  //     : obj.columnRule || {};
  //   obj.columnCustomeRule = Array.isArray(obj.columnCustomeRule)
  //     ? obj.columnCustomeRule
  //     : obj.columnCustomeRule?.length
  //     ? JSON.parse(obj.columnCustomeRule)
  //     : obj.columnCustomeRule || {};
  //   // obj.columnCustomeRule
  //   // ? JSON.parse(obj.columnCustomeRule)
  //   // : {};
  //   form.value[obj.name] = obj.object || obj.columnCustomeRule.defaultVal;
  //   dataRules.value[obj.name] = [];
  //   if (obj.columnRule.mustFilee)
  //     dataRules.value[obj.name].push({
  //       required: obj.columnRule.mustFilee,
  //       message: obj.prompt || obj.cnName + "不能为空",
  //       trigger: "change",
  //     });
  //   if (["oneLine", "multiLine", "tichText"].includes(obj.typeCode)) {
  //     obj.columnCustomeRule.maxLength > 0 &&
  //       dataRules.value[obj.name].push({
  //         validator: (rule: any, value: any, callback: any) => {
  //           if (
  //             value?.length < obj.columnCustomeRule.minLength &&
  //             value?.length > obj.columnCustomeRule.maxLength
  //           ) {
  //             callback(
  //               new Error(
  //                 obj.cnName +
  //                   "的长度必须在" +
  //                   obj.columnCustomeRule.minLength +
  //                   "到" +
  //                   obj.columnCustomeRule.maxLength +
  //                   "之间"
  //               )
  //             );
  //           } else {
  //             callback();
  //           }
  //         },
  //       });
  //     if (!obj.prompt) obj.prompt = "请输入" + obj.cnName;
  //   } else if (["number", "amount"].includes(obj.typeCode)) {
  //     form.value[obj.name] = Number(obj.object || obj.columnCustomeRule.defaultVal || 0);
  //     if (obj.columnCustomeRule.format == "百分比" && !obj.columnCustomeRule.unit)
  //       obj.columnCustomeRule.unit = "%";
  //     obj.cnName += obj.columnCustomeRule.unit
  //       ? "（" + obj.columnCustomeRule.unit + "）"
  //       : "";
  //     // dataRules.value[obj.name].push({ type: "number", message: "请输入数值" });
  //     obj.columnCustomeRule.range?.length > 0 &&
  //       dataRules.value[obj.name].push({
  //         validator: (rule: any, value: any, callback: any) => {
  //           if (
  //             value < obj.columnCustomeRule.range[0] &&
  //             value > obj.columnCustomeRule.range[1]
  //           ) {
  //             callback(
  //               new Error(
  //                 obj.cnName +
  //                   "的长度必须在" +
  //                   obj.columnCustomeRule.range[0] +
  //                   "到" +
  //                   obj.columnCustomeRule.range[1] +
  //                   "之间"
  //               )
  //             );
  //           } else {
  //             callback();
  //           }
  //         },
  //       });
  //   } else if (["date"].includes(obj.typeCode)) {
  //     if (!obj.prompt) obj.prompt = "请选择" + obj.cnName;
  //     obj.columnCustomeRule.range?.length > 0 &&
  //       dataRules.value[obj.name].push({
  //         validator: (rule: any, value: any, callback: any) => {
  //           if (value && new Date(value) > new Date(obj.columnCustomeRule.range[1])) {
  //             callback(
  //               new Error(
  //                 "时间范围必须在" +
  //                   obj.columnCustomeRule.range[0] +
  //                   "到" +
  //                   obj.columnCustomeRule.range[1] +
  //                   "之间"
  //               )
  //             );
  //           } else if (
  //             value &&
  //             new Date(value) < new Date(obj.columnCustomeRule.range[0])
  //           ) {
  //             callback(
  //               new Error(
  //                 "时间范围必须在" +
  //                   obj.columnCustomeRule.range[0] +
  //                   "到" +
  //                   obj.columnCustomeRule.range[1] +
  //                   "之间"
  //               )
  //             );
  //           } else {
  //             callback();
  //           }
  //         },
  //       });
  //   } else if (["dateRange"].includes(obj.typeCode)) {
  //     if (!obj.prompt) obj.prompt = "请选择" + obj.cnName;
  //     form.value[obj.name] = obj.object || [];
  //     obj.columnCustomeRule.range?.length > 0 &&
  //       dataRules.value[obj.name].push({
  //         validator: (rule: any, value: any, callback: any) => {
  //           if (
  //             value[1] &&
  //             new Date(value[1]) > new Date(obj.columnCustomeRule.range[1])
  //           ) {
  //             callback(
  //               new Error(
  //                 "时间范围必须在" +
  //                   obj.columnCustomeRule.range[0] +
  //                   "到" +
  //                   obj.columnCustomeRule.range[1] +
  //                   "之间"
  //               )
  //             );
  //           } else if (
  //             value[0] &&
  //             new Date(value[0]) < new Date(obj.columnCustomeRule.range[0])
  //           ) {
  //             callback(
  //               new Error(
  //                 "时间范围必须在" +
  //                   obj.columnCustomeRule.range[0] +
  //                   "到" +
  //                   obj.columnCustomeRule.range[1] +
  //                   "之间"
  //               )
  //             );
  //           } else {
  //             callback();
  //           }
  //         },
  //       });
  //     dataRules.value[obj.name].push({
  //       validator: (rule: any, value: any, callback: any) => {
  //         if (value[1] && new Date(value[0]) > new Date(value[1])) {
  //           callback(new Error("开始时间必须小于结束时间"));
  //         } else {
  //           callback();
  //         }
  //       },
  //     });
  //   } else if (["ratioOption"].includes(obj.typeCode)) {
  //     if (!obj.prompt) obj.prompt = "请选择" + obj.cnName;
  //   } else if (["multiOption"].includes(obj.typeCode)) {
  //     if (!obj.prompt) obj.prompt = "请选择" + obj.cnName;
  //   } else if (["province"].includes(obj.typeCode)) {
  //     form.value[obj.name] = form.value[obj.name] || "";
  //     if (!obj.prompt) obj.prompt = "请选择" + obj.cnName;
  //   } else if (["image", "attach"].includes(obj.typeCode)) {
  //     if (!obj.prompt) obj.prompt = "请上传" + obj.cnName;
  //   }
  // });
});

watch(
  () => props.columnList,
  () => {
    columnInfoList.value = props.columnList;
    columnInfoList.value.forEach((obj: any) => {
      obj.columnRule = Array.isArray(obj.columnRule)
        ? obj.columnRule
        : obj.columnRule?.length
        ? JSON.parse(obj.columnRule)
        : obj.columnRule || {};
      obj.columnCustomeRule = Array.isArray(obj.columnCustomeRule)
        ? obj.columnCustomeRule
        : obj.columnCustomeRule?.length
        ? JSON.parse(obj.columnCustomeRule)
        : obj.columnCustomeRule || {};
      // obj.columnCustomeRule
      // ? JSON.parse(obj.columnCustomeRule)
      // : {};
      form.value[obj.name] = form.value[obj.name] || obj.columnCustomeRule.defaultVal;
      dataRules.value[obj.name] = [];
      if (obj.columnRule.mustFilee)
        dataRules.value[obj.name].push({
          required: obj.columnRule.mustFilee,
          message: obj.prompt || obj.cnName + "不能为空",
          trigger: "change",
        });
      if (["oneLine", "multiLine", "tichText"].includes(obj.typeCode)) {
        obj.columnCustomeRule.maxLength > 0 &&
          dataRules.value[obj.name].push({
            validator: (rule: any, value: any, callback: any) => {
              if (
                value?.length < obj.columnCustomeRule.minLength &&
                value?.length > obj.columnCustomeRule.maxLength
              ) {
                callback(
                  new Error(
                    obj.cnName +
                      "的长度必须在" +
                      obj.columnCustomeRule.minLength +
                      "到" +
                      obj.columnCustomeRule.maxLength +
                      "之间"
                  )
                );
              } else {
                callback();
              }
            },
          });
        if (!obj.prompt) obj.prompt = "请输入" + obj.cnName;
      } else if (["number", "amount"].includes(obj.typeCode)) {
        form.value[obj.name] = Number(
          form.value[obj.name]|| obj.columnCustomeRule.defaultVal || 0
        );
        if (obj.columnCustomeRule.format == "百分比" && !obj.columnCustomeRule.unit)
          obj.columnCustomeRule.unit = "%";
        obj.cnName += obj.columnCustomeRule.unit
          ? "（" + obj.columnCustomeRule.unit + "）"
          : "";
        // dataRules.value[obj.name].push({ type: "number", message: "请输入数值" });
        obj.columnCustomeRule.range?.length > 0 &&
          dataRules.value[obj.name].push({
            validator: (rule: any, value: any, callback: any) => {
              if (
                value < obj.columnCustomeRule.range[0] &&
                value > obj.columnCustomeRule.range[1]
              ) {
                callback(
                  new Error(
                    obj.cnName +
                      "的长度必须在" +
                      obj.columnCustomeRule.range[0] +
                      "到" +
                      obj.columnCustomeRule.range[1] +
                      "之间"
                  )
                );
              } else {
                callback();
              }
            },
          });
      } else if (["date"].includes(obj.typeCode)) {
        if (!obj.prompt) obj.prompt = "请选择" + obj.cnName;
        obj.columnCustomeRule.range?.length > 0 &&
          dataRules.value[obj.name].push({
            validator: (rule: any, value: any, callback: any) => {
              if (value && new Date(value) > new Date(obj.columnCustomeRule.range[1])) {
                callback(
                  new Error(
                    "时间范围必须在" +
                      obj.columnCustomeRule.range[0] +
                      "到" +
                      obj.columnCustomeRule.range[1] +
                      "之间"
                  )
                );
              } else if (
                value &&
                new Date(value) < new Date(obj.columnCustomeRule.range[0])
              ) {
                callback(
                  new Error(
                    "时间范围必须在" +
                      obj.columnCustomeRule.range[0] +
                      "到" +
                      obj.columnCustomeRule.range[1] +
                      "之间"
                  )
                );
              } else {
                callback();
              }
            },
          });
      } else if (["dateRange"].includes(obj.typeCode)) {
        if (!obj.prompt) obj.prompt = "请选择" + obj.cnName;
        form.value[obj.name] = form.value[obj.name] || [];
        obj.columnCustomeRule.range?.length > 0 &&
          dataRules.value[obj.name].push({
            validator: (rule: any, value: any, callback: any) => {
              if (
                value[1] &&
                new Date(value[1]) > new Date(obj.columnCustomeRule.range[1])
              ) {
                callback(
                  new Error(
                    "时间范围必须在" +
                      obj.columnCustomeRule.range[0] +
                      "到" +
                      obj.columnCustomeRule.range[1] +
                      "之间"
                  )
                );
              } else if (
                value[0] &&
                new Date(value[0]) < new Date(obj.columnCustomeRule.range[0])
              ) {
                callback(
                  new Error(
                    "时间范围必须在" +
                      obj.columnCustomeRule.range[0] +
                      "到" +
                      obj.columnCustomeRule.range[1] +
                      "之间"
                  )
                );
              } else {
                callback();
              }
            },
          });
        dataRules.value[obj.name].push({
          validator: (rule: any, value: any, callback: any) => {
            if (value[1] && new Date(value[0]) > new Date(value[1])) {
              callback(new Error("开始时间必须小于结束时间"));
            } else {
              callback();
            }
          },
        });
      } else if (["ratioOption"].includes(obj.typeCode)) {
        if (!obj.prompt) obj.prompt = "请选择" + obj.cnName;
      } else if (["multiOption"].includes(obj.typeCode)) {
        if (!obj.prompt) obj.prompt = "请选择" + obj.cnName;
      } else if (["province"].includes(obj.typeCode)) {
        form.value[obj.name] = form.value[obj.name] || "";
        if (!obj.prompt) obj.prompt = "请选择" + obj.cnName;
      } else if (["image", "attach"].includes(obj.typeCode)) {
        if (!obj.prompt) obj.prompt = "请上传" + obj.cnName;
      }
    });
  },
  { deep: true, immediate: true }
);

watchEffect(() => {
  if (formData.value.id) {
    form.value = formData.value;
    console.log(' form.value~~~~~~',  form.value)
  }
});
const getData = () => {
  return form.value;
};
const validate = async () => {
  const valid = await customFormRef.value.validate().catch(() => {});
  if (!valid) return false;
  return true;
};

const setData = (formData: any = {}) => {
  form.value = formData;
};
defineExpose({
  getData,
  validate,
  setData,
});
</script>
<style lang="scss">
.custom-fieldForm-container {
  .el-row {
    .el-col {
      .el-form-item {
        label {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          display: inline-block;
          text-align: right;
        }
        label::before {
          display: inline-block;
        }
      }
    }
  }
}
</style>
