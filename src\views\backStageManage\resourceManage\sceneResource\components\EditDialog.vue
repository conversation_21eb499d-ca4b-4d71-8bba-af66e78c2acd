<template>
  <el-dialog v-model="visible" width="1200px" :close-on-click-modal="false" :close-on-press-escape="false"
    :show-close="false" destroy-on-close @close="handleClose" class="edit-dialog">
    <div class="edit-dialog-content">
      <!-- Tab导航 -->
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="基本信息" name="introduction">
          <Introduction ref="introductionRef" :data="resourceData" @update="handleDataUpdate" />
        </el-tab-pane>
        <el-tab-pane label="解说词" name="commentary">
          <Commentary ref="commentaryRef" :data="resourceData" @update="handleDataUpdate" />
        </el-tab-pane>
        <el-tab-pane label="展览配置" name="exhibition">
          <Exhibition ref="exhibitionRef" :data="resourceData" @update="handleDataUpdate" />
        </el-tab-pane>
      </el-tabs>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saveLoading">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import Introduction from './Introduction.vue'
import Commentary from './Commentary.vue'
import Exhibition from './Exhibition.vue'
import {
  getResourceDetail,
  saveBasicInfo,
  saveCommentary,
  saveExhibitionFiles,
  saveTimelineConfig,
  saveScrollConfig
} from '/@/api/backStageManage/resource'

interface Emits {
  (e: 'refresh'): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const visible = ref(false)
const activeTab = ref('introduction')
const saveLoading = ref(false)
const resourceId = ref('')

const resourceData = reactive({
  // 基本信息
  resourceId: '',
  buildingId: '',
  resourceName: '',
  cover: '',
  resourceType: '',
  resourceTypeName: '',
  labels: '',
  description: '',
  modelId: '',

  // 模型详情
  model: {
    id: '',
    mainFile: '',
    properties: '',
    url: '',
    fileExtension: ''
  },

  // 解说词
  commentary: '',

  // 文件列表
  pictureFiles: [] as Array<{
    id: string,
    mainFile: string,
    properties: string,
    url: string,
    fileExtension: string
  }>,
  audioFiles: [] as Array<{
    id: string,
    mainFile: string,
    properties: string,
    url: string,
    fileExtension: string
  }>,
  videoFiles: [] as Array<{
    id: string,
    mainFile: string,
    properties: string,
    url: string,
    fileExtension: string
  }>,
  subModelFiles: [] as Array<{
    id: string,
    mainFile: string,
    properties: string,
    url: string,
    fileExtension: string
  }>,

  // 文件描述
  pictureNote: '',
  audioNote: '',
  videoNote: '',
  subModelNote: '',

  // 时间轴配置
  timeAxisQuantity: 0,
  timeAxisList: [] as Array<{
    timeId: string,
    resourceId: string,
    sortOrder: number,
    timePhase: number,
    title: string,
    description: string,
    cover: string
  }>,

  // 长卷配置
  subScrollQuantity: 0,
  mainScroll: {
    scrollId: '',
    resourceId: '',
    mainPageFlag: '1',
    sortOrder: 0,
    cover: '',
    coverDescription: '',
    title: '',
    subtitle: '',
    description: ''
  },
  subScrollList: [] as Array<{
    scrollId: string,
    resourceId: string,
    mainPageFlag: string,
    sortOrder: number,
    cover: string,
    coverDescription: string,
    title: string,
    subtitle: string,
    description: string
  }>,

  // 创建和更新信息
  createBy: '',
  createTime: '',
  updateBy: '',
  updateTime: ''
})

// 组件引用
const introductionRef = ref()
const commentaryRef = ref()
const exhibitionRef = ref()

// 获取资源详情
const fetchResourceDetail = async () => {
  try {
    if (!resourceId.value) return

    const response = await getResourceDetail(resourceId.value)
    if (response.code === 0 && response.data) {
      // 直接赋值API返回的数据结构
      Object.assign(resourceData, {
        resourceId: response.data.resourceId || '',
        buildingId: response.data.buildingId || '',
        resourceName: response.data.resourceName || '',
        cover: response.data.cover || '',
        resourceType: response.data.resourceType || '',
        resourceTypeName: response.data.resourceTypeName || '',
        labels: response.data.labels || '',
        description: response.data.description || '',
        modelId: response.data.modelId || '',
        model: response.data.model || {},
        commentary: response.data.commentary || '',
        pictureFiles: response.data.pictureFiles || [],
        audioFiles: response.data.audioFiles || [],
        videoFiles: response.data.videoFiles || [],
        subModelFiles: response.data.subModelFiles || [],
        pictureNote: response.data.pictureNote || '',
        audioNote: response.data.audioNote || '',
        videoNote: response.data.videoNote || '',
        subModelNote: response.data.subModelNote || '',
        timeAxisQuantity: response.data.timeAxisQuantity || 0,
        timeAxisList: response.data.timeAxisList || [],
        subScrollQuantity: response.data.subScrollQuantity || 0,
        mainScroll: response.data.mainScroll || {},
        subScrollList: response.data.subScrollList || [],
        createBy: response.data.createBy || '',
        createTime: response.data.createTime || '',
        updateBy: response.data.updateBy || '',
        updateTime: response.data.updateTime || ''
      })
    } else {
      ElMessage.error(response.message || '获取资源详情失败')
    }
  } catch (error) {
    ElMessage.error('获取资源详情失败')
  }
}

// 处理数据更新
const handleDataUpdate = (field: string, value: any) => {
  if (field.includes('.')) {
    // 处理嵌套字段
    const keys = field.split('.')
    let target = resourceData as any
    for (let i = 0; i < keys.length - 1; i++) {
      target = target[keys[i]]
    }
    target[keys[keys.length - 1]] = value
  } else {
    (resourceData as any)[field] = value
  }
}

// 处理Tab切换
const handleTabClick = (tab: any) => {
  activeTab.value = tab.name
}

// 保存数据
const handleSave = async () => {
  try {
    saveLoading.value = true

    const saveResults: Array<{ type: string, success: boolean, message?: string }> = []

    if (activeTab.value == "introduction") {
      const introData = await introductionRef.value.getData();
      const basicInfoData = {
        resourceId: resourceData.resourceId,
        resourceName: introData.resourceName,
        resourceType: introData.resourceType,
        buildingId: introData.buildingId,
        labels: introData.labels,
        description: introData.description,
        cover: introData.cover,
        modelId: introData.modelId
      }
      saveBasicInfo(basicInfoData).then(res => {
        return res
      }).catch(err => {

        throw err
      })

    } else if (activeTab.value == "commentary") {
      const commentaryData = await commentaryRef.value.getData()
      const commentaryPayload = {
        resourceId: resourceData.resourceId,
        commentary: commentaryData.commentary
      }
      saveCommentary(commentaryPayload).then(res => {
        return res
      }).catch(err => {
        throw err
      })
    } else if (activeTab.value == "exhibition") {
      const exhibitionData = await exhibitionRef.value.getData()
      saveExhibitionFiles(exhibitionData).then(res => {
        saveResults.push({ type: '展览文件配置', success: res.code === 200, message: res.message })
        return res
      }).catch(err => {
        saveResults.push({ type: '展览文件配置', success: false, message: err.message || '保存失败' })
        throw err
      })

      saveTimelineConfig({}).then(res => {
        saveResults.push({ type: '时间轴配置', success: res.code === 200, message: res.message })
        return res
      }).catch(err => {
        saveResults.push({ type: '时间轴配置', success: false, message: err.message || '保存失败' })
        throw err
      })


      saveScrollConfig({}).then(res => {
        saveResults.push({ type: '长卷配置', success: res.code === 200, message: res.message })
        return res
      }).catch(err => {
        saveResults.push({ type: '长卷配置', success: false, message: err.message || '保存失败' })
        throw err
      })
    }
    ElMessage.success('保存成功')
    emit('refresh')
    handleClose()
  } catch (error) {
    ElMessage.error('保存过程中发生错误')
  } finally {
    saveLoading.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  activeTab.value = 'introduction'
  // 重置数据
  Object.assign(resourceData, {
    id: '',
    resourceName: '',
    resourceType: '',
    buildingId: '',
    buildingName: '',
    labels: '',
    resourceDescription: '',
    cover: '',
    introduction: '',
    commentary: '',
    audioUrl: '',
    duration: '',
    autoPlay: false,
    loop: false,
    volume: 80,
    playbackRate: 1.0,
    showSubtitle: false,
    subtitleUrl: '',
    subtitleFileName: '',
    subtitleLanguage: 'zh-CN',
    subtitleSize: 16,
    exhibitionConfig: {
      exhibitionType: 'image',
      title: '',
      description: '',
      sortOrder: 1,
      enabled: true,
      images: [],
      imageConfig: {
        interval: 3000,
        autoPlay: true
      },
      videoConfig: {
        url: '',
        autoPlay: false,
        loop: false,
        muted: false
      },
      audioConfig: {
        url: '',
        autoPlay: false,
        loop: false
      },
      modelConfig: {
        url: '',
        fileName: '',
        autoRotate: true,
        showControls: true,
        showGrid: false
      },
      panoramaConfig: {
        url: '',
        autoRotate: true,
        rotateSpeed: 0.5
      }
    }
  })
}

// 打开弹窗
const openDialog = async (id: string) => {
  resourceId.value = id
  visible.value = true

  await nextTick()
  await fetchResourceDetail()
}

// 暴露方法
defineExpose({
  openDialog
})
</script>

<style lang="scss" scoped>
.edit-dialog {
  padding: 20px;
}

// 隐藏弹窗头部，不占用布局空间
:deep(.edit-dialog .el-dialog__header) {
  display: none !important;
}

// 调整弹窗主体区域
:deep(.el-dialog__body) {
  padding: 0;
}

.edit-dialog-content {
  height: 600px;
  overflow: hidden;

  :deep(.el-tabs) {
    height: 100%;
    display: flex;
    flex-direction: column;

    .el-tabs__header {
      margin: 0;
      border-bottom: 1px solid #e4e7ed;
    }

    .el-tabs__content {
      flex: 1;
      padding: 0;
      overflow: hidden;

      .el-tab-pane {
        height: 100%;
        overflow: auto;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
