<template>
  <div class="model_edit" id="model_view">
    <div class="model_view_container">
      <svg v-if="!loadSuccess" xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-box" viewBox="0 0 16 16">
        <path d="M8.186 1.113a.5.5 0 0 0-.372 0L1.846 3.5 8 5.961 14.154 3.5 8.186 1.113zM15 4.239l-6.5 2.6v7.922l6.5-2.6V4.24zM7.5 14.762V6.838L1 4.239v7.923l6.5 2.6zM7.443.184a1.5 1.5 0 0 1 1.114 0l7.129 2.852A.5.5 0 0 1 16 3.5v8.662a1 1 0 0 1-.629.928l-7.185 2.874a.5.5 0 0 1-.372 0L.63 13.09a1 1 0 0 1-.63-.928V3.5a.5.5 0 0 1 .314-.464L7.443.184z"></path>
      </svg>
      <div id="root"></div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ref, onMounted, computed } from "vue";
  const props = defineProps({
    url: {
      type: String,
      default: "",
    },
  });
  onMounted(() => {
    loadStyleSheet();
    if (window.modelViewer) {
      window.modelViewer.clear();
    }
    const script = document.createElement("script");
    script.src = "/lib/get3d/index-0.0.4.umd.js";
    script.addEventListener("load", handleScriptLoad);
    document.head.appendChild(script);
  });
  const loadStyleSheet = async () => {
    const link = document.createElement("link");
    link.href = "/lib/get3d/css/Viewer.css";
    // link.href = '/lib/get3d/css/Editor.css';
    link.rel = "stylesheet";
    document.querySelector("#model_view")?.appendChild(link);
    const link1 = document.createElement("link");
    link1.href = "/lib/get3d/css/index.css";
    link1.rel = "stylesheet";
    document.querySelector("#model_view")?.appendChild(link1);
  };
  const loadSuccess = ref(false);
  const handleScriptLoad = () => {
    // let url = "https://gptp.cug.edu.cn/real3d/2024-06-09/obj/1717864832363/OBJ/model.json?random+0.42230278754815664";
    window.modelViewer = new Get3DLib.Get3DViewer(document.getElementById("root"), {
      // const viewer = new Get3DLib.Get3DEditor(document.getElementById("root"), {
      uri: props.url,
      // loading: true,
      onSuccess: (json: any) => {
        // console.log(viewer.hasMeasurements());
        loadSuccess.value = true;
      },
      onProgress: (p: any) => {
        console.log(p);
      },
    });
  };
</script>

<style lang="scss">
  .model_edit {
    position: absolute;
    left: 0px;
    top: 0px;
    width: 100%;
    height: 100%;
    background-color: rgba(85, 85, 85, 1);

    .model_view_container {
      float: left;
      width: 100%;
      height: 100%;
      top: 0px;
      position: relative;

      svg {
        width: 35px;
        height: 35px;
        color: #78a9ff;
        animation: fadenum 2s linear infinite;
        position: absolute;
        left: 50%;
        top: 50%;
      }

      @keyframes fadenum {
        0% {
          transform: rotate(0deg);
        }

        100% {
          transform: rotate(360deg);
        }
      }
    }
  }
</style>
