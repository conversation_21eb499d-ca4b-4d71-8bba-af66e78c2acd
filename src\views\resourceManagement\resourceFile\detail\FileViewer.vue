<template>
	<div style="position: relative; width: 100%; height: 100%; overflow: hidden">
		<div class="view-box">
			<el-image
				v-if="viewRecord.type == 6"
				style="width: 100%; height: 100%"
				:src="viewRecord.url"
				:zoom-rate="1.2"
				:max-scale="7"
				:min-scale="0.2"
				:preview-src-list="[viewRecord.url]"
				fit="contain"
			/>
			<div v-else-if="viewRecord.type == 5" style="width: 70%">
				<AudioViewer :audioSrc="viewRecord.url" />
			</div>
			<!-- <audio v-else-if="viewRecord.type == 5" autoplay controls loop preload="auto" :src="viewRecord.url"  /> -->
			<video
				v-else-if="viewRecord.type == 13"
				ref="videoRef"
				oncontextmenu="return false"
				controlslist="nodownload"
				autoplay
				controls
				loop
				preload="auto"
				:src="viewRecord.url"
				style="width: auto; height: auto; object-fit: contain; max-height: 100%; max-width: 100%;"
			/>
			<Das3d
				v-else-if="viewRecord.type == 1"
				type="model"
				style="width: 100%; height: 100%"
				:url="viewRecord.url"
				:isEditor="false"
				mapContainerId="mtMap"
			/>
			<ModelViewer v-else-if="viewRecord.type == 11" style="width: 100%; height: 100%" :url="viewRecord.url" />
			<PhotoSphereViewer v-else-if="viewRecord.type == 4" ref="psvRef" :url="viewRecord.url" />
			<Das3d
				v-else-if="[2, 3].includes(viewRecord.type)"
				type="tms"
				style="width: 100%; height: 100%"
				:url="viewRecord.url"
				:properties="viewRecord.properties"
				:isEditor="false"
				mapContainerId="mtMap"
			/>
			<FileViewer v-else-if="viewRecord.type == 7" :url="viewRecord.url" />
			<el-empty v-else description="当前文件暂时无法预览" />
		</div>
	</div>
</template>

<script setup lang="ts">
import { useMessage } from '/@/hooks/message';
const PhotoSphereViewer = defineAsyncComponent(() => import('/@/components/PhotoSphereViewer/index.vue'));
const ModelViewer = defineAsyncComponent(() => import('/@/components/ModelViewer/index.vue'));
const FileViewer = defineAsyncComponent(() => import('/@/components/FileViewer/index.vue'));
const AudioViewer = defineAsyncComponent(() => import('/@/components/AudioBar/index.vue'));

const viewRecord: any = ref({});
const psvRef: any = ref();
const videoRef: any = ref();
const props = defineProps({
    record: {
        type: Object,
        default: null,
    }
})

onMounted(() => {
    initData();
});
const initData = () => {
    let record = props.record
	record.type = Number(record.type);
	if (record.type == 11) {
		// record.url = record.url.replaceAll(record.url.split('/').pop(), 'model.json') + '?random+' + Math.random();
        let url = record.url.split('/');
        url.pop();
        record.url = url.join('/') + '/model.json?random+' + Math.random();
	} else if ([2, 3].includes(record.type)) {
		if (!(record.properties?.length > 0)) {
			useMessage().wraning('当前影像数据无四至信息，请检查数据');
			return;
		}
		record.properties = JSON.parse(record.properties);
	}
	viewRecord.value = record;
	nextTick(() => {
		disalbedKey();
	});
};

const disalbedKey = () => {
	let el = document.querySelector('.view-box');
	el &&
		(el.oncontextmenu = function () {
			return false;
		});
};

</script>
<style scoped lang="scss">
.view-box {
	width: 100%;
	height: 100%;
	overflow: hidden;
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
}
.mt-full {
	position: absolute;
	right: 0px;
	top: 0px;
	width: 100%;
	height: 100% !important;
	::v-deep(.mt-control) {
		right: 30px !important;
		top: 55px !important;
	}
}
.mt-btn {
	position: absolute;
	right: 80px;
	top: 0px;
	font-size: 20px;
	cursor: pointer;
	&:hover {
		color: #6b6b6b;
	}
}
audio::-webkit-media-controls-enclosure {
	background-color: #D9D9D933;
	border-radius: 6px;
}
</style>
