import request from '/@/utils/request'
export function getRegionAnalysis(query?: Object) {
    return request({
        url: '/report/water/level/regionAnalysis',
        method: 'get',
        params: query
    })
}
export function situationHourAnalysis(query?: Object) {
    return request({
        url: '/report/water/level/situationHourAnalysis',
        method: 'get',
        params: query
    })
}
export function exportRegionAnalysis(query?: Object) {
    return request({
        url: '/report/water/level/exportRegionAnalysis',
        method: 'get',
        params: query
    })
}
export function qualityDataList(query?: Object) {
    return request({
        url: '/report/water/quality/dataList',
        method: 'get',
        params: query
    })
}

export function dissolvedOxygen(query?: Object) {
    return request({
        url: '/report/water/quality/dissolvedOxygen',
        method: 'get',
        params: query
    })
}
export function qualityTur(query?: Object) {
    return request({
        url: '/report/water/quality/tur',
        method: 'get',
        params: query
    })
}
export function qualityCt(query?: Object) {
    return request({
        url: '/report/water/quality/ct',
        method: 'get',
        params: query
    })
}
export function qualityPh(query?: Object) {
    return request({
        url: '/report/water/quality/ph',
        method: 'get',
        params: query
    })
}
export function qualityTemper(query?: Object) {
    return request({
        url: '/report/water/quality/temper',
        method: 'get',
        params: query
    })
}

