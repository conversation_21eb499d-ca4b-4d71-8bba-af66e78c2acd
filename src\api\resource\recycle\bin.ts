import request from "/@/utils/request";

// 分页查询
export function fetchList(query?: Object) {
  return request({
    url: "/datacenter/recycle/bin/page",
    method: "get",
    params: query,
  });
}
// 批量操作回收站(1:删除 2：还原)
export function updateObj(obj?: Object) {
  return request({
    url: "/datacenter/recycle/bin/operateFile",
    method: "post",
    data: obj,
  });
}
// 获取资源详情
export function getObj(id?: Object) {
  return request({
    url: "/datacenter/recycle/bin/info",
    method: "get",
    params: id,
  });
}

// 获取资源详情
export function getRecycleObj(id?: Object) {
  return request({
    url: "/datacenter/learning/material/detail",
    method: "get",
    params: id,
  });
}
