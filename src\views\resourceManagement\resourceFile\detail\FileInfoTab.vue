<template>
	<div class="file-detail-tab">
		<el-empty class="no-file-data" v-if="!props.record?.id" description="未查询到该文件详情信息" />
		<template v-else>
			<div class="file-detail-left">
				<div class="file-info-title flex items-center">
					<img src="/@/assets/img/archives/icon_down_circle.png" />
					<span>文件信息</span>
				</div>
				<div class="info-list">
					<div class="info-item" v-for="item in fileInfoData" :key="item.key">
						<div class="info-label">{{ item.label }}</div>
						<div class="info-value" v-if="item.type === 'img'">
							<el-image v-if="item.value" class="info-cover" :src="item.value" fit="cover" :preview-src-list="[item.value]" />
							<span v-else>暂无封面</span>
						</div>
						<div class="info-value flex flex-wrap" v-else-if="item.type === 'tags'">
							<div v-for="tag in item.value" :key="tag" class="tag-item">#{{ tag }}</div>
						</div>
						<div class="info-value" v-else>{{ item.value }}</div>
					</div>
				</div>
				<div class="file-info-title flex items-center">
					<img src="/@/assets/img/archives/icon_down_circle.png" />
					<span>管理信息</span>
				</div>
				<div class="info-list">
					<div class="info-item" v-for="item in adminInfoData" :key="item.key">
						<div class="info-label">{{ item.label }}</div>
						<div class="info-value">{{ item.value }}</div>
					</div>
				</div>
			</div>
			<div class="file-detail-right">
				<FileViewer v-if="props.record?.id" :record="props.record" />
			</div>
		</template>
	</div>
</template>

<script setup lang="ts">
import { onMounted, computed } from 'vue';
import { getFileType } from '/@/config/resourceConfig';
import FileViewer from '/@/views/resourceManagement/resourceFile/detail/FileViewer.vue';
const props = defineProps({
	record: {
		type: Object,
		default: null,
	},
});

const fileInfoData = computed(() => {
	return [
		{ label: '资源封面', value: props.record.cover, key: 'cover', type: 'img' },
		{ label: '文件名称', value: props.record.name, key: 'name', type: 'text' },
		{ label: '文件类型', value: getFileType(props.record.type)?.name || '-', key: 'type', type: 'text' },
		{ label: '关联资源', value: props.record.catalogNo, key: 'catalogNo', type: 'text' },
		{ label: '文件密级', value: props.record.securityName, key: 'securityName', type: 'text' },
		{ label: '文件格式', value: props.record.format, key: 'format', type: 'text' },
		{ label: '文件大小', value: ((props.record.totalSize || 0) / 1024 / 1024).toFixed(2) + 'MB', key: 'totalSize', type: 'text' },
		{ label: '文件描述', value: props.record.desc, key: 'desc', type: 'text' },
		{ label: '文件标签', value: props.record.labels ? props.record.labels.split(/[,，]/) : [], key: 'labels', type: 'tags' },
	];
});
const adminInfoData = computed(() => {
	return [
		{ label: '来源单位', value: props.record.deptName, key: 'deptName', type: 'text' },
		{ label: '上传人', value: props.record.createUser, key: 'createUser', type: 'text' },
		{ label: '上传时间', value: props.record.createTime, key: 'createTime', type: 'text' },
		{ label: '更新人', value: props.record.updateUser, key: 'updateUser', type: 'text' },
		{ label: '更新时间', value: props.record.updateTime, key: 'updateTime', type: 'text' },
	];
});

onMounted(() => {});
</script>

<style lang="scss" scoped>
.file-detail-tab {
	padding: 20px;
	display: flex;
	height: 100%;
	background: #fff;
	border-radius: 5px;
	.no-file-data {
		width: 100%;
	}
	.file-detail-left {
		width: 314px;
		height: 100%;
		overflow-y: auto;
		padding: 12px;
		background: #f3f3f3;
		border-radius: 5px;
		.file-info-title {
			font-size: 16px;
			img {
				width: 16px;
				height: 16px;
				margin-right: 8px;
				margin-top: -2px;
			}
		}
		.info-list {
			padding-left: 25px;
		}
		.info-item {
			display: flex;
			align-items: start;
			margin: 15px 0;
			font-size: 14px;
			.info-label {
				width: 90px;
				color: #00000066;
			}
			.info-value {
				flex: 1;
				.tag-item {
					color: #366ef4;
					margin-right: 10px;
				}
			}
			.info-cover {
				width: 50px;
				height: 44px;
				border-radius: 4px;
			}
		}
	}
	.file-detail-right {
		flex: 1;
		height: 100%;
		margin-left: 20px;
		border-radius: 5px;
	}
}
</style>
