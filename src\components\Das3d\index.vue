<template>
  <div :id="props.mapContainerId" class="cesiumContainer">
    <div v-if="props.type == 'model'" class="mt-control">
      <el-tooltip class="item" effect="dark" content="视角锁定" placement="left">
        <el-image v-show="isLookAt" style="width: 100%; height: 100%;" :src="img_lock_open" fit="cover" @click="goCameraLookAt(true)" />
      </el-tooltip>
      <el-tooltip class="item" effect="dark" content="视角解锁" placement="left">
        <el-image v-show="!isLookAt" style="width: 100%; height: 100%;" :src="img_lock_close" fit="cover" @click="goCameraLookAt(false)" />
      </el-tooltip>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useSysStore } from "/@/stores/sys";
  import { onMounted, markRaw, onBeforeUnmount, watch } from "vue";
  import img_marker from "/@/assets/img/custom/marker.png";
  import img_lock_open from "/@/assets/img/custom/lock-open.png";
  import img_lock_close from "/@/assets/img/custom/lock-close.png";

  // interface mapType {
  //   type: string;
  // }

  let drawControl: any;
  const sysStore = useSysStore();

  const emit = defineEmits(["mapClick"]);

  const props = defineProps({
    point: {
      type: Object,
      default: null,
    },
    type: {
      type: String,
      default: "point",
    },
    isEditor: {
      type: Boolean,
      default: true,
    },
    url: {
      type: String,
      default: "",
    },
    properties: {
      type: Object,
      default: {},
    },
    mapContainerId: {
      type: String,
      default: "cesiumContainer",
    },
  });
  // const props = withDefaults(defineProps<mapType>(), {
  //   type: "point",
  // });
  const coord: any = ref([]);
  const isLookAt: any = ref(false);
  const lookAtBoundingSphere: any = ref();
  watch(
    () => props.point,
    (val) => {
      if (val) {
        coord.value = [val.x, val.y];
        val.z > 0 && coord.value.push(val.z);
        sysStore.cesiumViewer &&
          sysStore.cesiumViewer.flyTo(sysStore.cesiumViewer.entities, {
            offset: new Cesium.HeadingPitchRange(sysStore.cesiumViewer.camera.heading, sysStore.cesiumViewer.camera.pitch, sysStore.cesiumViewer.scene.camera.positionCartographic.height),
            duration: 1,
          });
      }
    },
    {
      immediate: true,
      deep: true,
    }
  );
  onMounted(() => {
    const createMapData = {
      id: props.mapContainerId,
      url: "/config/config.json",
      backgroundColor: "rgba(0,0,0,0)",
      style: {
        skyBox: false,
        moon: false,
        sun: false,
        atmosphere: true,
      },
      orderIndependentTranslucency: false,
      contextOptions: {
        webgl: {
          alpha: props.type == "model" ? true : false,
          preserveDrawingBuffer: true, //允许canvas 截图
        },
      },
      success: (o) => {
        const rawViewer = markRaw(o);

        sysStore.setCesiumViewer(rawViewer);
        rawViewer.scene.globe.depthTestAgainstTerrain = true;
        switch (props.type) {
          case "point":
            initMarker();
            if (props.point) {
              coord.value = [props.point.x, props.point.y];
              props.point.z > 0 && coord.value.push(props.point.z);
              sysStore.cesiumViewer &&
                sysStore.cesiumViewer.flyTo(sysStore.cesiumViewer.entities, {
                  offset: new Cesium.HeadingPitchRange(sysStore.cesiumViewer.camera.heading, sysStore.cesiumViewer.camera.pitch, sysStore.cesiumViewer.scene.camera.positionCartographic.height),
                  duration: 1,
                });
            }
            if (props.isEditor) {
              onPickCoords();
              initDrawControl();
            }
            break;
          case "model":
            loadModel();
            break;
          case "tms":
            loadTMS();
            break;
        }
      },
    };
    das3d.createMap(createMapData);
  });

  onUnmounted(() => {
    const viewer = sysStore.cesiumViewer;

    if (drawControl) {
      drawControl.destroy();
      sysStore.setDrawTool(null);
    }

    if (viewer) {
      viewer.das?.off(das3d.event?.click);
      viewer.destroy();
      sysStore.setCesiumViewer(null);
    }
  });

  // 创建地图监听鼠标点击事件，拾取坐标
  const onPickCoords = () => {
    const viewer = sysStore.cesiumViewer;

    viewer.das.on(das3d.event.click, (event: any) => {
      const cartesian = das3d.point.getCurrentMousePosition(viewer.scene, event.position);

      if (cartesian) {
        const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
        const jd = Number(Cesium.Math.toDegrees(cartographic.longitude).toFixed(6));
        const wd = Number(Cesium.Math.toDegrees(cartographic.latitude).toFixed(6));
        const height = Number(cartographic.height.toFixed(1));
        if (props.isEditor) {
          coord.value = [jd, wd, height];
          emit("mapClick", coord.value);
        }
      }
    });
  };

  // 将当前拾取的坐标点绘制到地图上
  const initMarker = () => {
    const viewer = sysStore.cesiumViewer;
    viewer.entities.removeAll();
    viewer.entities.add({
      name: "坐标拾取",
      position: new Cesium.CallbackProperty(function () {
        let position = null;
        if (coord.value.length > 0) {
          if (coord.value[2] > 0) {
            position = Cesium.Cartesian3.fromDegrees(coord.value[0], coord.value[1], coord.value[2]);
          } else {
            position = Cesium.Cartesian3.fromDegrees(coord.value[0], coord.value[1]);
          }
        }
        return position;
      }, false),
      //   point: {
      //     color: Cesium.Color.fromCssColorString("#3388ff"),
      //     pixelSize: 10,
      //     outlineColor: Cesium.Color.fromCssColorString("#ffffff"),
      //     outlineWidth: 2,
      //     disableDepthTestDistance: Number.POSITIVE_INFINITY, // 一直显示，不被地形等遮挡
      //   },
      billboard: {
        image: img_marker,
        scale: 0.2,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        scaleByDistance: new Cesium.NearFarScalar(1.5e2, 1.0, 8.0e6, 0.2),
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
      },
      // label: {
      //   show: !!props.point,
      //   pixelOffset: new Cesium.Cartesian2(0, 30),
      //   showBackground: true,
      //   backgroundColor: Cesium.Color.fromCssColorString("#fff").withAlpha(0.5),
      //   backgroundPadding: new Cesium.Cartesian2(10, 10),
      //   text: [val.x.toFixed(5), val.y.toFixed(5), val.z].join(","),
      //   font: "normal bolder 14px PingFang SC Regular",
      //   style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      //   fillColor: new Cesium.Color.fromCssColorString("#000"),
      //   outlineColor: new Cesium.Color.fromCssColorString("#fff").withAlpha(0.5),
      //   outlineWidth: 1,
      //   horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
      //   verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      //   heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      //   // distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0.0, 20505),
      //   disableDepthTestDistance: Number.POSITIVE_INFINITY,
      // },
    });
  };

  // 初始化绘制工具
  const initDrawControl = () => {
    const viewer = sysStore.cesiumViewer;

    if (drawControl) {
      return false;
    }

    drawControl = new das3d.Draw({
      viewer: viewer,
      hasEdit: true,
    });

    sysStore.setDrawTool(markRaw(drawControl));
  };

  const loadModel = () => {
    const viewer = sysStore.cesiumViewer;
    let mtlayer = new das3d.layer.createLayer(viewer, {
      type: "3dtiles",
      url: props.url,
      visible: true,
      maximumScreenSpaceError: 16, //默认16，值越大，能让最终成像变模糊
      dynamicScreenSpaceError: true,
      dynamicScreenSpaceErrorDensity: 0.00278,
      dynamicScreenSpaceErrorFactor: 4.0,
      dynamicScreenSpaceErrorFactorHeightFalloff: 0.25,
      maximumMemoryUsage: 128, // 默认512MB，内存分配变小有利于倾斜摄影数据回收，提升性能体验
      skipLevelOfDetail: true,
      immediatelyLoadDesiredLevelOfDetail: true,
      cullRequestsWhileMovingMultiplier: 100, // 默认60，值越小能够更快的剔除请求的乘数
      loadSiblings: true, // 如果为true则不会在已加载完概况房屋后，自动从中心开始超清化房屋
      flyTo: false,
    });
    // var tileset = viewer.scene.primitives.add(
    //   new Cesium.Cesium3DTileset({
    //     url: props.url,
    //   })
    // );
    mtlayer.tileset.readyPromise.then(function (argument: any) {
      viewer.scene.globe.show = false;
      lookAtBoundingSphere.value = argument.boundingSphere;
      goCameraLookAt(true);
      // let entity = viewer.entities.add({
      //   position: center,
      //   point: {
      //     color: Cesium.Color.fromCssColorString("#3388ff"),
      //     pixelSize: 10,
      //     outlineColor: Cesium.Color.fromCssColorString("#ffffff"),
      //     outlineWidth: 2,
      //     disableDepthTestDistance: Number.POSITIVE_INFINITY, // 一直显示，不被地形等遮挡
      //   },
      // });
      // viewer.trackedEntity = entity;
      // viewer.flyTo(entity);
    });
  };
  const loadTMS = () => {
    const viewer = sysStore.cesiumViewer;
    // window.myviewer = viewer;
    // viewer.imageryLayers.removeAll();
    let arr = props.properties.bbox?.split(",") || [];
    let imageryProvider = new Cesium.UrlTemplateImageryProvider({
      url: props.url + "/{z}/{x}/{reverseY}.png",
      // url: "http://10.100.111.18:8600/geoserver/gwc/service/tms/1.0.0/test123:1823651512731873280@EPSG:4326@png/{z}/{x}/{reverseY}.png",
      tilingScheme: new Cesium.GeographicTilingScheme(),
      minimumLevel: props.properties.minimumLevel || 0,
      maximumLevel: props.properties.maximumLevel || 18,
      // rectangle: Cesium.Rectangle.fromDegrees(112.8515625,21.616579337,113.203125,21.943045533),
      rectangle: Cesium.Rectangle.fromDegrees(arr[0], arr[1], arr[2], arr[3]),
    });
    let imageryLayer = viewer.imageryLayers.addImageryProvider(imageryProvider);
    viewer.flyTo(imageryLayer);
    // viewer.camera.flyTo({
    //   destination: Cesium.Cartesian3.fromDegrees(115.37274190591727, 22.765137949220676, 1000),
    // });
  };
  const goCameraLookAt = (islock: any) => {
    const viewer = sysStore.cesiumViewer;
    if (islock) {
      let center = lookAtBoundingSphere.value.center;
      let radius = lookAtBoundingSphere.value.radius;
      const transform = Cesium.Transforms.eastNorthUpToFixedFrame(center);
      const heading = Cesium.Math.toRadians(0.0);
      const pitch = Cesium.Math.toRadians(-60.0);
      const range = radius + 100;
      viewer.camera.lookAtTransform(transform, new Cesium.HeadingPitchRange(heading, pitch, range));
      isLookAt.value = false;
    } else {
      viewer.camera.lookAtTransform(Cesium.Matrix4.IDENTITY);
      isLookAt.value = true;
    }
  };
</script>

<style lang="scss" scoped>
  .cesiumContainer {
    position: relative;
    // background-image: url(/@/assets/img/custom/backGroundImg.jpg);
    background-image: url(/@/assets/img/custom/20211124161941.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .mt-control {
      position: absolute;
      width: 40px;
      height: 40px;
      right: 10px;
      top: 5px;
      z-index: 999;
      cursor: pointer;
      &:hover {
        opacity: 0.7;
      }
    }
  }
</style>
