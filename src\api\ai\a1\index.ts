import request from "/@/utils/request"

// 分页查询
export function fetchList(query?: Object) {
  return request({
    url: '/ai/wbPointsMonitPredict/page',
    method: 'get',
    params: query
  })
}
// 查询未来一天预测点位统计数据
export function getNextDayStatData() {
  return request({
    url: '/ai/wbPointsMonitPredict/getNextDayStatData',
    method: 'get',
  })
}
// 详情查询监测趋势
export function getObj(id?: string) {
  return request({
    url: '/ai/wbPointsMonitPredict/getMonitTendency',
    method: 'get',
    params: {pointId:id}
  })
}

export function delObjs(ids?: Object) {
  return request({
    url: '/business/yjEventInfo',
    method: 'delete',
    data: ids
  })
}

export function putObj(obj?: Object) {
  return request({
    url: '/business/yjEventInfo',
    method: 'put',
    data: obj
  })
}

export function cancelObjs(ids?: Array<string>) {
  return request({
    url: '/business/yjEventInfo/updateByIds',
    method: 'post',
    data: ids
  })
}