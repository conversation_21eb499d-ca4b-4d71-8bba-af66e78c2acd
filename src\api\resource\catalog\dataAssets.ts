import request from "/@/utils/request";

// 新增数据项
export function addMetaData(data?: Object) {
  return request({
    url: "/datacenter/dataAssets/addMetaData",
    method: "post",
    data: data,
  });
}

// 数据项列表
export function pageMetaData(data?: Object) {
  return request({
    url: "/datacenter/dataAssets/pageMetaData",
    method: "post",
    data: data,
  });
}

// 编辑数据项
export function editMetaData(data?: Object) {
  return request({
    url: "/datacenter/dataAssets/editMetaData",
    method: "post",
    data: data,
  });
}
// 发布/启用
export function pubAndEnableMetaData(data?: Object) {
  return request({
    url: "/datacenter/dataAssets/pubAndEnableMetaData",
    method: "post",
    data: data,
  });
}

// 数据项统计
export function countMetaData(data?: Object) {
  return request({
    url: "/datacenter/dataAssets/countMetaData",
    method: "post",
    data: data,
  });
}