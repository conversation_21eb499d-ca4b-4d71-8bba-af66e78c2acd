<template>
  <div class="file-resource-container">
    <!-- 查看模式 -->
    <div v-if="viewMode === 'view'" class="file-list-view">
      <el-table :data="fileList" border stripe class="file-table" header-cell-class-name="custom-table-header"
        v-loading="loading" element-loading-text="加载中...">
        <el-table-column prop="assetsNum" label="文件编码" min-width="100" show-overflow-tooltip />
        <el-table-column prop="name" label="文件名称" min-width="180" show-overflow-tooltip />
        <el-table-column label="文件类型" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="getFileTypeTag(getFileTypeLabel(row.type))" size="small">
              {{ getFileTypeLabel(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="format" label="文件格式" min-width="100" show-overflow-tooltip />
        <el-table-column prop="totalSize" label="文件大小" width="120" align="center">
          <template #default="{ row }">
            {{ formatFileSize(row.totalSize) }}
          </template>
        </el-table-column>
        <el-table-column prop="createUser" label="创建人" min-width="100" show-overflow-tooltip />
        <el-table-column prop="createTime" label="创建时间" width="150" align="center">
          <template #default="{ row }">
            {{ formatTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="描述" min-width="150" show-overflow-tooltip />
        <el-table-column label="操作" width="100" align="center" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link size="small" @click="viewFile(row)">
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-empty v-if="!loading && fileList.length === 0" description="暂无文件资源" />

      <!-- 分页组件 -->
      <div v-if="pagination.total > 0" class="pagination-container">
        <el-pagination v-model:current-page="pagination.current" v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]" :total="pagination.total" layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </div>

    <!-- 编辑模式 -->
    <div v-else class="file-list-edit">
      <el-table :data="fileList" border stripe class="file-table" header-cell-class-name="custom-table-header"
        v-loading="loading" element-loading-text="加载中...">
        <el-table-column prop="assetsNum" label="文件编码" min-width="100" show-overflow-tooltip />
        <el-table-column prop="name" label="文件名称" min-width="180" show-overflow-tooltip />
        <el-table-column label="文件类型" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="getFileTypeTag(getFileTypeLabel(row.type))" size="small">
              {{ getFileTypeLabel(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="format" label="文件格式" min-width="100" show-overflow-tooltip />
        <el-table-column prop="totalSize" label="文件大小" width="120" align="center">
          <template #default="{ row }">
            {{ formatFileSize(row.totalSize) }}
          </template>
        </el-table-column>
        <el-table-column prop="createUser" label="创建人" min-width="100" show-overflow-tooltip />
        <el-table-column prop="createTime" label="创建时间" width="150" align="center">
          <template #default="{ row }">
            {{ formatTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="描述" min-width="150" show-overflow-tooltip />
        <el-table-column label="操作" width="120" align="center" fixed="right">
          <template #default="{ row, $index }">
            <el-button type="primary" link size="small" @click="viewFile(row)">
              查看
            </el-button>
            <el-button type="danger" link size="small" @click="removeFile(row)">
              移除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-empty v-if="!loading && fileList.length === 0" description="暂无文件资源，点击上方按钮新增" />

      <!-- 分页组件 -->
      <div v-if="pagination.total > 0" class="pagination-container">
        <el-pagination v-model:current-page="pagination.current" v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]" :total="pagination.total" layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </div>

    <!-- 新增/编辑文件弹窗 -->
    <AddFileDialog v-model:visible="showAddDialog" :edit-data="props.resourceData" @confirm="handleFileConfirm" />

    <!-- 文件详情弹窗 -->
    <el-dialog v-model="showFileDetailDialog" :title="null" width="1200px" height="700px" :close-on-click-modal="false"
      :close-on-press-escape="false" :show-close="false" class="file-detail-dialog"
      :style="{ '--el-dialog-padding-primary': '0px' }">
      <FileDetail ref="fileDetailRef" v-if="showFileDetailDialog" :type="1" @close="handleCloseFileDetail" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, defineAsyncComponent, onMounted, watch } from 'vue';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { fetchList } from "/@/api/resource/filelist/file";
import { getFileType } from '/@/config/resourceConfig';
import { delResourceOfFile } from '/@/api/resource/data/resource';

const AddFileDialog = defineAsyncComponent(() => import('./AddFileDialog.vue'));
const FileDetail = defineAsyncComponent(() => import('/@/views/resourceManagement/resourceFile/detail/FileDetail.vue'));

interface Props {
  resourceData: any;
  viewMode: 'view' | 'edit';
}

const props = withDefaults(defineProps<Props>(), {
  resourceData: null,
  viewMode: 'view',
});

const emit = defineEmits<{
  save: [data: any[]];
}>();

// 响应式数据
const fileList = ref<any[]>([]);
const showAddDialog = ref(false);
const editFileData = ref<any>(null);
const loading = ref(false);
const showFileDetailDialog = ref(false);
const fileDetailRef = ref();

// 分页相关数据
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0,
});

// 获取文件列表数据
const getFileList = async () => {
  if (!props.resourceData?.id || !props.resourceData?.tableName) {
    return;
  }

  loading.value = true;
  try {
    const params = {
      resourceId: props.resourceData.id,
      tabName: props.resourceData.tableName,
      current: pagination.current,
      size: pagination.size,
    };

    const response = await fetchList(params);

    if ((response.code === 0 || response.code === 200) && response.data) {
      fileList.value = response.data.records || [];
      pagination.total = response.data.total || 0;
      pagination.current = response.data.current || 1;
      pagination.size = response.data.size || 10;
    } else {
      fileList.value = [];
      pagination.total = 0;
      useMessage().error(response.message || '获取文件列表失败');
    }
  } catch (error) {
    fileList.value = [];
    pagination.total = 0;
    useMessage().error('获取文件列表失败');
  } finally {
    loading.value = false;
  }
};

// 分页事件处理
const handleCurrentChange = (page: number) => {
  pagination.current = page;
  getFileList();
};

const handleSizeChange = (size: number) => {
  pagination.size = size;
  pagination.current = 1; // 重置到第一页
  getFileList();
};

// 获取文件类型标签
const getFileTypeLabel = (typeId: string) => {
  if (!typeId) return '其他文件';

  const fileTypeConfig = getFileType(typeId);
  return fileTypeConfig ? fileTypeConfig.name : '其他文件';
};

// 文件类型标签颜色
const getFileTypeTag = (typeName: string) => {
  const typeMap: Record<string, string> = {
    '图片': 'success',
    '音频': 'info',
    '视频': 'warning',
    '文档': 'primary',
    '二维GIS-影像': 'danger',
    '二维GIS-矢量': 'warning',
    '三维模型-场景类': 'success',
    '三维模型-物件类': 'success',
    '全景图': 'info',
  };
  return typeMap[typeName] || 'info';
};

// 格式化文件大小
const formatFileSize = (size: number) => {
  if (!size) return '--';
  const units = ['B', 'KB', 'MB', 'GB'];
  let index = 0;
  let fileSize = size;

  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024;
    index++;
  }

  return `${fileSize.toFixed(1)} ${units[index]}`;
};

// 格式化时间
const formatTime = (time: string) => {
  if (!time) return '--';
  return new Date(time).toLocaleString('zh-CN');
};

// 查看文件
const viewFile = (file: any) => {
  // 打开文件详情弹窗
  showFileDetailDialog.value = true;
  // 等待弹窗渲染完成后初始化文件信息
  setTimeout(() => {
    if (fileDetailRef.value) {
      fileDetailRef.value.initInfo(file);
    }
  }, 100);
};

// 移除文件
const removeFile = async (row: any) => {
  await useMessageBox().confirm('确认移除该文件关联吗？');
  try {
    // 构建接口参数
    const requestData = {
      dataId: props.resourceData.id || '',
      tabName: props.resourceData.tableName,
      fileId: row.id,
    };
    // 调用接口
    const response = await delResourceOfFile(requestData);
    if ((response.code === 0 || response.code === 200)) {
      useMessage().success('已移除文件关联');
      getFileList();
    } else {
      useMessage().error(response.message || '移除文件关联失败');
    }
  } catch (error) {
    useMessage().error('移除文件关联失败');
  }
};

// 关闭文件详情弹窗
const handleCloseFileDetail = () => {
  showFileDetailDialog.value = false;
};

// 处理文件确认
const handleFileConfirm = () => {
  // 文件关联成功后，重新获取文件列表
  getFileList();
  editFileData.value = null;
  showAddDialog.value = false;
};

// 获取数据
const getData = () => {
  return fileList.value;
};

// 验证数据
const validate = async () => {
  return true;
};

// 打开新增文件弹窗
const openAddDialog = () => {
  editFileData.value = null;
  showAddDialog.value = true;
};

// 监听 resourceData 变化
watch(() => props.resourceData, (newVal) => {
  if (newVal?.id && newVal?.tableName) {
    pagination.current = 1; // 重置到第一页
    getFileList();
  }
}, { immediate: true });

// 组件挂载时获取数据
onMounted(() => {
  if (props.resourceData?.id && props.resourceData?.tableName) {
    getFileList();
  }
});

defineExpose({
  getData,
  validate,
  openAddDialog,
});
</script>

<style scoped lang="scss">
.file-resource-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.file-list-view,
.file-list-edit {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 4px;

  .header-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .add-btn {
    font-size: 14px;
  }
}

.file-table {
  flex: 1;

  :deep(.el-table__header-wrapper) {
    .el-table__header {
      th {
        background-color: #fafafa;
        color: #303133;
        font-weight: 600;
      }
    }
  }

  // 自定义表头样式
  :deep(.custom-table-header) {
    background-color: var(--next-bg-main-color, #f5f7fa);
    color: rgba(0, 0, 0, 0.4) !important;
    font-weight: 400;
    font-size: 14px;
    height: 50px;
  }
}



:deep(.el-button + .el-button) {
  margin-left: 8px;
}

// 分页组件样式
.pagination-container {
  display: flex;
  justify-content: right;
  align-items: center;
  padding: 10px 10px;
  margin-top: 10px;
  border-top: 1px solid #e4e7ed;
  background-color: #fafafa;

  :deep(.el-pagination) {
    .el-pagination__total {
      color: #606266;
      font-weight: 400;
    }

    .el-pagination__sizes {
      .el-select {
        .el-input {
          .el-input__wrapper {
            box-shadow: 0 1px 0 0 #dcdfe6 inset, 0 -1px 0 0 #dcdfe6 inset, -1px 0 0 0 #dcdfe6 inset, 1px 0 0 0 #dcdfe6 inset;
          }
        }
      }
    }

    .el-pager {
      li {
        &.is-active {
          background-color: var(--el-color-primary);
          color: #fff;
        }

        &:hover {
          color: var(--el-color-primary);
        }
      }
    }

    .btn-prev,
    .btn-next {
      &:hover {
        color: var(--el-color-primary);
      }

      &.is-disabled {
        color: #c0c4cc;
      }
    }
  }
}

// 文件详情弹窗样式
:deep(.file-detail-dialog.el-dialog__wrapper) {
  .el-dialog {
    border-radius: 8px !important;

    .el-dialog__header {
      display: none !important; // 隐藏头部
      height: 0 !important; // 移除头部占位高度
      margin: 0 !important; // 移除头部边距
      padding: 0 !important; // 移除头部内边距
    }

    .el-dialog__body {
      padding: 0 !important; // 强制移除body的padding
      margin: 0 !important; // 移除body的margin
      height: 700px !important; // 使用完整高度
      overflow: hidden !important;
    }
  }
}

// 备用样式，直接针对全局dialog
:deep(.el-dialog__wrapper.file-detail-dialog) {
  .el-dialog__header {
    display: none !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .el-dialog__body {
    padding: 0 !important;
    margin: 0 !important;
    height: 700px !important;
    overflow: hidden !important;
  }
}

// 最强优先级样式
.file-detail-dialog {
  :deep(.el-dialog__header) {
    display: none !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  :deep(.el-dialog__body) {
    padding: 0 !important;
    margin: 0 !important;
    height: 700px !important;
    overflow: hidden !important;
  }
}
</style>

<!-- 全局样式，确保生效 -->
<style lang="scss">
.file-detail-dialog .el-dialog__header {
  display: none !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

.file-detail-dialog .el-dialog__body {
  padding: 5px !important;
  margin: 0 !important;
  height: 800px !important;
  overflow: hidden !important;
}
</style>
