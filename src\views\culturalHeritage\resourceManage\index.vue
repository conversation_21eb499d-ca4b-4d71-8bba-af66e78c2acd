<template>
  <div style="position: relative; width: 100%; height: 100%; left: 0px; right: 0px">
    <!-- 主列表视图 -->
    <div class="mt-box layout-padding" v-show="!showDetailView">
      <div class="left-tree-panel">
        <div class="layout-model-title">资源目录</div>
        <div class="tree-container">
          <el-scrollbar height="100%">
            <query-tree ref="queryTreeRef" placeholder="目录搜索" :props="{
              label: 'catalogName',
              children: 'childCatalogs',
              value: 'catalogNo',
            }" :query="treeData.queryList" :show-expand="true" @node-click="handleNodeClick"
              @treeDataLoaded="handleTreeDataLoaded">
              <template #default="{ node, data }">
                <el-tooltip v-if="data.isLock" class="item" effect="dark" content="无数据权限" placement="right-start">
                  <span>{{ node.label }}
                    <SvgIcon name="ele-Lock" />
                  </span>
                </el-tooltip>
                <span v-if="!data.isLock">{{ node.label }}</span>
              </template>
            </query-tree>
          </el-scrollbar>
        </div>
      </div>
      <div class="right-content-panel">
        <div class="toolbar-section">
          <el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList_custom"
            class="w100">
            <el-row :gutter="24">
              <el-col :span="16">
                <el-button @click="onOperation('DoAdd')" type="primary" :icon="Plus">新增</el-button>
                <el-button @click="onOperation('DoUpload')" type="primary" class="">批量导入</el-button>
                <el-button @click="onOperation('DoExport')" :disabled="selectDatas.length == 0" type="primary" plain
                  class="">批量导出</el-button>
                <el-button @click="onOperation('DoDeleteList')" :disabled="selectDatas.length == 0" type="danger" plain
                  class="">批量删除</el-button>
              </el-col>
              <el-col :span="5">
                <el-form-item label="" prop="keyword" style="width: 100%">
                  <el-input placeholder="请输入关键字" v-model="state.queryForm.keyword" />
                </el-form-item>
              </el-col>
              <el-col :span="1" style="padding: 0px">
                <el-form-item>
                  <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
              </el-col>
              <el-col :span="1" style="padding-left: 40px;">
                <el-tooltip class="item" effect="dark" content="字段显隐控制" placement="top">
                  <el-button ref="filedControlBtnRef" icon="Fold" @click="isShowPopover = !isShowPopover" />
                </el-tooltip>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <el-popover :visible="isShowPopover" :virtual-ref="filedControlBtnRef" virtual-triggering trigger="click"
          title="自定义显示列项" width="300">
          <h6 style="font-size: 11px; color: #4f4f4fc7; margin: -8px 0px 10px 0px">
            列项显示不得少于5项，最多支持自定义10个列项，灰色选中列不支持隐藏和排序
          </h6>
          <el-table :data="tableColumn" show-overflow-tooltip border max-height="460px" draggable
            header-cell-class-name="custom-table-header">
            <el-table-column width="60" align="center" fixed="left" prop="isShow" label="显示">
              <template #default="scope">
                <el-checkbox v-model="scope.row.isShow" label="" :disabled="scope.row.disabled"
                  @change="selectionColumnCheckHandle" />
              </template>
            </el-table-column>
            <el-table-column align="center" prop="cnName" label="列名" :width="'auto'" />
          </el-table>
          <el-button size="small" text type="primary" style="margin: 5px 0px -5px 0px"
            @click="selectionColumnResetHandle">恢复默认</el-button>
          <el-button size="small" type="default" style="margin: 5px 0px -5px 0px; position: absolute; right: 70px"
            @click="isShowPopover = false">取消</el-button>
          <el-button size="small" type="primary" style="margin: 5px 0px -5px 0px; position: absolute; right: 10px"
            @click="selectionColumnChangeHandle">确定</el-button>
        </el-popover>

        <div class="table-container">
          <el-table :data="state.dataList || []" v-loading="state.loading" show-overflow-tooltip border
            @selection-change="selectionChangHandle" style="width: 100%" header-cell-class-name="custom-table-header"
            height="100%">
            <el-table-column type="selection" width="40" fixed="left" align="center" v-if="showColumnList.length" />
            <el-table-column align="center" v-for="(item, k) in showColumnList" :key="k" :prop="item.name"
              :label="item.cnName" min-width="130px" show-overflow-tooltip>
              <template #default="scope">
                <ImageUpload v-if="
                  String(scope.row[item.name]).includes('.jpg') ||
                  String(scope.row[item.name]).includes('.png') ||
                  String(scope.row[item.name]).includes('.jpeg')
                " v-model:imageUrl="scope.row[item.name]" :disabled="true" borderRadius="0%" width="90px" height="60px"
                  style="margin-bottom: -15px;">
                  <template #empty>
                    <el-icon>
                      <Picture />
                    </el-icon>
                    <span>暂无封面</span>
                  </template>
                </ImageUpload>
                <span v-else>
                  <span
                    style="overflow: hidden; white-space: nowrap; text-overflow: ellipsis; -o-text-overflow: ellipsis">{{
                      scope.row[item.name]
                    }}</span>
                </span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="operation" label="操作" width="180px" fixed="right"
              v-if="showColumnList.length">
              <template #default="scope">
                <div class="">
                  <el-button @click="onOperation('DoView', scope.row)" size="small" text type="primary"
                    :disabled="scope.row.level > 2">查看</el-button>
                  <el-button @click="onOperation('DoEdit', scope.row)" size="small" text type="primary">编辑</el-button>
                  <el-button @click="onOperation('DoDelete', scope.row)" size="small" text type="danger"
                    :disabled="scope.row.children?.length > 0">删除</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="pagination-section">
          <pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
        </div>
      </div>
    </div>

    <!-- 二级详情视图 -->
    <div class="detail-view-container" v-show="showDetailView">
      <div class="detail-content">
        <el-button @click="backToList" class="back-btn">
          <el-icon>
            <ArrowLeft />
          </el-icon>
          返回
        </el-button>
        <el-tabs v-model="activeTab" class="detail-tabs" @tab-click="handleTabClick">
          <el-tab-pane label="资源详情" name="detail">
            <ResourceDetailView v-if="activeTab === 'detail'" :key="`detail-${currentResource?.id}`"
              :resource-data="currentResource" :view-mode="detailViewMode" @refresh="refreshCurrentResource" />
          </el-tab-pane>
          <el-tab-pane label="操作日志" name="log">
            <OperationLogView v-if="activeTab === 'log'" :key="`log-${currentResource?.id}`"
              :resource-data="currentResource" />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 新增资源弹窗 -->
    <AddResourceDialog v-model:visible="showAddDialog" :edit-record="editRecord" @refresh="getDataList" />

    <UploadExcel ref="excelUploadRef" title="导入" :isLocal="false"
      :temp-url="'/datacenter/data/resource/download/template?tabName=' + state.queryForm.tableName"
      :url="'/datacenter/data/resource/import/excel?tabName=' + state.queryForm.tableName"
      @refreshDataList="getDataList" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, defineAsyncComponent, nextTick, onMounted } from 'vue';
import { Plus, ArrowLeft } from "@element-plus/icons-vue";
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { fetchList as pageList_resourceList, getColumn, deleteModelData } from '/@/api/resource/data/resource';
import { queryCatalogListForSquare } from '/@/api/personCenter';

const ImageUpload = defineAsyncComponent(() => import('/@/components/Upload/Image.vue'));
const QueryTree = defineAsyncComponent(() => import('/@/components/QueryTree/index.vue'));
const AddResourceDialog = defineAsyncComponent(() => import('./components/AddResourceDialog.vue'));
const ResourceDetailView = defineAsyncComponent(() => import('./components/ResourceDetailView.vue'));
const OperationLogView = defineAsyncComponent(() => import('./components/OperationLogView.vue'));
const UploadExcel = defineAsyncComponent(() => import('/@/components/Upload/Excel.vue'));
const Pagination = defineAsyncComponent(() => import('/@/components/Pagination/index.vue'));
const state: BasicTableProps = reactive<BasicTableProps>({
  queryForm: {
    third_catalog_no: '',
    tableId: '',
    tableName: '',
    keyword: '',
    columns: [],
  },
  createdIsNeed: false,
  // descs: ["gmt_create"],
  pageList: pageList_resourceList,
  props: {
    item: 'records',
    totalCount: 'total',
  },
  isPage: true,
});
const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile } = useTable(state);

// 自定义变量
const queryRef = ref();
const excelUploadRef = ref();
const isShowPopover = ref(false);
const filedControlBtnRef = ref();
const selectCatalog: any = ref({});
const showColumnList: any = ref([]);
const selectDatas = ref([]);
const editRecord: any = ref();
const tableColumn: any = ref([]);

// 二级详情视图相关变量
const showDetailView = ref(false);
const currentResource = ref<any>(null);
const activeTab = ref('detail');
const detailViewMode = ref<'view' | 'edit'>('view');
const showAddDialog = ref(false);

// 资源树使用的数据
const treeData = reactive({
  queryList: (name: String) => {
    return queryCatalogListForSquare({ catalogName: name, catalogState: 1 });
  },
});
const getDataList_custom = async () => {
  let res = await getColumn({ tableName: state.queryForm.tableName });
  res.data.forEach((obj: any) => {
    obj.isShow = obj.default;
  });
  showColumnList.value = res.data.filter((obj: any) => {
    return obj.default;
  });
  tableColumn.value = res.data;
  getDataList();
};
// 多选事件
const selectionChangHandle = (objs: any) => {
  selectDatas.value = objs;
};
// 多选事件
const selectionColumnChangeHandle = () => {
  showColumnList.value = tableColumn.value.filter((obj: any) => {
    return obj.isShow;
  });
  isShowPopover.value = false;
};
const selectionColumnResetHandle = () => {
  tableColumn.value.forEach((obj: any) => {
    obj.isShow = obj.default;
    obj.disabled = false;
  });
  selectionColumnChangeHandle();
};
const selectionColumnCheckHandle = () => {
  let showList = tableColumn.value.filter((obj: any) => {
    return obj.isShow;
  });
  if (showList.length >= 10) {
    tableColumn.value.forEach((obj: any) => {
      obj.disabled = !obj.isShow;
    });
  } else if (showList.length <= 5) {
    tableColumn.value.forEach((obj: any) => {
      obj.disabled = obj.isShow;
    });
  } else {
    tableColumn.value.forEach((obj: any) => {
      obj.disabled = false;
    });
  }
};
// 点击树
const handleNodeClick = (item: any, node: any) => {
  node.isCurrent = false;
  if (item.catalogLevel == 4) {
    node.isCurrent = true;
    selectCatalog.value = item;
    state.queryForm.tableName = item.catalogRemark;
    state.queryForm.tableId = item.id;
    state.queryForm.third_catalog_no = item.parentCatalog;
    getDataList_custom();
  }
};
const handleTreeDataLoaded = (treeData: any, treeRef: any) => {
  for (let i in treeData) {
    for (let j in treeData[i].childCatalogs) {
      for (let k in treeData[i].childCatalogs[j].childCatalogs) {
        for (let m in treeData[i].childCatalogs[j].childCatalogs[k].childCatalogs) {
          treeData[i].childCatalogs[j].childCatalogs[k].childCatalogs[m].catalogNo =
            treeData[i].childCatalogs[j].childCatalogs[k].childCatalogs[m].catalogRemark;
          treeData[i].childCatalogs[j].childCatalogs[k].childCatalogs[m].parentCatalog = treeData[i].childCatalogs[j].childCatalogs[k].catalogNo;
        }
      }
    }
  }
  let sel: any = null;
  for (let i in treeData) {
    for (let j in treeData[i].childCatalogs) {
      for (let k in treeData[i].childCatalogs[j].childCatalogs) {
        if (treeData[i].childCatalogs[j].childCatalogs[k].childCatalogs?.length > 0) {
          sel = treeData[i].childCatalogs[j].childCatalogs[k].childCatalogs[0];
          break;
        }
      }
      if (sel) break;
    }
    if (sel) break;
  }
  selectCatalog.value = sel;
  state.queryForm.tableName = selectCatalog.value?.catalogNo;
  state.queryForm.tableId = selectCatalog.value?.id;
  state.queryForm.third_catalog_no = selectCatalog.value?.parentCatalog;

  if (selectCatalog.value) {
    getDataList_custom();
  }
  nextTick(() => {
    selectCatalog.value?.catalogNo && treeRef!.setCurrentKey(selectCatalog.value.catalogNo);
  });
};
// 清空搜索条件
const resetQuery = () => {
  queryRef.value?.resetFields();
  getDataList_custom();
};
const onOperation = (type: string, record?: any) => {
  isShowPopover.value = false;
  switch (type) {
    case 'DoAdd':
      if (!state.queryForm.tableName) {
        useMessage().wraning('元数据不能为空，请先选择元数据');
        return;
      }
      // 改为弹窗模式
      editRecord.value = {
        tableName: state.queryForm.tableName,
        tableId: state.queryForm.tableId,
        third_catalog_no: state.queryForm.third_catalog_no,
      };
      showAddDialog.value = true;
      break;
    case 'DoView':
      // 改为本页面二级界面
      currentResource.value = record;
      currentResource.value.tableName = state.queryForm.tableName;
      currentResource.value.tableId = state.queryForm.tableId;
      detailViewMode.value = 'view';
      activeTab.value = 'detail';
      showDetailView.value = true;
      break;
    case 'DoEdit': {
      // 改为本页面二级界面
      currentResource.value = record;
      currentResource.value.tableName = state.queryForm.tableName;
      currentResource.value.tableId = state.queryForm.tableId;
      detailViewMode.value = 'edit';
      activeTab.value = 'detail';
      showDetailView.value = true;
      break;
    }
    case 'DoDelete':
      onDeleteItem([record.id]);
      break;
    case 'DoDeleteList':
      onDeleteItem(
        selectDatas.value.map((obj: any) => {
          return obj.id;
        })
      );
      break;
    case 'DoUpload':
      excelUploadRef.value.show();
      break;
    case 'DoExport':
      let ids = selectDatas.value.map((obj: any) => {
        return obj.id;
      });
      let params = { tableName: state.queryForm.tableName, ids: ids.join(',') };
      downBlobFile('/datacenter/data/resource/export', params, 'resourceInfo.xlsx');
      break;
  }
};
// 删除
const onDeleteItem = async (ids: any) => {
  try {
    await useMessageBox().confirm('确认进行删除操作吗？');
  } catch {
    return;
  }
  try {
    await deleteModelData({
      tableName: state.queryForm.tableName,
      ids: ids,
    });
    useMessage().success('删除成功');
    nextTick(() => {
      getDataList();
    });
  } catch (err) {
    useMessage().error((err as any).msg);
  }
};
// 返回列表视图
const backToList = () => {
  showDetailView.value = false;
  currentResource.value = null;
  activeTab.value = 'detail';
  // 刷新列表数据
  getDataList();
};

// 刷新当前资源数据
const refreshCurrentResource = () => {
  if (currentResource.value?.id) {
    // 重新获取资源数据
    getDataList();
  }
};

// 处理tab点击事件
const handleTabClick = (tab: any) => {
  activeTab.value = tab.props.name;
};

onMounted(() => {
  // 重置表单数据
  // nextTick(() => {});
});
</script>

<style scoped lang="scss">
// 主容器布局
.mt-box {
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: 300px 1fr;
  grid-template-rows: 100%;
  grid-column-gap: 15px;
  background-color: #f5f7fa;
  overflow: hidden; // 防止内容溢出

  // 自定义表头样式
  :deep(.custom-table-header) {
    background-color: var(--next-bg-main-color, #f5f7fa);
    color: rgba(0, 0, 0, 0.4);
    font-weight: 400 !important;
    font-size: 14px !important;
    height: 50px;
  }

}

:deep(.el-upload) {
  display: inline-flex !important;
}

// 自定义表头样式
:deep(.custom-table-header) {
  background-color: var(--next-bg-main-color, #f5f7fa);
  color: rgba(0, 0, 0, 0.4);
  font-weight: 400 !important;
  font-size: 14px !important;
  height: 50px;
}


// 左侧树形面板
.left-tree-panel {
  display: flex;
  flex-direction: column;
  background-color: white;
  padding: 15px;
  height: 100%;
  overflow: hidden;

  .layout-model-title {
    font-size: 16px;
    font-weight: 400;
    color: #1d1d1d;
    padding-left: 5px;
    margin-bottom: 0px;
    flex-shrink: 0; // 标题不收缩
  }

  .tree-container {
    flex: 1;
    overflow: hidden;
    min-height: 0; // 重要：允许flex子项收缩
  }
}

// 右侧内容面板
.right-content-panel {
  display: flex;
  flex-direction: column;
  background-color: white;
  padding: 15px;
  height: 100%;
  overflow: hidden;

  .toolbar-section {
    flex-shrink: 0; // 工具栏不收缩
  }

  .table-container {
    flex: 1;
    overflow: hidden;
    min-height: 0; // 重要：允许flex子项收缩
  }

  .pagination-section {
    flex-shrink: 0; // 分页不收缩
  }
}

// 表格样式优化
:deep(.el-table) {
  height: 100% !important;

  .el-table__body-wrapper {
    overflow-y: auto;
  }
}

// 滚动条样式优化
:deep(.el-scrollbar) {
  height: 100%;

  .el-scrollbar__wrap {
    overflow-x: hidden;
  }
}

// 二级详情视图样式
.detail-view-container {
  width: 100%;
  height: 100%;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;

  .back-btn {
    color: #606266;
    font-size: 14px;
    position: absolute;
    right: 19px;
    margin-top: 3px;
    z-index: 1;

    &:hover {
      color: var(--el-color-primary);
    }
  }

  .detail-content {
    flex: 1;
    overflow: hidden;
    background-color: white;
    margin: 15px 15px 0px 15px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .detail-tabs {
      height: 100%;

      :deep(.el-tabs__header) {
        margin: 0;
        padding: 0 20px;
        border-bottom: 1px solid #e4e7ed;
      }

      :deep(.el-tabs__content) {
        height: calc(100% - 40px);
        overflow: hidden;
      }

      :deep(.el-tab-pane) {
        height: 100%;
        overflow: hidden;
      }
    }
  }
}

.group-header {
  margin-bottom: 10px;

  .group-title {
    font-size: 16px;
    font-weight: 600;
    padding-left: 15px;
    position: relative;
    color: #554242;
    letter-spacing: 0.1em;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 15%;
      width: 3px;
      height: 70%;
      border-radius: 6px;
      background: var(--el-color-primary);
    }
  }

  .group-add {
    cursor: pointer;
    color: var(--el-color-primary);
  }
}
</style>
