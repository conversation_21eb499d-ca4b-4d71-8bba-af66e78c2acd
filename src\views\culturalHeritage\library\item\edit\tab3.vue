<template>
	<div class="fields-management-container">
		<!-- 左侧字段分组区域 -->
		<div class="fields-groups-panel">
			<div class="groups-header">
				<div class="groups-title">信息条目</div>
				<el-button type="primary" text @click="onClickGroup('DoAdd')">
					+ 新增条目
				</el-button>
			</div>
			<div class="groups-list">
				<div v-if="groupTreeData.length" class="groups-content">
					<div v-for="item in groupTreeData" :key="item.id" class="group-tree-item">
						<!-- 主分组 -->
						<div @click="onSelectGroup(item)" class="group-item main-group"
							:class="{ 'is-active': state.queryForm.groupId === item.id }">
							<div class="group-info">
								<div class="group-expand"
									v-if="item.groupType == 2 && item.subGroups && item.subGroups.length > 0 && item.defaultGroup != 1">
									<el-icon @click.stop="toggleGroupExpand(item)" class="expand-icon"
										:class="{ 'is-expanded': item.expanded }">
										<CaretRight />
									</el-icon>
								</div>
								<div class="group-expand placeholder" v-else></div>

								<div class="group-details">
									<div class="group-info-line">
										<div class="group-name" :title="item.groupName">{{ item.groupName }}</div>
										<div class="group-tags">
											<el-tag :type="item.groupType == 1 ? 'primary' : 'success'" size="small"
												class="group-type-tag">
												{{ item.groupType == 1 ? '列表' : '表单' }}
											</el-tag>
											<el-tag v-if="item.defaultGroup === 1" type="info" size="small"
												class="default-tag">
												默认
											</el-tag>
										</div>
									</div>
								</div>
							</div>

							<!-- 默认分组不显示操作菜单 -->
							<el-dropdown v-if="item.id === state.queryForm.groupId && item.defaultGroup !== 1"
								trigger="click" @click.stop>
								<el-icon class="group-actions" style="margin-left: 5px;">
									<img src="/@/assets/img/resourceHome/Union.png" />
								</el-icon>
								<template #dropdown>
									<el-dropdown-menu>
										<el-dropdown-item v-if="item.groupType == 2"
											@click="onClickGroup('DoAddChild', item)">
											<el-icon>
												<Plus />
											</el-icon>
											新增分组
										</el-dropdown-item>
										<el-dropdown-item @click="onClickGroup('DoEdit', item)">
											<el-icon>
												<Edit />
											</el-icon>
											编辑
										</el-dropdown-item>
										<el-dropdown-item @click="onClickGroup('DoDelete', item)" class="delete-item">
											<el-icon>
												<Delete />
											</el-icon>
											删除
										</el-dropdown-item>
									</el-dropdown-menu>
								</template>
							</el-dropdown>
						</div>

						<!-- 子分组 -->
						<div v-if="item.groupType == 2 && item.subGroups && item.subGroups.length > 0 && item.defaultGroup != 1 && item.expanded"
							class="sub-groups">
							<div v-for="child in item.subGroups" :key="child.id"
								@click="onSelectChildGroup(item, child)" class="group-item sub-group"
								:class="{ 'is-active': state.queryForm.groupId === child.id }">
								<div class="group-name" :title="item.groupName">{{ child.groupName }}</div>
								<!-- 默认子分组不显示操作菜单 -->
								<el-dropdown v-if="child.id === state.queryForm.groupId && child.defaultGroup !== 1"
									trigger="click">
									<el-icon class="group-actions" style="margin-left: 5px;" @click.stop="() => { }">
										<img src="/@/assets/img/resourceHome/Union.png" />
									</el-icon>
									<template #dropdown>
										<el-dropdown-menu>
											<el-dropdown-item @click="onClickGroup('DoEditChild', child)">
												<el-icon>
													<Edit />
												</el-icon>
												编辑
											</el-dropdown-item>
											<el-dropdown-item @click="onClickGroup('DoGroupDelete', child)"
												class="delete-item">
												<el-icon>
													<Delete />
												</el-icon>
												删除
											</el-dropdown-item>
										</el-dropdown-menu>
									</template>
								</el-dropdown>
							</div>
						</div>
					</div>
				</div>
				<el-empty v-else description="暂无分组数据" :image-size="80" />
			</div>

			<group-form-dialog ref="groupFormDialogRef" @refresh="getGroupList" />
			<sub-group-form-dialog ref="subGroupFormDialogRef" @refresh="getGroupList" />
		</div>

		<!-- 右侧字段列表区域 -->
		<div class="fields-list-panel">
			<div class="fields-header">
				<div class="fields-title">
					<el-button v-if="!isDefaultGroup" type="primary" @click="onClickField('DoAdd')"
						class="add-field-btn">
						<el-icon>
							<Plus />
						</el-icon>
						新增字段
					</el-button>
				</div>
				<div class="fields-actions">
					<!-- 默认条目不显示新增字段按钮 -->
					<el-button @click="backTo" class="back-btn">
						<el-icon>
							<ArrowLeft />
						</el-icon>
						返回
					</el-button>
				</div>
			</div>

			<div class="fields-table-container">
				<el-table :data="state.dataList || []" v-loading="state.loading" class="fields-table" stripe>
					<el-table-column prop="cnName" label="字段名称" min-width="120" show-overflow-tooltip>
						<template #default="scope">
							<div class="field-name">{{ scope.row.cnName }}</div>
						</template>
					</el-table-column>
					<el-table-column
						v-if="currentSelectedGroup && currentSelectedGroup.groupType == 2 && currentSelectedGroup.defaultGroup != 1"
						prop="subGroupId" label="分组" min-width="120" show-overflow-tooltip>
						<template #default="scope">
							<div class="field-name">{{
								currentSelectedGroup.subGroups?.find((el: any) => el.id ==
									scope.row.subGroupId)?.groupName || ''
							}}</div>
						</template>
					</el-table-column>
					<!-- <el-table-column prop="name" label="字段名" min-width="120" show-overflow-tooltip>
						<template #default="scope">
							<div class="field-code">{{ scope.row.name }}</div>
						</template>
					</el-table-column> -->

					<el-table-column prop="typeCode" label="字段类型" min-width="100">
						<template #default="scope">
							<el-tag :type="getFieldTypeTagType(scope.row.typeCode)" size="small" class="field-type-tag">
								{{ columnTypeConfig[scope.row.typeCode]?.name || scope.row.typeCode }}
							</el-tag>
						</template>
					</el-table-column>

					<el-table-column label="是否必填" width="120">
						<template #default="scope">
							<el-tag :type="getMustFileeStatus(scope.row) ? 'success' : 'danger'" size="small">
								{{ getMustFileeStatus(scope.row) ? '是' : '否' }}
							</el-tag>
						</template>
					</el-table-column>

					<el-table-column label="是否启用" width="120">
						<template #default="scope">
							<el-tag :type="getFieldStatusType(scope.row)" size="small" class="status-tag">
								{{ getFieldStatusText(scope.row) }}
							</el-tag>
						</template>
					</el-table-column>

					<el-table-column prop="createUser" label="创建人" width="120" show-overflow-tooltip>
						<template #default="scope">
							{{ scope.row.createUser || "管理员" }}
						</template>
					</el-table-column>

					<el-table-column prop="createTime" label="创建时间" width="180" show-overflow-tooltip />



					<!-- 默认条目不显示操作列 -->
					<el-table-column v-if="!isDefaultGroup" label="操作" width="180" align="center" fixed="right">
						<template #default="scope">
							<div class="operation-buttons">
								<el-button @click="onClickField('DoEdit', scope.row)" size="small" type="primary" text
									:disabled="scope.row.useState == 1">
									编辑
								</el-button>
								<el-button @click="onClickField('DoEnable', scope.row)" size="small"
									:type="scope.row.useState == 0 ? 'success' : 'warning'" text>
									{{ scope.row.useState == 0 ? '启用' : '禁用' }}
								</el-button>
								<el-button @click="onClickField('DoDelete', scope.row)" size="small" type="danger" text>
									删除
								</el-button>
							</div>
						</template>
					</el-table-column>
				</el-table>
			</div>

			<!-- 字段表单对话框 -->
			<fields-form-dialog ref="fieldsFormDialogRef" @refresh="getDataList" v-if="showField"
				@cancel="onFieldFormCancel" :record="fieldsRecord" />
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineAsyncComponent, onMounted, nextTick } from 'vue';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { listColumnGroup, pageColumnInfo, editColumnInfo, removeColumnGroup, delFormColumnGroup } from '/@/api/resource/catalog/columnInfo';
import { columnTypeConfig } from '/@/config/resourceConfig';
import {
	Edit,
	Delete,
	Plus,
	ArrowLeft,
	CaretRight,
} from '@element-plus/icons-vue';

const GroupFormDialog = defineAsyncComponent(() => import('./GroupForm.vue'));
const SubGroupFormDialog = defineAsyncComponent(() => import('./SubGroupForm.vue'));
const FieldsFormDialog = defineAsyncComponent(() => import('./FieldsForm.vue'));
const emit = defineEmits(['backTo']);
const props = defineProps({
	resuorceData: {
		type: Array,
		default: () => [],
	},
	deptData: {
		type: Array,
		default: () => [],
	},
	editCode: {
		type: String,
		default: null,
	},
	editRecord: {
		type: Object,
		default: null,
	},
});

// 响应式数据
const fieldsFormDialogRef = ref();
const groupFormDialogRef = ref();
const subGroupFormDialogRef = ref();
const groupList = ref<any[]>([]);
const groupTreeData = ref<any[]>([]); // 树形结构数据
const currentSelectedGroup = ref<any>(null); // 当前选中的分组
const fieldsRecord = ref({});
const showField = ref(false);

// 计算当前选中的分组是否为默认分组
const isDefaultGroup = computed(() => {
	return currentSelectedGroup.value?.defaultGroup === 1;
});
const backTo = () => {
	emit('backTo');
};
const getGroupList = async () => {
	let res = await listColumnGroup({ tableName: props.editRecord.tableName });
	groupList.value = res.data;

	// 构建树形结构
	buildGroupTree();

	if (groupTreeData.value.length > 0) {
		// 默认选择第一个主分组
		const firstGroup = groupTreeData.value[0];
		onSelectGroup(firstGroup);
	}
};

// 构建分组树形结构
const buildGroupTree = () => {
	const treeData: any[] = [];
	const groupMap = new Map();

	// 先处理所有分组，建立映射关系
	groupList.value.forEach((group: any) => {
		groupMap.set(group.id, {
			...group,
			children: [],
			expanded: false // 默认折叠
		});
	});

	// 构建树形结构
	groupList.value.forEach((group: any) => {
		const groupItem = groupMap.get(group.id);
		if (group.parentId && groupMap.has(group.parentId)) {
			// 有父级，添加到父级的children中
			const parent = groupMap.get(group.parentId);
			parent.children.push(groupItem);
		} else {
			// 没有父级，作为根节点
			treeData.push(groupItem);
		}
	});

	groupTreeData.value = treeData;
};

// 切换分组展开/折叠状态
const toggleGroupExpand = (item: any) => {
	item.expanded = !item.expanded;
};
const onSelectGroup = (item: any) => {
	currentSelectedGroup.value = item; // 设置当前选中的分组
	state.pageList = pageColumnInfo;
	state.isPage = true;
	state.queryForm.groupId = item.id;
	getDataList();
};

const onSelectChildGroup = async (parent: any, item: any) => {
	currentSelectedGroup.value = parent;
	state.pageList = pageColumnInfo;
	state.isPage = true;
	state.queryForm.groupId = parent.id;
	const res = await pageColumnInfo({
		...state.queryForm,
		groupId: parent.id,
		current: state.pagination?.current,
		size: state.pagination?.size,
		descs: state.descs?.join(','),
		ascs: state.ascs?.join(','),
	});
	state.dataList = res.data?.data?.filter((el: any) => el.subGroupId === item.id);
};


const onClickGroup = (type: string, record?: any) => {
	switch (type) {
		case 'DoAdd':
			// 新增主条目
			groupFormDialogRef.value.openDialog({
				metaDataId: props.editRecord.id,
				parentId: null
			});
			break;
		case 'DoAddChild':
			// 新增分组（只有表单类型的条目才能新增分组）
			if (record && record.groupType == 2) {
				subGroupFormDialogRef.value.openDialog(
					{
						metaDataId: props.editRecord.id,
						parentId: record.id
					},
					groupTreeData.value.filter((el: any) => el.defaultGroup != 1) // 传递所有主条目供选择
				);
			}
			break;
		case 'DoEdit':
			// 判断是主条目还是分组
			if (record.parentId) {
				// 编辑分组
				subGroupFormDialogRef.value.openDialog(record, groupTreeData.value);
			} else {
				// 编辑主条目
				groupFormDialogRef.value.openDialog(record);
			}
			break;
		case 'DoEditChild':
			// 新增分组（只有表单类型的条目才能新增分组）
			if (record) {
				subGroupFormDialogRef.value.openDialog(
					record,
					groupTreeData.value.filter((el: any) => el.defaultGroup != 1) // 传递所有主条目供选择
				);
			}
			break;
		case 'DoDelete':
			onDeleteGroup(record);
			break;
		case "DoGroupDelete":
			onDeleteFormGroup(record);
			break;
	}
};
// 彻底删除
const onDeleteGroup = async (obj: any) => {
	try {
		await useMessageBox().confirm('确认彻底删除吗？');
	} catch {
		return;
	}
	try {
		await removeColumnGroup({ id: obj.id });
		useMessage().success('删除成功');
		getGroupList();
	} catch (err) {
		useMessage().error((err as any).msg);
	}
};

// 彻底删除表单分组
const onDeleteFormGroup = async (obj: any) => {
	try {
		await useMessageBox().confirm('确认彻底删除吗？');
	} catch {
		return;
	}
	try {
		await delFormColumnGroup({ id: obj.id });
		useMessage().success('删除成功');
		getGroupList();
	} catch (err) {
		useMessage().error((err as any).msg);
	}
};

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		tableInfoId: null,
		name: null,
		groupId: null,
	},
	createdIsNeed: false,
	// descs: ["gmt_create"],
	pageList: pageColumnInfo,
	props: {
		item: 'data',
		totalCount: 'total',
	},
	pagination: {
		// 当前页码，默认为1
		current: 1,
		// 每页显示条数，默认为10
		size: 100,
	},
});
const { getDataList } = useTable(state);
const onClickField = (type: string, record?: any) => {
	switch (type) {
		case 'DoAdd': {
			showField.value = true;
			fieldsRecord.value = {
				tableInfoId: props.editRecord.id,
				groupId: currentSelectedGroup.value.id,// state.queryForm.groupId,
				groupData: currentSelectedGroup.value.groupType == 2 ? (currentSelectedGroup.value.subGroups || []) : []
			};
			break;
		}
		case 'DoViewDDL':
			break;
		case 'DoCopy':
			break;
		case 'DoEdit': {
			showField.value = true;
			fieldsRecord.value = Object.assign(record, { groupData: currentSelectedGroup.value.groupType == 2 ? (currentSelectedGroup.value.subGroups || []) : [] });
			break;
		}
		case 'DoEnable':
			onEnableField(record);
			break;
		case 'DoDelete':
			onDeleteField(record);
			break;
	}
};

// 启用/禁用
const onEnableField = async (obj: any) => {
	let title = obj.useState == 0 ? '启用' : '禁用';
	try {
		await useMessageBox().confirm('确认进行' + title + '操作吗？');
	} catch {
		return;
	}
	try {
		obj.useState = obj.useState == 1 ? 0 : 1;
		await editColumnInfo(obj);
		useMessage().success(title + '成功');
		getDataList();
	} catch (err) {
		useMessage().error((err as any).msg);
	}
};

// 启用/禁用
const onDeleteField = async (obj: any) => {
	try {
		await useMessageBox().confirm('确认进行删除操作吗？');
	} catch {
		return;
	}
	try {
		obj.useState = 2;
		await editColumnInfo(obj);
		useMessage().success('删除成功');
		getDataList();
	} catch (err) {
		useMessage().error((err as any).msg);
	}
};

// 字段表单取消事件
const onFieldFormCancel = () => {
	showField.value = false;
	fieldsRecord.value = {};
};

// 获取字段类型标签类型
const getFieldTypeTagType = (typeCode: string) => {
	const typeMap: Record<string, string> = {
		'oneLine': 'primary',
		'multiLine': 'success',
		'richText': 'info',
		'number': 'warning',
		'amount': 'warning',
		'date': 'danger',
		'dateRange': 'danger',
		'ratioOption': '',
		'multiOption': '',
		'province': 'info',
		'image': 'success',
		'attach': 'success'
	};
	return typeMap[typeCode] || '';
};

// 获取必填状态
const getMustFileeStatus = (row: any) => {
	if (row.columnRule) {
		const rule = JSON.parse(row.columnRule);
		return rule.mustFilee || false;
	}

	return false;
};

// 获取字段状态类型
const getFieldStatusType = (row: any) => {
	if (row.useState === 1) {
		return 'success';
	} else if (row.useState === 0) {
		return 'warning';
	} else {
		return 'danger';
	}
};

// 获取字段状态文本
const getFieldStatusText = (row: any) => {
	if (row.useState === 1) {
		return '是';
	} else if (row.useState === 0) {
		return '否';
	} else {
		return '--';
	}
};

onMounted(() => {
	if (!props.editRecord) return;
	state.queryForm.tableInfoId = props.editRecord.id;
	getGroupList();
	// 重置表单数据
	nextTick(() => { });
});
</script>

<style scoped lang="scss">
// 主容器样式
.fields-management-container {
	width: 100%;
	height: 100%;
	display: flex;
	background: #fff;
}

// 左侧分组面板样式
.fields-groups-panel {
	width: 280px;
	min-width: 280px;
	border-right: 1px solid #e4e7ed;
	display: flex;
	flex-direction: column;
	background: #fafafa;

	.groups-header {
		padding: 15px;
		padding-right: 0px;
		border-bottom: 1px solid #e4e7ed;
		display: flex;
		justify-content: space-between;
		align-items: center;
		background: #fff;

		.groups-title {
			font-size: 16px;
			font-weight: 400;
			color: #1D1D1D;
			padding-left: 5px;
			margin-bottom: 0px;
		}
	}

	.groups-list {
		flex: 1;
		overflow-y: auto;
		padding: 8px;
		background: white;

		.groups-content {

			.group-tree-item {
				margin-bottom: 4px;
			}

			.group-item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 8px 0px;
				padding-right: 4px;
				border-radius: 6px;
				cursor: pointer;
				transition: all 0.2s;
				background: #F6F6F6;

				&:hover {
					background: #FFF0ED;
				}

				&.is-active {
					background: #FFF0ED;
					color: var(--el-color-primary, #A12F2F);

					.group-name {
						color: var(--el-color-primary, #A12F2F);
					}
				}

				&.main-group {
					// 主分组样式
					font-weight: 500;
				}

				&.sub-group {
					// 子分组样式
					margin-left: 24px;
					padding-left: 25px;
					margin-top: 5px;

					&.is-active {
						background: #FFF0ED;
						color: var(--el-color-primary, #A12F2F);
					}
				}

				.group-info {
					display: flex;
					align-items: center;
					flex: 1;
					min-width: 0;

					.group-expand {
						width: 16px;
						height: 16px;
						display: flex;
						align-items: center;
						justify-content: center;
						margin-left: 4px;
						margin-right: 4px;

						&.placeholder {
							/* 占位符，保持对齐 */
							visibility: hidden;
						}

						.expand-icon {
							color: #909399;
							font-size: 12px;
							cursor: pointer;
							transition: transform 0.2s;

							&.is-expanded {
								transform: rotate(90deg);
							}
						}
					}

					.group-details {
						flex: 1;
						min-width: 0;

						.group-info-line {
							display: flex;
							align-items: center;
							min-width: 0;

							.group-name {
								font-weight: 400;
								font-size: 14px;
								line-height: 22px;
								letter-spacing: 0%;
								white-space: nowrap;
								overflow: hidden;
								text-overflow: ellipsis;
								flex: 1;
								min-width: 0;
							}

							.group-tags {
								display: flex;
								align-items: center;
								gap: 4px;
								flex-shrink: 0;

								.group-type-tag {
									flex-shrink: 0;
								}

								.default-tag {
									flex-shrink: 0;
									background: #f4f4f5;
									color: #909399;
									border-color: #e4e7ed;
								}
							}
						}
					}
				}

			}

			.sub-groups {
				margin-top: 4px;
			}
		}
	}
}

// 右侧字段列表面板样式
.fields-list-panel {
	flex: 1;
	display: flex;
	flex-direction: column;
	min-width: 0;

	.fields-header {
		padding: 15px 20px;
		border-bottom: 1px solid #e4e7ed;
		display: flex;
		justify-content: space-between;
		align-items: center;
		background: #fff;

		.fields-title {
			font-size: 16px;
			font-weight: 600;
			color: #303133;
		}

		.fields-actions {
			display: flex;
			gap: 8px;

			.add-field-btn {
				.el-icon {
					margin-right: 4px;
				}
			}

			.back-btn {
				.el-icon {
					margin-right: 4px;
				}
			}
		}
	}

	.fields-table-container {
		flex: 1;
		padding: 5px;
		overflow: hidden;

		.fields-table {
			height: calc(100% - 10px);
			width: calc(100% - 15px);

			.field-name {
				font-weight: 500;
				color: #303133;
			}

			.field-code {
				font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
				color: #606266;
				font-size: 12px;
			}

			.field-type-tag {
				font-weight: 500;
			}

			.subject-type {
				color: #606266;
				font-size: 13px;
			}

			.status-tag {
				font-weight: 500;
			}

			.operation-buttons {
				display: flex;
				gap: 4px;
				justify-content: center;
			}
		}
	}
}

// 下拉菜单样式
:deep(.el-dropdown-menu) {
	.delete-item {
		color: #f56c6c;

		&:hover {
			background: #fef0f0;
			color: #f56c6c;
		}
	}
}

// 表格样式优化
:deep(.el-table) {
	.el-table__header {
		th {
			background-color: var(--next-bg-main-color, #f5f7fa);
			color: rgba(0, 0, 0, 0.4);
			font-weight: 400;
			font-size: 14px;
			height: 50px;
		}
	}

	.el-table__row {
		&:hover {
			background: #f5f7fa;
		}
	}
}
</style>
