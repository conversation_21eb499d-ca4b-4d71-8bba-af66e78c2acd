<template>
  <el-dialog v-model="dialogVisible" title="新增资源" width="900px" :close-on-click-modal="false"
    :close-on-press-escape="false" destroy-on-close @close="handleClose" class="add-resource-dialog">
    <div class="dialog-content">
      <el-form ref="dataFormRef" :model="form" :rules="dataRules" label-width="100px" v-loading="loading"
        class="resource-form">
        <!-- 基础信息 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-line"></div>
            <div class="section-title">基础信息</div>
          </div>

          <div class="form-content">
            <el-row :gutter="20">
              <!-- 左侧封面上传 -->
              <el-col :span="6">
                <div class="cover-upload-area">
                  <el-form-item label="资源封面" prop="assets_cover" class="cover-form-item">
                    <div class="upload-container">
                      <ImageUpload v-model:imageUrl="form.assets_cover" borderRadius="4px" width="120px" height="100px"
                        uploadFileUrl="/datacenter/learning/material/cover" class="cover-uploader">
                        <template #empty>
                          <div class="upload-placeholder">
                            <el-icon class="upload-icon">
                              <Picture />
                            </el-icon>
                            <div class="upload-text">请上传封面</div>
                          </div>
                        </template>
                      </ImageUpload>
                      <div class="upload-tip">图片支持JPG、PNG且小于5M</div>
                    </div>
                  </el-form-item>
                </div>
              </el-col>

              <!-- 右侧表单字段 -->
              <el-col :span="18">
                <div class="form-fields">
                  <el-row :gutter="16">
                    <el-col :span="12">
                      <el-form-item label="资源名称" prop="assets_name">
                        <el-input v-model="form.assets_name" maxlength="20" placeholder="请输入资源名称" class="form-input" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="公开状态" prop="public_state">
                        <el-select v-model="form.public_state" placeholder="请选择公开状态" clearable class="form-select">
                          <el-option label="公开" :value="1" />
                          <el-option label="私有" :value="0" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="16" style="margin-top: 18px;">
                    <el-col :span="12">
                      <el-form-item label="资源密级" prop="security_id">
                        <el-select v-model="form.security_id" placeholder="请选择文件密级" clearable class="form-select">
                          <el-option v-for="item in securityList" :key="item.id" :label="item.title" :value="item.id" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="资源标签" prop="assets_tag">
                        <el-input v-model="form.assets_tag" maxlength="16" placeholder="多个标签用 ，隔开" class="form-input" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row style="margin-top: 18px;">
                    <el-col :span="24">
                      <el-form-item label="描述" prop="assets_remark">
                        <el-input type="textarea" v-model="form.assets_remark" maxlength="250" :rows="3" show-word-limit
                          placeholder="请输入描述" class="form-textarea" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 位置信息 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-line"></div>
            <div class="section-title">位置信息</div>
            <el-button @click="mapMakerVisible = true" class="map-picker-btn" type="primary" plain size="small"
              style="position: absolute !important; right: 40px !important;">
              <el-icon>
                <Location />
              </el-icon>
              地图拾取
            </el-button>
          </div>
          <div class="form-content location-content">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="经度" prop="longitude">
                  <el-input v-model="form.longitude" maxlength="20" placeholder="请输入经度" class="form-input" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="纬度" prop="latitude">
                  <el-input v-model="form.latitude" maxlength="20" placeholder="请输入纬度" class="form-input" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin-top: 18px;">
              <el-col :span="24">
                <el-form-item label="位置描述" prop="location_remark">
                  <el-input type="textarea" v-model="form.location_remark" maxlength="250" :rows="3" show-word-limit
                    placeholder="请输入位置描述" class="form-input" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" class="cancel-btn">取消</el-button>
        <el-button type="primary" @click="onSubmit" :loading="loading" class="submit-btn">
          确认
        </el-button>
      </div>
    </template>

    <!-- 地图拾取弹窗 -->
    <el-dialog v-model="mapMakerVisible" title="地图拾取" width="1200px" align-center destroy-on-close
      :close-on-click-modal="false" :close-on-press-escape="false" class="map-dialog">
      <LeafletMap @mapClick="onMapClick" style="height: 650px" />
    </el-dialog>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, defineAsyncComponent, nextTick } from 'vue';
import { useMessage } from '/@/hooks/message';
import { securityLevelConfig } from '/@/config/resourceConfig';
import { fetchList as fetchList_security } from '/@/api/resource/security/level';
import { addModelData } from '/@/api/resource/data/resource';
import { Picture, Location } from '@element-plus/icons-vue';

const ImageUpload = defineAsyncComponent(() => import('/@/components/Upload/Image.vue'));
const LeafletMap = defineAsyncComponent(() => import('/@/components/LeafletMap/index.vue'));

interface Props {
  visible: boolean;
  editRecord: any;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  editRecord: null,
});

const emit = defineEmits<{
  'update:visible': [value: boolean];
  refresh: [];
}>();

// 响应式数据
const dataFormRef = ref();
const loading = ref(false);
const mapMakerVisible = ref(false);
const securityList = ref<any[]>([]);

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

// 表单数据
const form = reactive({
  assets_cover: '',
  assets_name: '',
  public_state: '',
  security_id: '',
  assets_tag: '',
  assets_remark: '',
  longitude: '',
  latitude: '',
  location_remark: '',
});

// 验证规则
const dataRules = ref({
  assets_cover: [{ required: true, message: '封面不能为空', trigger: 'change' }],
  assets_name: [{ required: true, message: '资源名称不能为空', trigger: 'blur' }],
  public_state: [{ required: true, message: '公开状态不能为空', trigger: 'change' }],
  security_id: [{ required: true, message: '资源密级不能为空', trigger: 'change' }],
});

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    initData();
  }
});

// 初始化数据
const initData = async () => {
  try {
    loading.value = true;

    // 获取安全级别
    const res_security = await fetchList_security({ enabled: 1 });
    res_security.data.forEach((item: any) => {
      item.title = item.securityName + '（' + securityLevelConfig[item.sortOrder] + '）';
    });
    securityList.value = res_security.data;

    // 重置表单
    nextTick(() => {
      dataFormRef.value?.resetFields();
    });
  } catch (error) {
    useMessage().error('初始化数据失败');
  } finally {
    loading.value = false;
  }
};

// 提交表单
const onSubmit = async () => {
  const valid = await dataFormRef.value.validate();
  if (!valid) return;
  try {
    loading.value = true;

    const securityOBJ = securityList.value.find((obj: any) => obj.id === form.security_id);
    const obj_add: any = {
      tableName: props.editRecord.tableName,
      datas: {
        ...form,
        longitude: String(form.longitude),
        latitude: String(form.latitude),
      },
      extraMap: {
        third_catalog_no: props.editRecord.third_catalog_no,
        security_name: securityOBJ?.securityName,
      },
      groupMap: {},
    };

    await addModelData(obj_add);
    useMessage().success('添加成功');
    emit('refresh');
    handleClose();
  } catch (error) {
    useMessage().error((error as any).msg || '添加失败');
  } finally {
    loading.value = false;
  }
};

// 地图点击事件
const onMapClick = (coord: number[]) => {
  form.longitude = String(coord[0]);
  form.latitude = String(coord[1]);
  mapMakerVisible.value = false;
};

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false;
  // 重置表单数据
  Object.assign(form, {
    assets_cover: '',
    assets_name: '',
    public_state: '',
    security_id: '',
    assets_tag: '',
    assets_remark: '',
    longitude: '',
    latitude: '',
    location_remark: '',
  });
};
</script>

<style lang="scss">
// 弹窗整体样式 - 使用全局样式
.add-resource-dialog {
  .el-dialog {
    border-radius: 8px;

    .el-dialog__header {
      border-radius: 8px;
      padding: 20px 24px 16px;
      border-bottom: 1px solid #e4e7ed;
      background: linear-gradient(135deg, #f5f7fa 0%, #ffffff 100%);

      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }
    }

    .el-dialog__body {
      border-radius: 8px;
      padding: 0;
      background-color: #fafbfc;
    }

    .el-dialog__footer {
      padding: 16px 24px 20px;
      border-top: 1px solid #e4e7ed;
      background: linear-gradient(135deg, #f5f7fa 0%, #ffffff 100%);
    }


  }
}

// 地图弹窗样式
.map-dialog {
  .el-dialog {
    .el-dialog__body {
      padding: 0;
    }
  }
}
</style>

<style scoped lang="scss">
// 弹窗内容区域
.dialog-content {
  padding: 15px;
  max-height: 70vh;
  overflow-y: auto;
  background-color: #fafbfc;
}

// 表单样式
.resource-form {
  .form-section {
    margin-bottom: 32px;

    &:last-child {
      margin-bottom: 0;
    }

    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 15px;

      .section-line {
        width: 4px;
        height: 16px;
        background-color: var(--el-color-primary, #A12F2F);
        border-radius: 2px;
        margin-right: 15px;
        margin-bottom: 5px;
      }

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        letter-spacing: 0.5px;
      }
    }

    .form-content {
      background-color: #ffffff;
      border-radius: 8px;
      padding: 20px 0px;
      padding-right: 15px;
      border: 1px solid #e4e7ed;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }
  }
}

// 封面上传区域
.cover-upload-area {
  .cover-form-item {
    :deep(.el-form-item__label) {
      font-weight: 500;
      color: #606266;
    }
  }

  .upload-container {
    .cover-uploader {
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: all 0.3s;

      &:hover {
        border-color: #409eff;
      }

      .upload-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100px;

        .upload-icon {
          font-size: 28px;
          color: #c0c4cc;
          margin-bottom: 8px;
        }

        .upload-text {
          font-size: 14px;
          color: #606266;
        }
      }
    }

    .upload-tip {
      font-size: 12px;
      color: #909399;
      text-align: left;
    }
  }
}

:deep(textarea) {
  resize: none !important;
}

// 表单字段区域
.form-fields {

  .form-input,
  .form-select,
  .form-textarea {
    width: 100%;

    :deep(.el-input__wrapper) {
      border-radius: 6px;
      box-shadow: 0 0 0 1px #dcdfe6 inset;
      transition: all 0.3s;

      &:hover {
        box-shadow: 0 0 0 1px #c0c4cc inset;
      }

      &.is-focus {
        box-shadow: 0 0 0 1px var(--el-color-primary, #A12F2F) inset;
      }
    }
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }
}

// 位置信息区域
.location-content {
  position: relative;

  .location-header {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 16px;

    .map-picker-btn {
      font-size: 14px;
      padding: 8px 16px;
      border-radius: 6px;
      transition: all 0.3s;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
      }
    }
  }
}

// 底部按钮
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  .cancel-btn {
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s;

    &:hover {
      background-color: #f5f7fa;
      border-color: #c0c4cc;
    }
  }

  .submit-btn {
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
    }
  }
}



// 表单项间距调整
:deep(.el-form-item) {
  margin-bottom: 18px;

  &:last-child {
    margin-bottom: 0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dialog-content {
    padding: 16px;
  }

  .form-content {
    padding: 16px !important;
  }

  .cover-upload-area {
    margin-bottom: 20px;
  }
}
</style>
