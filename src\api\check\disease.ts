import request from "/@/utils/request"

// 查询病害类型统计
export function queryDiseaseTypeForPC(query?: any) {
  return request({
    url: '/check/disease/queryDiseaseTypeForPC',
    method: 'get',
    params: query
  })
}

// 查询病害排行
export function queryDiseaseRank(query?: any) {
  return request({
    url: '/check/disease/queryDiseaseRank',
    method: 'get',
    params: query
  })
}

// 查询病害点位信息
export function queryDiseasePoint(query?: any) {
  return request({
    url: '/check/disease/queryDiseasePoint',
    method: 'get',
    params: query
  })
}

// 查询病害状况统计
export function queryDiseaseStatusForPC(query?: any) {
  return request({
    url: '/check/disease/queryDiseaseStatusForPC',
    method: 'get',
    params: query
  })
}

// 病害列表
export function queryDiseaseDisplayForPC(query?: any) {
  return request({
    url: '/check/disease/queryDiseaseDisplayForPC',
    method: 'get',
    params: query
  })
}

// 病害详情页 - 病害历史巡查事件列表

export function queryEventListByDiseaseId(query?: any) {
  return request({
    url: '/check/disease/queryEventListByDiseaseId',
    method: 'get',
    params: query
  })
}

// 同一个病害同一个点位根据对比分析

export function pictureCompare(query?: any) {
  return request({
    url: '/check/patrolFile/compare',
    method: 'get',
    params: query
  })
}

// 历史点位-病害分析 type 1 表示 变化量 2 表示 变化速度

export function lineByPointAndDisease(query?: any) {
  return request({
    url: '/check/diseaseInstance/lineByPointAndDisease',
    method: 'get',
    params: query
  })
}

// 查询建筑空间树
export function queryArchitectureTree(params: any) {
  return request({
    url: '/admin/sysArchitecturalSpace/getArchitecturalSpaceTree',
    method: 'get',
    params
  })
}

// 新增病害
export function saveForPC(data: any) {
  return request({
    url: '/check/disease/saveForPC',
    method: 'post',
    data,
  })
}

// 巡查上传
export function uploadFile(data: any) {
  return request({
    url: '/check/patrolFile/uploadFile',
    method: 'post',
    data,
  })
}