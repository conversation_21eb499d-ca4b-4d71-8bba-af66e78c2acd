<template>
  <div class="txt-viewer">
    {{ textData }}
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
const props = defineProps({
  fileUrl: {
    type: String,
    default: ''
  }
})
const textData = ref('');

onMounted(() => {
  createPreview(props.fileUrl)
})

const createPreview = (url: string) => {
  fetch(url).then(res => {
    if (res) {
      res.text().then(data => {
        textData.value = data || ""
      })
    }
  })
}
</script>

<style scoped lang="scss">
.txt-viewer {
  padding: 0 16px;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  white-space: pre-wrap;
}
</style>