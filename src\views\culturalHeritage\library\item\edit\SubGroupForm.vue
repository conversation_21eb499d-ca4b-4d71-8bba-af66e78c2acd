<template>
	<el-dialog v-model="visible" :title="form.id ? '编辑分组' : '新增分组'" width="500px" :close-on-click-modal="false"
		:close-on-press-escape="false" @close="onCancel">
		<el-form ref="formRef" :model="form" :rules="rules" label-width="100px" class="sub-group-form">
			<el-form-item label="分组名称" prop="groupName" required>
				<el-input v-model="form.groupName" placeholder="请输入分组名称" maxlength="50" show-word-limit />
			</el-form-item>

			<el-form-item label="所属条目" prop="parentGroupId" required>
				<el-select v-model="form.parentGroupId" placeholder="请选择所属条目" style="width: 100%" :disabled="!!form.id">
					<el-option v-for="item in formTypeGroups" :key="item.id" :label="item.groupName" :value="item.id" />
				</el-select>
			</el-form-item>

			<el-form-item label="分组序号" prop="groupSort" required>
				<div class="sort-order-input">
					<el-button @click="decreaseOrder" :disabled="form.groupSort <= 1" icon="Minus" size="small" />
					<el-input v-model.number="form.groupSort" placeholder="请输入序号" type="number" :min="1" :max="999"
						class="order-input" />
					<el-button @click="increaseOrder" :disabled="form.groupSort >= 999" icon="Plus" size="small" />
				</div>
			</el-form-item>

			<el-form-item label="备注">
				<el-input v-model="form.remark" type="textarea" placeholder="请输入描述" :rows="4" maxlength="200"
					show-word-limit />
			</el-form-item>
		</el-form>

		<template #footer>
			<div class="dialog-footer">
				<el-button @click="onCancel">取消</el-button>
				<el-button type="primary" @click="onConfirm" :loading="loading">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue';
import { useMessage } from '/@/hooks/message';
import { addFormColumnGroup, updateFormColumnGroup } from '/@/api/resource/catalog/columnInfo';

const emit = defineEmits(['refresh']);

const visible = ref(false);
const loading = ref(false);
const formRef: any = ref();

// 表单数据
const form = reactive({
	id: null as any,
	groupName: '',
	parentGroupId: null as any,
	groupSort: 1,
	remark: '',
	metaDataId: null as any,
	groupType: 2, // 分组类型，固定为2（表单类型的子分组）
});

// 表单验证规则
const rules = {
	groupName: [
		{ required: true, message: '请输入分组名称', trigger: 'blur' },
		{ min: 1, max: 50, message: '分组名称长度在1到50个字符', trigger: 'blur' }
	],
	parentGroupId: [
		{ required: true, message: '请选择所属条目', trigger: 'change' }
	],
	groupSort: [
		{ required: true, message: '请输入分组序号', trigger: 'blur' },
		{ type: 'number', min: 1, max: 999, message: '序号范围为1-999', trigger: 'blur' }
	]
};

// 表单类型的条目列表（只有表单类型才能新增分组）
const formTypeGroups = ref<any[]>([]);

// 序号操作
const decreaseOrder = () => {
	if (form.groupSort > 1) {
		form.groupSort--;
	}
};

const increaseOrder = () => {
	if (form.groupSort < 999) {
		form.groupSort++;
	}
};

// 打开弹窗
const openDialog = (record?: any, parentGroups?: any[]) => {
	visible.value = true;
	loading.value = false;

	// 设置表单类型的条目列表
	formTypeGroups.value = (parentGroups || []).filter(item => item.groupType === 2);

	// 重置表单
	Object.assign(form, {
		id: null,
		groupName: '',
		parentGroupId: null,
		groupSort: 1,
		remark: '',
		metaDataId: null
	});

	if (record) {
		if (record.id) {
			// 编辑模式
			Object.assign(form, {
				id: record.id,
				groupName: record.groupName,
				parentGroupId: record.parentGroupId,
				groupSort: record.groupSort || 1,
				remark: record.remark || ''
			});
		} else {
			// 新增模式，预设参数
			form.parentGroupId = record.parentGroupId;
			form.metaDataId = record.metaDataId;
		}
	}

	nextTick(() => {
		formRef.value?.clearValidate();
	});
};

// 确认提交
const onConfirm = async () => {
	try {
		await formRef.value?.validate();
	} catch {
		return;
	}

	loading.value = true;
	try {
		const submitData = {
			id: form.id,
			groupName: form.groupName,
			parentGroupId: form.parentGroupId,
			groupSort: form.groupSort,
			remark: form.remark
		};

		if (form.id) {
			await updateFormColumnGroup(submitData);
			useMessage().success('编辑分组成功');
		} else {
			await addFormColumnGroup(submitData);
			useMessage().success('新增分组成功');
		}

		visible.value = false;
		emit('refresh');
	} catch (err: any) {
		useMessage().error(err.msg || '操作失败');
	} finally {
		loading.value = false;
	}
};

// 取消
const onCancel = () => {
	visible.value = false;
};

// 暴露方法
defineExpose({
	openDialog,
});
</script>

<style scoped lang="scss">
.sub-group-form {
	.sort-order-input {
		display: flex;
		align-items: center;
		gap: 8px;

		.order-input {
			flex: 1;
			text-align: center;

			:deep(.el-input__inner) {
				text-align: center;
			}
		}
	}
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 12px;
}
</style>
