<template>
  <div class="palace-form">
    <el-form ref="formRef" :model="formData" :rules="formRules" :label-position="'top'" label-width="100px" class="form-content">
      <!-- 基本信息 -->
      <div class="form-section">
        <h4 class="section-title">
          <el-icon><OfficeBuilding /></el-icon>
          基本信息
        </h4>

        <el-form-item label="宫观名称" prop="palaceName" required>
          <el-input v-model="formData.palaceName" placeholder="请输入名称" maxlength="50" show-word-limit />
        </el-form-item>

        <el-form-item label="宫观序号" prop="palaceOrder" required>
          <el-input-number
            v-model="formData.palaceOrder"
            :min="1"
            :max="999"
            placeholder="请输入"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="视频链接" prop="videoUrl" required>
          <el-input v-model="formData.videoUrl" placeholder="请输入视频链接" />
        </el-form-item>
      </div>

      <!-- 讲解配置 -->
      <div class="form-section">
        <h4 class="section-title">
          <el-icon><Microphone /></el-icon>
          讲解配置
        </h4>

        <el-form-item label="是否讲解" prop="hasCommentary" required>
          <el-radio-group v-model="formData.hasCommentary">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>

        <template v-if="formData.hasCommentary">
          <el-form-item label="数字人形象" prop="avatarType">
            <el-select v-model="formData.avatarType" placeholder="请选择数字人" style="width: 100%">
              <el-option label="数字人1" value="avatar1" />
              <el-option label="数字人2" value="avatar2" />
              <el-option label="数字人3" value="avatar3" />
            </el-select>
          </el-form-item>

          <el-form-item label="讲解内容" prop="commentaryContent">
            <el-input
              v-model="formData.commentaryContent"
              type="textarea"
              placeholder="请输入内容"
              :rows="4"
              maxlength="1000"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="讲解音频">
            <div class="upload-container">
              <el-button icon="UploadFilled" @click="handleAudioUpload">上传文件</el-button>
              <div class="upload-hint">支持MP3/WAV格式，且最大20M</div>
            </div>
          </el-form-item>
        </template>
      </div>

      <!-- 评论配置 -->
      <div class="form-section">
        <h4 class="section-title">
          <el-icon><ChatDotRound /></el-icon>
          评论配置
        </h4>

        <el-form-item label="是否允许评论" prop="allowComments" required>
          <el-radio-group v-model="formData.allowComments">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="是否展示评论" prop="showComments" required>
          <el-radio-group v-model="formData.showComments">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { OfficeBuilding, Microphone, ChatDotRound } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';

interface PalaceFormData {
  palaceName: string;
  palaceOrder: number;
  videoUrl: string;
  hasCommentary: boolean;
  avatarType: string;
  commentaryContent: string;
  audioFileId: string;
  audioUrl: string;
  allowComments: boolean;
  showComments: boolean;
}

interface Emits {
  (e: 'data-change', data: PalaceFormData): void;
}

const emit = defineEmits<Emits>();

// 响应式数据
const formRef = ref();

const formData = reactive<PalaceFormData>({
  palaceName: '',
  palaceOrder: 1,
  videoUrl: '',
  hasCommentary: true,
  avatarType: '',
  commentaryContent: '',
  audioFileId: '',
  audioUrl: '',
  allowComments: true,
  showComments: true
});

// 表单验证规则
const formRules = {
  palaceName: [
    { required: true, message: '请输入宫观名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  palaceOrder: [
    { required: true, message: '请输入宫观序号', trigger: 'blur' }
  ],
  videoUrl: [
    { required: true, message: '请输入视频链接', trigger: 'blur' }
  ],
  hasCommentary: [
    { required: true, message: '请选择是否讲解', trigger: 'change' }
  ],
  allowComments: [
    { required: true, message: '请选择是否允许评论', trigger: 'change' }
  ],
  showComments: [
    { required: true, message: '请选择是否展示评论', trigger: 'change' }
  ]
};

// 监听表单数据变化
watch(formData, (newData) => {
  emit('data-change', { ...newData });
}, { deep: true });

// 音频上传
const handleAudioUpload = () => {
  ElMessage.info('音频上传功能待实现');
};

// 获取表单数据
const getFormData = () => {
  return { ...formData };
};

// 设置表单数据
const setFormData = (data: Partial<PalaceFormData>) => {
  Object.assign(formData, data);
};

// 验证表单
const validateForm = async () => {
  if (!formRef.value) return false;
  try {
    await formRef.value.validate();
    return true;
  } catch {
    return false;
  }
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  Object.assign(formData, {
    palaceName: '',
    palaceOrder: 1,
    videoUrl: '',
    hasCommentary: true,
    avatarType: '',
    commentaryContent: '',
    audioFileId: '',
    audioUrl: '',
    allowComments: true,
    showComments: true
  });
};

// 暴露方法
defineExpose({
  getFormData,
  setFormData,
  validateForm,
  resetForm
});
</script>

<style lang="scss" scoped>
.palace-form {
  padding: 0 20px;

  .form-content {
    .form-section {
      margin-bottom: 24px;

      .section-title {
        display: flex;
        align-items: center;
        margin: 0 0 16px 0;
        font-size: 14px;
        font-weight: 600;
        color: #1d2129;
      }
    }

    .upload-container {
      .upload-hint {
        margin-top: 8px;
        font-size: 12px;
        color: #86909c;
      }
    }
  }
}

:deep(.el-form-item__label) {
  color: #1d2129;
  font-weight: 500;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
}

:deep(.el-input-number) {
  width: 100%;
}
</style>