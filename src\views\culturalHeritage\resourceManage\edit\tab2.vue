<template>
  <div style="width: 100%; height: calc(100% - 50px); position: relative;" v-if="isLoaded">
    <div class="layout-padding-auto content-box" style="padding: 0px;">
      <div class="layout-padding-auto layout-padding-view cb-top" style="border-radius: 0px 0px 24px 24px;">
        <div class="cbt-left">
          <div class="cbtl-img">
            <ImageUpload v-model:imageUrl="baseInfo.datas.assets_cover" :disabled="props.editCode == 'view'" borderRadius="0%" width="140px" height="140px" @uploadSuccess="updateBaseInfo" uploadFileUrl="/datacenter/learning/material/cover">
              <template #empty>
                <el-icon><Picture /></el-icon>
                <span>请上传封面</span>
              </template>
            </ImageUpload>
          </div>
          <div class="cbtl-info">
            <div class="cbtli-title" style="margin-left: -10px;">【{{ baseInfo.datas.assets_num }}】{{ baseInfo.datas.assets_name }}</div>
            <div class="cbtli-type">
              <el-icon><Folder /></el-icon>
              <span :style="{ color: baseInfo.datas.public_state == 1 ? '#7BDA6B' : '#FD4949' }">{{ baseInfo.datas.public_state == 1 ? "公开" : "私有" }}</span>
            </div>
            <div class="cbtli-type">
              <el-icon><Guide /></el-icon>
              <span>{{ baseInfo.extraData.assets_num_name }}</span>
            </div>
            <div class="cbtli-tags mt-button">
              <el-tag v-for="(tag, index) in assets_tag_list" :key="tag" effect="plain" :closable="props.editCode == 'edit'" @close="assets_tag_handleClose(tag)">
                <span class="tag-radius" :style="{ backgroundColor: colorList[index % colorList.length] }"></span>
                {{ tag }}
              </el-tag>
              <el-input v-if="assets_tag_inputVisible && props.editCode == 'edit'" ref="assets_tag_InputRef" v-model="assets_tag_inputValue" style="width: 80px;" size="small" @keyup.enter="assets_tag_handleInputConfirm" @blur="assets_tag_handleInputConfirm" />
              <el-button v-else-if="!assets_tag_inputVisible && props.editCode == 'edit'" size="small" type="primary" style="margin-bottom: 3px;" plain @click="assets_tag_showInput">+ 新增</el-button>
            </div>
          </div>
        </div>
        <div class="cbt-right">
          <el-row :gutter="24">
            <el-col :span="12">
              <div class="cbtr-box">
                <div class="cbtr-key">所属单位</div>
                <div class="cbtr-value">{{ baseInfo.extraData.create_dept || "--" }}</div>
              </div>
              <div class="cbtr-box">
                <div class="cbtr-key">资源密级</div>
                <div class="cbtr-value">{{ baseInfo.extraData.security_name || "--" }}</div>
              </div>
              <div class="cbtr-box">
                <div class="cbtr-key">创建人</div>
                <div class="cbtr-value">{{ baseInfo.extraData.create_user || "--" }}</div>
              </div>
              <div class="cbtr-box">
                <div class="cbtr-key">创建时间</div>
                <div class="cbtr-value">{{ baseInfo.datas.create_time || "--" }}</div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="cbtr-box">
                <div class="cbtr-key">资源定位</div>
                <div class="cbtr-value">{{ baseInfo.datas.longitude && baseInfo.datas.latitude ? baseInfo.datas.longitude + "，" + baseInfo.datas.latitude : "--" }}</div>
              </div>
              <div class="cbtr-box">
                <div class="cbtr-key">描述</div>
                <div class="cbtr-value">{{ baseInfo.datas.assets_remark || "--" }}</div>
              </div>
              <div class="cbtr-box">
                <div class="cbtr-key">更新人</div>
                <div class="cbtr-value">{{ baseInfo.extraData.update_user || "--" }}</div>
              </div>
              <div class="cbtr-box">
                <div class="cbtr-key">更新时间</div>
                <div class="cbtr-value">{{ baseInfo.datas.update_time || "--" }}</div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
      <div class="layout-padding-auto cb-center" style="padding: 0px 10px 0px 0px;">
        <div class="layout-padding-auto layout-padding-view layout-border-radius">
          <div class="cbc-progress">
            <span>质量合格率</span>
            <span class="cbcp-val">100%</span>
            <el-progress :percentage="100" :stroke-width="10" :show-text="false" class="mt-button" />
          </div>
          <div class="cbc-anchor">
            <div class="cbca-select" v-for="item in groupList" :key="item.id" :class="{ active: select_group?.id == item.id }" @click="shouGroupDetail(item)">{{ item.groupName }}</div>
          </div>
        </div>
        <div class="layout-padding-auto layout-padding-view layout-border-radius">
          <div class="btn-box mt-button" v-show="props.editCode == 'edit'">
            <el-button size="small" icon="Edit" text type="primary" v-show="!isEditor" @click="onOperation('DoEdit')">编辑</el-button>
            <el-button size="small" icon="Select" text type="primary" v-show="isEditor" @click="onOperation('DoSave')">保存</el-button>
            <el-button size="small" icon="CloseBold" text type="primary" v-show="isEditor" @click="isEditor = false">取消</el-button>
          </div>
          <div v-if="select_group" style="padding: 5px 0px; height: 100%;" v-loading="!select_group">
            <el-col :span="24" class="mb0">
              <div class="form-subTitle mb0">
                <div class="split-line"></div>
                {{ select_group.groupName }}
              </div>
            </el-col>
            <el-col :span="24" class="mb0" style="height: calc(100% - 40px);" :style="{ padding: props.editCode == 'edit' ? '10px 10px 0px 10px' : '0px 10px 0px 10px' }">
              <div class="layout-form-view layout-border-radius mb10" style="height: 100%; overflow-y: auto; margin-top: 10px;">
                <CustomFieldForm ref="customFieldRef" v-if="select_group.groupType == 2 && select_group.columnList.length > 0" :colSpan="2" :canEditor="isEditor" :columnList="select_group.columnList" />
                <CustomFieldTable ref="customFieldRef" v-else-if="select_group.groupType == 1 && select_group.columnList.length > 0" :colSpan="2" :canEditor="isEditor" :columnList="select_group.columnList" :dataList="select_group.tableData" />
                <el-empty v-else description="该目录暂无元数据字段" />
              </div>
            </el-col>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { ref } from "vue";
  import { useMessage, useMessageBox } from "/@/hooks/message";
  import { getModelDataById, getColumnInfo, updateModelData } from "/@/api/resource/data/resource";
  import { listColumnGroup } from "/@/api/resource/catalog/columnInfo";
  import { useRoute } from 'vue-router'
  const ImageUpload = defineAsyncComponent(() => import("/@/components/Upload/Image.vue"));
  const CustomFieldForm = defineAsyncComponent(() => import("./CustomFieldForm.vue"));
  const CustomFieldTable = defineAsyncComponent(() => import("./CustomFieldTable.vue"));
  const route = useRoute()
  const emit = defineEmits(["backTo"]);
  const props = defineProps({
    editCode: {
      type: String,
      default: null,
    },
    editRecord: {
      type: Object,
      default: null,
    },
  });
  const isLoaded = ref(false);
  const loading = ref(false);
  const isEditor = ref(false);
  const baseInfo: any = ref({ data: {}, extraData: {} });
  const groupList: any = ref([]);
  const customFieldRef: any = ref();
  const select_group: any = ref(null);
  const assets_tag_list: any = ref([]);
  const assets_tag_inputVisible: any = ref();
  const assets_tag_InputRef: any = ref();
  const assets_tag_inputValue: any = ref();
  const colorList = ["#F1B739", "#737BD7", "#5B89FF", "#FF5B5B", "#0093D2", "#5B7FFF", "#FF835B", "#00A389", "#AB54DB", "#FFAE35"];
  const backTo = () => {
    emit("backTo");
  };
  const assets_tag_handleClose = (tag: string) => {
    assets_tag_list.value.splice(assets_tag_list.value.indexOf(tag), 1);
    updateBaseInfo();
  };
  const assets_tag_showInput = () => {
    assets_tag_inputVisible.value = true;
    nextTick(() => {
      assets_tag_InputRef.value!.input!.focus();
    });
  };
  const assets_tag_handleInputConfirm = () => {
    if (assets_tag_inputValue.value) {
      assets_tag_list.value.push(assets_tag_inputValue.value);
    }
    assets_tag_inputVisible.value = false;
    assets_tag_inputValue.value = "";
    updateBaseInfo();
  };
  const updateBaseInfo = async () => {
    let obj_edit: any = {
      tableName: props.editRecord.tableName,
      id: props.editRecord.resourceId,
      datas: Object.assign(baseInfo.value.datas, {
        assets_cover: baseInfo.value.datas.assets_cover,
        assets_tag: assets_tag_list.value?.join("，"),
      }),
      extraData: baseInfo.value.extraData,
      extraMap: {
        security_name: baseInfo.value.extraData.security_name,
      },
      groupMap: {},
    };
    try {
      await updateModelData(obj_edit);
    } catch (err) {
      useMessage().error((err as any).msg);
    } finally {
    }
  };
  const shouGroupDetail = (item: any) => {
    select_group.value = null;
    nextTick(() => {
      getColumnInfo({ resourceId: props.editRecord.resourceId, tabName: props.editRecord.tableName, groupId: item.id }, route.query.searchFlag).then((res) => {
        item.columnList = res.data;
        item.columnList.forEach((obj: any) => {
          if (item.groupType == 2) {
            // 表单
            if (["dateRange", "multiOption", "image", "attach"].includes(obj.typeCode) && obj.object?.length > 0 && typeof obj.object == "string") {
              obj.object = JSON.parse(obj.object);
            }
          } else {
            // 列表
            if (["dateRange", "multiOption", "image", "attach"].includes(obj.typeCode) && obj.object?.length > 0) {
              let object_new: any = [];
              obj.object.forEach((a: any) => {
                if (a?.length > 0 && typeof a == "string") {
                  object_new.push(JSON.parse(a));
                } else {
                  object_new.push(a);
                }
              });
              obj.object = object_new;
            }
          }
        });
        if (item.groupType == 1) {
          let tableData: any = [];
          let length = item.columnList[0]?.object?.length || 0;
          for (let i = 0; i < length; i++) {
            let obj_tb: any = {};
            item.columnList.forEach((obj: any) => {
              obj_tb[obj.name] = obj.object[i];
            });
            tableData.push(obj_tb);
          }
          item.tableData = tableData;
        }
        select_group.value = item;
        console.log('item~~~~~~~~234', item)
      });
    });
  };
  const onOperation = async (type: string) => {
    switch (type) {
      case "DoEdit":
        isEditor.value = true;
        break;
      case "DoSave":
        let obj_edit: any = {
          tableName: props.editRecord.tableName,
          id: props.editRecord.resourceId,
          datas: {},
          extraData: baseInfo.value.extraData,
          extraMap: {
            security_name: baseInfo.value.extraData.security_name,
          },
          groupMap: {},
        };
        let isvalid = await customFieldRef.value.validate();
        if (!isvalid) return false;
        let data = JSON.parse(JSON.stringify(customFieldRef.value.getData()));
        if (select_group.value.groupType == 2) {
          for (let k in data) {
            if (Array.isArray(data[k])) data[k] = JSON.stringify(data[k]);
          }
          // 表单
          obj_edit.datas = data;
        } else {
          // 列表
          data.forEach((obj: any) => {
            for (let k in obj) {
              if (Array.isArray(obj[k])) obj[k] = JSON.stringify(obj[k]);
            }
          });
          obj_edit.groupMap[select_group.value.id] = data;
        }
        isEditor.value = false;
        try {
          loading.value = true;
          obj_edit.datas = Object.assign(baseInfo.value.datas, obj_edit.datas);
          console.log('obj_edit~~~~~~~~~', obj_edit)
          await updateModelData(obj_edit);
          useMessage().success("更新成功");
        } catch (err) {
          useMessage().error((err as any).msg);
        } finally {
          loading.value = false;
        }
        break;
    }
  };
  onMounted(() => {
    isEditor.value = props.editCode == "view" ? false : true;
    nextTick(async () => {
      let res_baseInfo = await getModelDataById({ resourceId: props.editRecord.resourceId, tableName: props.editRecord.tableName }, route.query.searchFlag);
      baseInfo.value = res_baseInfo.data;
      assets_tag_list.value = baseInfo.value.datas.assets_tag?.trim().length > 0 ? baseInfo.value.datas.assets_tag.split("，") : [];
      let res_groupList = await listColumnGroup({ tableName: props.editRecord.tableName });
      groupList.value = res_groupList.data.filter((obj: any) => obj.defaultGroup != 1);
      groupList.value[0] && shouGroupDetail(groupList.value[0]);
      isLoaded.value = true;
    });
  });
</script>

<style scoped lang="scss">
  .content-box {
    height: 100%;
    width: 100%;
    display: grid;
    grid-template-columns: 100%;
    grid-template-rows: 180px calc(100% - 180px);
    grid-column-gap: 0px;
    grid-row-gap: 10px;
    .cb-top {
      position: relative;
      height: 100%;
      width: 100%;
      .cbt-left {
        margin: 10px 0px;
        position: absolute;
        height: calc(100% - 20px);
        width: 590px;
        left: 0px;
        top: 0px;
        display: flex;
        align-items: center;
        .cbtl-img {
          width: 220px;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          float: left;
        }
        .cbtl-info {
          width: calc(100% - 220px);
          max-height: calc(100% + 0px);
          float: left;
          overflow-y: auto;
          .cbtli-title {
            font-size: 17px;
            font-weight: bolder;
            letter-spacing: 0em;
          }
          .cbtli-type {
            height: 30px;
            display: flex;
            align-items: center;
            .el-icon {
              font-size: 18px;
              color: #919191;
              margin-right: 10px;
            }
            span {
              color: #2b2b2b;
            }
          }
          .cbtli-tags {
            width: 100%;
            margin-top: 10px;
            .el-tag {
              padding: 10px 10px;
              margin-right: 10px;
              margin-bottom: 10px;
              font-size: 14px;
              font-weight: bolder;
              border: 1px solid #a3a3a3;
              border-radius: 10px;
              color: #a3a3a3;
              .tag-radius {
                border-radius: 100%;
                width: 10px;
                height: 10px;
                float: left;
                margin-top: 2px;
                margin-right: 3px;
              }
              ::v-deep(.el-tag__close) {
                --el-tag-text-color: #FD4949;
                border: 1px solid var(--el-tag-text-color);
              }
            }
          }
        }
      }
      .cbt-right {
        position: absolute;
        height: 100%;
        width: calc(100% - 590px);
        right: 0px;
        top: 0px;
        padding: 10px 0px;
        padding-left: 30px;
        overflow-y: auto;
        .el-row {
          width: 100%;
          height: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          .el-col {
            .cbtr-box {
              height: 35px;
              line-height: 35px;
              width: 100%;
              .cbtr-key {
                font-weight: 600;
                float: left;
                color: #a3a3a3;
              }
              .cbtr-value {
                float: right;
                padding-right: 50px;
                font-weight: 600;
              }
            }
          }
        }
      }
    }
    .cb-center {
      height: calc(100% - 7px);
      width: 100%;
      display: grid;
      grid-template-columns: 280px calc(100% - 280px);
      grid-template-rows: 100%;
      grid-column-gap: 10px;
      grid-row-gap: 0px;
      .cbc-progress {
        padding: 0px 10px;
        span {
          font-size: 15px;
          margin-right: 10px;
          margin-bottom: 10px;
          letter-spacing: 0.1em;
        }
        .cbcp-val {
          font-size: 16px;
          font-weight: bolder;
          letter-spacing: 0.1em;
          color: #fd4949;
        }
        .el-progress {
          margin-top: 10px;
          --el-color-primary: #fd4949;
        }
      }
      .cbc-anchor {
        // margin-top: 20px;
        padding: 0px 10px;
        max-height: 450px;
        overflow-y: auto;
        .cbca-select {
          border: 1px solid #a5a5a56b;
          margin-top: 20px;
          display: flex;
          align-items: center;
          justify-content: left;
          height: 50px;
          border-radius: 5px;
          font-size: 16px;
          letter-spacing: 0.1em;
          cursor: pointer;
          text-indent: 30px;
          color: #000000;
          font-weight: 500;
          &.active {
            border: 1px solid #fd494900;
            color: #fd4949;
            background-color: #fbdfdf;
          }
          &:hover {
            background-color: #fbdfdf54;
          }
        }
      }
      .layout-padding-view {
        position: relative;
        .btn-box {
          position: absolute;
          top: 15px;
          right: 15px;
          z-index: 99;
          .el-button {
            margin-bottom: 5px;
            z-index: 9;
          }
        }
        .el-form {
          position: relative;
          ::v-deep(.el-form-item__label) {
            font-weight: bolder;
          }
        }
      }
    }
  }
  ::v-deep(.el-form-item__label) {
    color: #a3a3a3;
  }
</style>
