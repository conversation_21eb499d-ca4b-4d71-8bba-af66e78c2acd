<template>
  <div class="hot-resources-container">
    <div class="title">热门资源</div>
    <el-carousel height="660px" :autoplay="false" arrow="never">
      <el-carousel-item v-for="(item, idx) in hotResourceList" :key="idx">
        <div class="resources-grid">
          <!-- 左侧大卡片 -->
          <div class="large-card" @click="handleClick(item[0])">
            <div class="img-box">
              <div class="resource-image" style="border-radius: 8px">
                <img :src="item[0]?.assets_cover || noCoverImg" alt="" :class="{ 'no-cover': !item[0]?.assets_cover }" @error="onImgError" />
              </div>
              <div class="icon-box" v-if="item[0]?.collected">
                <img src="/@/assets/img/squareNew/collectIcon1.png" alt="" />
              </div>
            </div>
            <div class="card-info">
              <div class="resource-tag">
                <div class="tag" v-for="(tag, index) in getTags(item[0])" :key="'tag' + index">{{ tag }}</div>
                <div class="view-count">
                  <i src="" alt="" />
                  <span>{{ item[0]?.view_count || 0 }}</span>
                </div>
              </div>
              <div class="resource-title">{{ item[0]?.assets_name }}</div>
              <div class="resource-meta">
                <div class="resource-size">{{ item[0]?.third_catalog_name?.split(",").join(' > ') }}</div>
              </div>
            </div>
          </div>
          <!-- 右侧小卡片列表 -->
          <div class="small-cards">
            <div
              class="small-card-item"
              v-for="i in item.slice(1, 5)"
              :key="i"
              @click="handleClick(i)"
            >
              <div class="img-box">
                <div class="resource-image">
                  <img :src="i?.assets_cover || noCoverImg" alt="" :class="{ 'no-cover': !i?.assets_cover }" @error="onImgError" />
                </div>
                <div class="icon-box" v-if="i?.collected">
                  <img src="/@/assets/img/squareNew/collectIcon1.png" alt="" />
                </div>
              </div>
              <div class="card-info">
                <div class="resource-tag">
                  <div class="tag" v-for="(tag, index) in getTags(i)" :key="'tag' + index">{{ tag }}</div>
                </div>
                <div class="resource-title">{{ i?.assets_name }}</div>
                <div class="resource-meta">
                  <div class="resource-size">{{ i?.third_catalog_name?.split(",").join(' > ') }}</div>
                  <div class="view-count">
                    <i src="" alt="" />
                    <span>{{ i.view_count }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-carousel-item>
    </el-carousel>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { getHotResource } from "/@/api/squareNew/index";
import noCoverImg from "/@/assets/img/squareNew/no_cover.png";

const hotResourceList = ref<any>([]);
const emit = defineEmits(['goResourceDetail']);

const handleClick = (record: any) => {
  emit('goResourceDetail', record)
};

function chunkArray(arr: any[], size: number) {
  if (!Array.isArray(arr) || size <= 0) return [];
  const result = [];
  for (let i = 0; i < arr.length; i += size) {
    result.push(arr.slice(i, i + size));
  }
  return result;
}

// 查询热门资源
const queryHotResource = () => {
  getHotResource().then((res) => {
    if (res.ok) {
      hotResourceList.value = chunkArray(res.data, 5);
    }
  });
};

onMounted(() => {
  queryHotResource();
});

const onImgError = (e: any) => {
  e.target.src = noCoverImg;
  e.target.classList.add("no-cover");
};
// 处理标签
const getTags = (item: any) => {
  let tags = []
  if (item.assets_tag) {
    tags = item.assets_tag.split(/[,，]/);
    tags = tags.slice(0, 5)
  }
  return tags;
};

</script>

<style scoped lang="scss">
.hot-resources-container {
  display: flex;
  flex-direction: column;
  margin-top: 100px;

  .title {
    font-family: Source Han Serif CN;
    font-weight: 700;
    font-size: 40px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
    color: #141414;
    margin-bottom: 45px;
  }

  :deep(.el-carousel) {
    padding-bottom: 50px;
    .el-carousel__indicators {
      z-index: 99;
      .el-carousel__indicator {
        padding: 0;
        .el-carousel__button {
          background-image: url("/@/assets/img/squareNew/dot.png");
          background-size: 100% 100%;
          width: 24px;
          height: 24px;
          opacity: 1;
          background-color: transparent;
          margin-right: 16px;
        }
        &:last-child {
          .el-carousel__button {
            margin-right: 0;
          }
        }
      }
      .is-active {
        .el-carousel__button {
          background-image: url("/@/assets/img/squareNew/dotCur.png");
        }
      }
    }
  }

  .resources-grid {
    display: flex;
    gap: 20px;
    width: 100%; // 根据图片调整总宽度

    .large-card {
      width: 50%; // 根据图片调整
      height: 660px; // 根据图片调整
      width: 50%; // 根据图片调整
      background: #fff;
      overflow: hidden;
      position: relative;
      padding: 20px 20px 27px;
      cursor: pointer;
      border: 1px solid rgba(0, 0, 0, 0.10);
      transition: all 0.3s;
      &:hover{
        box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.15);
      }
      .img-box {
        height: calc(100% - 90px);
        position: relative;

        .resource-image {
          width: 100%;
          height: 100%;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: center;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
          .no-cover {
            width: 200px;
            height: auto;
            object-fit: contain;
          }
        }

        .icon-box {
          position: absolute;
          right: 9px;
          bottom: 7px;
          display: flex;
          > img {
            width: 28px;
            height: 28px;
            margin-right: 10px;
            &:last-child {
              margin-right: 0;
            }
          }
        }
      }

      .card-info {
        background: rgba(255, 255, 255, 0.9);
        margin-top: 13px;
        display: flex;
        flex-direction: column;

        .resource-tag {
          display: flex;
          align-items: center;
          margin-bottom: 10px;
          height: 24px;

          .tag {
            background-color: #DCB171;
            color: white;
            padding: 2px 4px;
            border-radius: 2px;
            align-self: flex-start;
            font-family: Source Han Serif CN;
            font-weight: 500;
            font-size: 13px;
            height: 24px;
            text-align: center;
            box-sizing: border-box;
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            &+.tag {
              margin-left: 8px; 
            }
          }

          .view-count {
            display: flex;
            align-items: center;
            flex-grow: 1;
            justify-content: flex-end;

            i {
              width: 24px;
              height: 24px;
              display: flex;
              align-items: center;
              margin-right: 4px;
              &::before {
                content: "";
                display: inline-block;
                width: 22px;
                height: 15px;
                background: url("/@/assets/img/squareNew/eyePrimaryIcon.png") no-repeat
                  center;
                background-size: contain;
              }
            }
            > span {
              font-family: Source Han Sans CN;
              font-weight: 500;
              font-size: 16px;
              line-height: 22.54px;
              letter-spacing: 0%;
              color: rgba(134, 38, 38, 1);
            }
          }
        }

        .resource-title {
          margin-bottom: 2px;
          font-family: Source Han Sans CN;
          font-weight: 700;
          font-size: 20px;
          line-height: 100%;
          letter-spacing: 0%;
          vertical-align: middle;
          color: #862626;
          margin-bottom: 8px;
        }

        .resource-meta {
          font-size: 12px;
          color: #666;
          display: flex;
          font-weight: 400;
          font-size: 14px;
          display: flex;
          align-items: center;

          .resource-size {
            flex-grow: 1;
            font-family: Source Han Sans CN;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0px;
            color: #262626b2;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
        }
      }
    }

    .small-cards {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-gap: 20px;
      width: 50%;

      .small-card-item {
        width: 100%;
        height: 320px;
        background: #fff;
        overflow: hidden;
        position: relative;
        padding: 20px;
        cursor: pointer;
        border: 1px solid rgba(0, 0, 0, 0.10);
        transition: all 0.3s;
        &:hover{
          box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.15);
        }
        .img-box {
          height: calc(100% - 90px);
          position: relative;
          .resource-image {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
            .no-cover {
              width: 200px;
              height: auto;
              object-fit: contain;
            }
          }
          .icon-box {
            position: absolute;
            right: 9px;
            bottom: 7px;
            display: flex;
            > img {
              width: 28px;
              height: 28px;
              margin-right: 10px;
              &:last-child {
                margin-right: 0;
              }
            }
          }
        }

         .card-info {
          background: rgba(255, 255, 255, 0.9);
          margin-top: 12px;
          display: flex;
          flex-direction: column;
  
          .resource-tag {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            height: 24px;
  
            .tag {
              background-color: #DCB171;
              color: white;
              padding: 2px 4px;
              border-radius: 2px;
              align-self: flex-start;
              font-family: Source Han Serif CN;
              font-weight: 500;
              font-size: 13px;
              height: 24px;
              text-align: center;
              box-sizing: border-box;
              max-width: 100%;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              &+.tag {
                margin-left: 8px; 
              }
            }
          }
  
          .resource-title {
            font-family: Source Han Sans CN;
            font-weight: 700;
            font-size: 16px;
            line-height: 100%;
            letter-spacing: 0%;
            vertical-align: middle;
            color: #862626;
            margin-bottom: 8px;
          }
  
          .resource-meta {
            font-size: 12px;
            color: #666;
            display: flex;
            font-weight: 400;
            font-size: 14px;
            display: flex;
            align-items: center;
  
            .resource-size {
              flex-grow: 1;
              font-family: Source Han Sans CN;
              font-weight: 400;
              font-size: 14px;
              line-height: 20px;
              letter-spacing: 0px;
              color: #262626b2;
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
              max-width: 80%;
            }
  
            .view-count {
              display: flex;
              align-items: center;
              flex-grow: 1;
              justify-content: flex-end;
  
              i {
                width: 24px;
                height: 24px;
                display: flex;
                align-items: center;
                margin-right: 4px;
                &::before {
                  content: "";
                  display: inline-block;
                  width: 22px;
                  height: 15px;
                  background: url("/@/assets/img/squareNew/eyePrimaryIcon.png") no-repeat
                    center;
                  background-size: contain;
                }
              }
              > span {
                font-family: Source Han Sans CN;
                font-weight: 500;
                font-size: 16px;
                line-height: 22.54px;
                letter-spacing: 0%;
                color: rgba(134, 38, 38, 1);
              }
            }
          }
        }
      }
    }
  }

  .pagination-dots {
    display: flex;
    margin-top: 30px;

    .dot {
      width: 8px;
      height: 8px;
      background-color: #dcdfe6;
      border-radius: 50%;
      margin: 0 5px;
      cursor: pointer;

      &.active {
        background-color: #333;
      }
    }
  }
}
</style>
