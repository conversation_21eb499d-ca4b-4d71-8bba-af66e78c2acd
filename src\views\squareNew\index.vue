<script setup lang="ts">
import { ArrowRight } from "@element-plus/icons-vue";
import { useRoute } from "vue-router";

const navMenu = defineAsyncComponent(() => import("./nav/index.vue"));
const Footer = defineAsyncComponent(() => import("./components/footer.vue"));
const route = useRoute();

watch(
  () => route.path,
  () => {
    // 切换路由时页面滚动到顶部
    document.querySelector(".square-main-content")?.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }
);

// 页面面包屑
const breadcrumbList = computed(() => {
  if (route.path == "/squareNew/index") {
    return [];
  } else if (route.path == "/squareNew/resource") {
    return [
      { label: "首页", to: "/squareNew/index" },
      { label: "资源列表", to: "/squareNew/resource" },
    ];
  } else if (route.path.includes("/squareNew/resource/detail")) {
    return [
      { label: "首页", to: "/squareNew/index" },
      { label: "资源列表", to: "/squareNew/resource" },
      { label: "资源详情", to: "" },
    ];
  } else if (route.path == "/squareNew/personCenter") {
    return [
      { label: "首页", to: "/squareNew/index" },
      { label: "个人中心", to: "" },
    ];
  } else {
    return [];
  }
});
</script>

<template>
  <div class="square-container">
    <navMenu></navMenu>
    <div class="square-main-content">
      <el-breadcrumb :separator-icon="ArrowRight" v-if="breadcrumbList.length">
        <el-breadcrumb-item
          v-for="(item, index) in breadcrumbList"
          :to="item.to"
          :key="index"
          >{{ item.label }}</el-breadcrumb-item
        >
      </el-breadcrumb>
      <router-view />
      <Footer />
    </div>
  </div>
</template>

<style scoped lang="scss">
.square-container {
  width: 100%;
  height: 100%;
  .square-main-content {
    height: calc(100% - 66px);
    overflow: auto;
    background-color: rgba(245, 245, 245, 1);

    :deep(.el-breadcrumb) {
      min-width: 1400px;
      margin: 0 210px;
      background: #fff;
      padding: 20px;
      margin-top: 15px;

      .home {
        position: relative;
        &::before {
          content: "";
          // background-image: url("/@/assets/img/squareNew/breadIcon.png");
          background-repeat: no-repeat;
          background-size: 15px 15px;
          background-position: center;
          width: 24px;
          height: 24px;
          display: inline-block;
        }
      }
      .el-breadcrumb__item {
        cursor: pointer;
        > span {
          font-family: Source Han Serif CN;
          font-weight: 500;
          font-size: 14px;
          line-height: 24.61px;
          letter-spacing: 0%;
          color: #393939;
        }
        .el-breadcrumb__separator {
          background-image: url("/@/assets/img/squareNew/icon_double_right.png");
          background-size: 100% 100%;
          width: 20px;
          height: 20px;
          svg {
            display: none;
          }
        }
      }
    }
  }
}
</style>
