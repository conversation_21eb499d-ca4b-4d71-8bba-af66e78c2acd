<template>
  <div class="file_viewer">
    <div v-if="format == 'txt'" class="mt-box" style="white-space: pre-wrap">
      {{ textContent }}
    </div>
    <iframe
      v-else-if="format == 'pdf'"
      :src="props.url"
      frameborder="0"
      class="mt-box"
    ></iframe>
    <iframe
      v-else
      :src="'https://view.officeapps.live.com/op/view.aspx?src=' + props.url"
      frameborder="0"
      class="mt-box"
    ></iframe>
    <!-- <iframe
      v-else
      :src="'https://view.xdocin.com/xdoc?_xdoc=' + props.url"
      frameborder="0"
      class="mt-box"
    ></iframe> -->

    <!-- <iframe v-else :src="'https://view.officeapps.live.com/op/view.aspx?src=https://fm-dzdx.daspatial.com/real3d/2024-08-15/xlsx/1823960474464677888.xlsx'" frameborder="0" class="mt-box"></iframe> -->
  </div>
</template>
<script lang="ts" setup>
import axios from "axios";
import { ref, onMounted, computed } from "vue";
const props = defineProps({
  url: {
    type: String,
    default: "",
  },
});
const textContent = ref<any>("");
const format = ref();
onMounted(() => {
  let arr = props.url?.split(".");
  format.value = arr[arr.length - 1];
  if (format.value?.toLocaleLowerCase() == "txt") {
    axios
      .get(props.url, {
        responseType: "blob",
        transformResponse: [
          async function (data) {
            return await transformData(data);
          },
        ],
      })
      .then((res) => {
        res.data.then((data: any) => {
          textContent.value = data;
        });
      });
  }
});
const transformData = (data: any) => {
  return new Promise((resolve) => {
    let reader = new FileReader();
    // reader.readAsText(data, "GBK");
    reader.readAsText(data, "utf8");
    reader.onload = () => {
      resolve(reader.result);
    };
  });
};
</script>

<style lang="scss">
.file_viewer {
  position: relative;
  width: 100%;
  height: 100%;
  top: 0px;
  left: 0px;
  overflow: auto;
  .mt-box {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0px;
    left: 0px;
  }
}
</style>
