<template>
	<div class="layout-padding">
		<div class="resource-file-mg-container" v-show="!isShowDetail">
			<FolderList @changeFolder="handleChangeFolder" />
			<div class="right-list">
				<div class="directory-path flex items-center">
					<span class="directory-path-item flex items-center" v-for="(item, index) in parentDirList" :key="index" @click="onChangeFolder(index)">
						<el-icon v-if="index !== 0"><ArrowRight /></el-icon>
						<span>{{ item.catalogName }}</span>
					</span>
				</div>
				<div class="directory-header">
					<span class="flex items-center">
						<el-dropdown>
							<el-button type="primary">新增</el-button>
							<template #dropdown>
								<el-dropdown-menu>
									<el-dropdown-item @click="onOperation('upload')">上传文件</el-dropdown-item>
									<el-dropdown-item @click="onAddFolder">创建文件夹</el-dropdown-item>
								</el-dropdown-menu>
							</template>
						</el-dropdown>
						<el-dropdown>
							<el-button type="primary" class="ml-3">批量操作</el-button>
							<template #dropdown>
								<el-dropdown-menu>
									<el-dropdown-item @click="onOperation('batchUpload')">批量上传</el-dropdown-item>
									<el-dropdown-item @click="onBatchDownload()">批量下载</el-dropdown-item>
									<el-dropdown-item @click="onBatchDelete"><span style="color: var(--el-color-danger)">批量删除</span></el-dropdown-item>
								</el-dropdown-menu>
							</template>
						</el-dropdown>
						<div class="types-search flex items-center">
							<span style="padding: 0 20px;" class="types-search-item" :class="{ active: activeType === '' }" @click="onChangeType('')">全部</span>
							<span
								class="types-search-item"
								:class="{ active: activeType === item.value }"
								v-for="(item, index) in resFileSearchType"
								:key="index"
								@click="onChangeType(item.value)"
							>
								<img :src="item.icon" alt="" />
								<span>{{ item.label }}</span>
							</span>
						</div>
					</span>
					<span class="flex items-center">
						<el-select v-model="activeValue" placeholder="请选择" style="width: 100px">
							<el-option
								v-for="item in searchOpts"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							></el-option>
						</el-select>
						<el-input v-model="searchInput" placeholder="请输入" style="width: 220px;margin-left: 10px;" @keyup.enter="getFileList">
							<template #suffix>
								<el-icon @click="getFileList" class="el-input__icon cursor-pointer"><search /></el-icon>
							</template>
						</el-input>
						<el-button class="reset-btn" @click="resetSearch"><el-icon><RefreshRight /></el-icon> 重置</el-button>
					</span>
				</div>
				<div class="directory-grid-header">
					<span class="flex items-center">
						<span class="mr-3" v-if="listType === 1">
							<el-checkbox v-model="canCheck" @change="selectDatas = []">开启选择</el-checkbox>
						</span>
						<span>已选 {{ selectDatas.length }} 项</span>
					</span>
					<div class="flex items-center">
						<img class="list-type-item" @click="switchListType(1)" :class="{ active: listType === 1 }" src="/@/assets/img/archives/view_grid.png" />
						<img class="list-type-item" @click="switchListType(2)" :class="{ active: listType === 2 }" src="/@/assets/img/archives/view_list.png" />
					</div>
				</div>
				<div class="directory-file-list card-list" v-if="listType === 1 && !isShowDetail" v-loading="isLoading">
					<el-dropdown trigger="hover" v-for="(item, index) in dataList" :key="index" :title="item.name" :disabled="canCheck"
						popper-class="card-item-dropdown" class="file-item-dropdown">
						<div :title="item.name"
							class="file-item"
							:class="{ active: selectDatas.some((i: any) => i.id === item.id && i.isFolder == item.isFolder), isFolder: item.isFolder }"
							@click="onCheckData(item)"
							@dblclick="onDblClickData(item)"
						>
							<div class="icon-check-box" v-if="canCheck"></div>
							<img class="icon-checked" src="/@/assets/img/archives/icon_checked.png" />
							<img v-if="item?.isFolder" class="file-icon" src="/@/assets/img/archives/icon_folder_blue.png" />
							<img v-else class="file-icon" :src="fileTypeConfig.find((obj: any) => obj.id == item.type)?.icon" />
							<span class="file-name">{{ item.name }}</span>
						</div>
						<template #dropdown>
							<el-dropdown-item @click="onOperation('detail', item)">查看</el-dropdown-item>
							<el-dropdown-item @click="onOperation('edit', item)">编辑</el-dropdown-item>
							<el-dropdown-item @click="onOperation('download', item)" v-if="!item.isFolder">下载</el-dropdown-item>
							<el-dropdown-item @click="onOperation('delete', item)"><span style="color: var(--el-color-danger)">删除</span></el-dropdown-item>
						</template>
					</el-dropdown>
					<el-empty v-if="!dataList.length" style="margin-top: 240px; width: 100%" :image-size="100"></el-empty>
				</div>
				<div class="directory-file-list table-list" v-if="listType === 2" v-loading="isLoading">
					<el-table :data="dataList" @selection-change="selectionChangHandle" height="100%" ref="tableRef">
						<el-table-column type="selection" width="40" align="center" />
						<el-table-column prop="name" label="名称" width="250" show-overflow-tooltip>
							<template #default="scope">
								<span class="flex items-center">
									<img v-if="scope.row.isFolder" class="icon-table-file" src="/@/assets/img/archives/icon_table_folder.png" />
									<img v-else class="icon-table-file" src="/@/assets/img/archives/icon_table_document.png" />
									<span class="table-file-name">{{ scope.row.name }}</span>
								</span>
							</template>
						</el-table-column>
						<el-table-column prop="type" label="文件类型" width="150" show-overflow-tooltip>
							<template #default="scope">
								{{ scope.row.isFolder ? '文件夹' : getFileTypeName(scope.row.type) }}
							</template>
						</el-table-column>
						<el-table-column prop="format" label="格式" width="130" show-overflow-tooltip></el-table-column>
						<el-table-column prop="totalSize" label="大小" width="100" show-overflow-tooltip>
							<template #default="scope">
								{{ scope.row.isFolder ? '/' : ((scope.row.totalSize || 0) / 1024 / 1024).toFixed(2) + 'MB' }}
							</template>
						</el-table-column>
						<el-table-column prop="createUser" label="创建人" width="130" show-overflow-tooltip></el-table-column>
						<el-table-column prop="createTime" label="创建时间" width="200" show-overflow-tooltip></el-table-column>
						<el-table-column prop="operation" label="操作" width="auto" show-overflow-tooltip>
							<template #default="scope">
								<div v-if="scope.row.isFolder">
									<el-button link type="primary" @click="onOperation('detail', scope.row)">查看</el-button>
									<el-button link type="primary" @click="onOperation('edit', scope.row)">编辑</el-button>
									<el-button link type="danger" @click="onOperation('delete', scope.row)">删除</el-button>
								</div>
								<div v-else>
									<el-button link type="primary" @click="onOperation('detail', scope.row)">查看</el-button>
									<el-button link type="primary" @click="onOperation('edit', scope.row)">编辑</el-button>
									<el-button link type="primary" @click="onOperation('download', scope.row)">下载</el-button>
									<el-button link type="danger" @click="onOperation('delete', scope.row)">删除</el-button>
								</div>
							</template>
						</el-table-column>
					</el-table>
				</div>
			</div>
		</div>
		<FileDetail ref="fileDetailRef" v-if="isShowDetail" @close="isShowDetail = false" />
		<FolderForm ref="folderFormRef" @refresh="getFileList" :currentParentList="parentDirList" />
		<UploadFileForm
			ref="uploadFormRef"
			@refresh="getFileList"
			:deptData="deptData"
			:resourceCatalogTree="resCatalogTree"
			:currentParentList="parentDirList"
		/>
		<BatchUploadForm
			v-if="isShowBatchUpload"
			@refresh="getFileList"
			@close="isShowBatchUpload = false"
			:deptData="deptData"
			:currentParentList="parentDirList"
		/>
	</div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { getFolderList, batchDelFolder, queryResourceCatalogList } from '/@/api/resource/resourceFile';
import { deptTree } from '/@/api/admin/dept';
import { fileTypeConfig, resFileSearchType } from '/@/config/resourceConfig';
import FolderList from '/@/views/resourceManagement/resourceFile/folder/FolderList.vue';
import FolderForm from '/@/views/resourceManagement/resourceFile/folder/FolderForm.vue';
import UploadFileForm from '/@/views/resourceManagement/resourceFile/upload/UploadFileForm.vue';
import BatchUploadForm from '/@/views/resourceManagement/resourceFile/upload/BatchUploadForm.vue';
import FileDetail from '/@/views/resourceManagement/resourceFile/detail/FileDetail.vue';

const fileDetailRef = ref();
const folderFormRef = ref();
const uploadFormRef = ref();
const tableRef = ref();
const deptData = ref<any[]>([]); // 部门下拉选项列表
const dataList = ref<any>([]); // 右侧文件、文件夹列表数据
const selectDatas = ref<any>([]); // 勾选数据
const parentDirList = ref<any>([]); // 文件夹路径
const listType = ref<number>(1); // 列表类型-1网格/2列表
const isLoading = ref<boolean>(false);
const canCheck = ref<boolean>(false); // 开启卡片勾选
const isShowDetail = ref<boolean>(false);
const resCatalogTree = ref<any>([]); // 资源目录列表
const activeType = ref<string>(''); // 筛选文件类型
const activeValue = ref<string>('fileName'); // 筛选值类型
const searchInput = ref<string>(''); // 搜索框筛选值
const isShowBatchUpload = ref<boolean>(false);

const searchOpts = [
	{ label: '名称', value: 'fileName' },
	{ label: '创建人', value: 'createBy' },
];

onMounted(() => {
	getDeptData();
	getResourceCatalogList();
});
// 切换左侧目录
const handleChangeFolder = (folder: any) => {
	canCheck.value = false;
	parentDirList.value = [folder];
	getFileList();
};
// 查询文件、文件夹列表
const getFileList = () => {
	if (parentDirList.value.length) {
		isLoading.value = true;
		selectDatas.value = [];
		let currentParentId = parentDirList.value[parentDirList.value.length - 1].id;
		getFolderList({
			catalogParentId: currentParentId,
			type: activeType.value,
			fileName: activeValue.value === 'fileName' ? searchInput.value : '',
			createBy: activeValue.value === 'createBy' ? searchInput.value : '',
		})
			.then((res) => {
				const { folders = [], files = [] } = res?.data || {};
				folders.forEach((item: any) => {
					item.isFolder = true;
					item.name = item.catalogName;
					item.format = '/';
					item.createUser = item.catalogCreator;
					item.createTime = item.catalogCreateTm;
				});
				dataList.value = (folders || []).concat(files || []);
			})
			.finally(() => {
				isLoading.value = false;
			});
	} else {
		dataList.value = [];
		selectDatas.value = [];
	}
};

const resetSearch = () => {
	activeType.value = '';
	searchInput.value = '';
	getFileList();
};

// 获取部门数据
const getDeptData = () => {
	deptTree().then((res) => {
		deptData.value = res?.data || [];
	});
};

// 切换视图 1网格/2列表
const switchListType = (type: number) => {
	listType.value = type;
	nextTick(() => {
		if (type === 2 && selectDatas.value.length) {
			selectDatas.value.forEach((row: any) => {
				tableRef.value?.toggleRowSelection(row, true);
			});
		}
	});
};

// 新增文件夹
const onAddFolder = () => {
	folderFormRef.value.openDialog();
};

// 从文件夹路径进入文件夹
const onChangeFolder = (index: number) => {
	parentDirList.value = parentDirList.value.slice(0, index + 1);
	getFileList();
};

const onOperation = (type: string, record?: any) => {
	switch (type) {
		case 'upload':
			uploadFormRef.value.openDialog();
			break;
		case 'batchUpload':
			isShowBatchUpload.value = true;
			break;
		case 'edit':
			if (record?.isFolder) {
				folderFormRef.value.openDialog(record);
			} else {
				uploadFormRef.value.openDialog(record, false);
			}
			break;
		case 'detail':
			if (record?.isFolder) {
				parentDirList.value.push(record);
				getFileList();
			} else {
				isShowDetail.value = true;
				nextTick(() => {
					fileDetailRef.value.initInfo(record);
				});
			}
			break;
		case 'delete':
			onBatchDelete([record]);
			break;
		case 'download':
			onBatchDownload([record]);
			break;
		default:
			return;
	}
};
// 表格勾选
const selectionChangHandle = (objs: { id: string }[]) => {
	selectDatas.value = objs;
};

// 网格视图勾选
const onCheckData = (record: any) => {
	if (!canCheck.value) {
		// 非勾选状态时，单击文件查看详情
		if (!record.isFolder) {
			onOperation('detail', record)
		}
		return;
	}
	let isExist = selectDatas.value.findIndex((item: any) => item.id === record.id && item.isFolder == record.isFolder);
	if (isExist > -1) {
		selectDatas.value.splice(isExist, 1);
	} else {
		selectDatas.value.push(record);
	}
};

const onDblClickData = (record: any) => {
	onOperation('detail', record);
};

// 批量删除
const onBatchDelete = async (arr?: any[]) => {
	if (!selectDatas.value.length && (arr && !arr.length)) return useMessage().wraning('请选择要删除的文件/文件夹');
	let fileIds: any = [];
	let folderIds: any = [];
	if (arr?.length) {
		arr.forEach((item: any) => {
			item.isFolder ? folderIds.push(item.id) : fileIds.push(item.id);
		});
	} else {
		selectDatas.value.forEach((item: any) => {
			item.isFolder ? folderIds.push(item.id) : fileIds.push(item.id);
		});
	}
	try {
		await useMessageBox().confirm('确认删除选中的文件/文件夹吗？');
	} catch {
		return;
	}
	try {
		isLoading.value = true;
		await batchDelFolder({ fileIds, folderIds });
		getFileList();
		useMessage().success('删除成功！');
	} catch {
		return;
	}
};
// 批量下载
const onBatchDownload = async (arr?: any[]) => {
	let list = arr || selectDatas.value;
	if (!list?.length) return useMessage().wraning('请选择要下载的文件');
	let hasFolder = list.some((item: any) => item.isFolder);
	try {
		await useMessageBox().confirm(hasFolder ? '批量下载时仅会下载选中的文件，不会下载文件夹中的内容，确认下载吗？' : '确认下载选中的文件吗？');
	} catch {
		return;
	}
	// 过滤出非文件夹的项目进行下载
	let downloadList = list.filter((item: any) => !item.isFolder);
	// 使用延时确保每个文件都能被下载
	downloadList.forEach((item: any, index: number) => {
		if (item.downloadUrl) {
			setTimeout(() => {
				// 使用a标签直接下载
				const link = document.createElement('a');
				link.href = item.downloadUrl;
				link.download = item.name + (item.format ? '.' + item.format : '');
				document.body.appendChild(link);
				link.click();
				
				// 给浏览器一些时间来处理下载请求
				setTimeout(() => {
					document.body.removeChild(link);
				}, 100);
			}, index * 300); // 每个下载间隔300毫秒
		}
	});
	selectDatas.value = [];
	if (downloadList.length > 0) {
		useMessage().success(`已开始下载${downloadList.length}个文件，请稍候...`);
	}
};

// 表格类型筛选
const onChangeType = (type: string) => {
	activeType.value = type;
	getFileList();
};
const getFileTypeName = (type: any) => {
	return (
		fileTypeConfig.find((obj: any) => {
			return obj.id == type;
		})?.name || '-'
	);
};

// 获取资源目录列表
const getResourceCatalogList = async () => {
	const res = await queryResourceCatalogList({ catalogState: 1 });
	const setTreeData = (data: any[]) => {
		data.forEach((item: any) => {
			item.disabled = item.catalogLevel != 4;
			item.catalogNo = item.catalogLevel == 4 ? item.catalogRemark : item.catalogNo;
			if (item.childCatalogs?.length) {
				setTreeData(item.childCatalogs);
			} else {
				item.childCatalogs = [];
			}
		});
	};
	const treeData = res?.data || [];
	setTreeData(treeData);
	resCatalogTree.value = treeData;
};
</script>

<style lang="scss" scoped>
.resource-file-mg-container {
	height: 100%;
	display: flex;
	.right-list {
		flex: 1;
		background: #fff;
		padding: 20px;
		margin-left: 20px;
		border-radius: 5px;
		display: flex;
		flex-direction: column;
		.directory-path {
			font-size: 16px;
			margin-bottom: 10px;
			.el-icon {
				margin: 0 5px;
			}
			.directory-path-item {
				cursor: pointer;
				&:hover {
					color: var(--el-color-primary);
				}
			}
		}
		.directory-header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			.types-search {
				margin-left: 30px;
				padding: 2px 10px;
				border-radius: 3px;
				background: #f6f6f6;
				.types-search-item {
					display: flex;
					align-items: center;
					font-size: 12px;
					height: 31px;
					cursor: pointer;
					padding: 2px 10px;
					&.active {
						background: var(--el-color-primary-light-8);
						border-radius: 3px;
						color: var(--el-color-primary);
					}
					img {
						width: 25px;
						height: 25px;
						margin-right: 5px;
					}
				}
			}
			.reset-btn {
				margin-left: 10px;
				background: #F2F3F5;
				.el-icon {
					margin-right: 5px;
				}
				&:hover {
					border-color: var(--el-border-color);
					color: var(--el-text-color-regular);
				}
			}
		}
		.directory-grid-header {
			height: 32px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin: 10px 0 10px 0;
			.list-type-item {
				width: 24px;
				height: 24px;
				cursor: pointer;
				margin-left: 15px;
				filter: brightness(0.1);
				&.active {
					filter: brightness(1);
				}
			}
		}
		.directory-file-list {
			overflow-y: auto;
			flex: 1;
			.icon-table-file {
				width: 16px;
				height: 16px;
				margin-right: 6px;
			}
			.table-file-name {
				width: 90%;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
			.file-item-dropdown {
				width: 160px;
				margin-right: 10px;
				margin-bottom: 10px;
			}
			.file-item {
				position: relative;
				width: 100%;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				cursor: pointer;
				padding: 20px;
				border-radius: 8px;
				&:hover,
				&.active {
					background: #F4F4F4;;
				}
				&.active .icon-checked {
					display: block;
				}
				&.active .icon-check-box {
					display: none;
				}
				.icon-check-box {
					position: absolute;
					left: 9px;
					top: 9px;
					width: 17px;
					height: 17px;
					border: 1px solid #d1d0d0;
					border-radius: 4px;
				}
				.icon-checked {
					left: 9px;
					top: 9px;
					width: 17px;
					height: 17px;
					display: none;
					position: absolute;
				}
				.file-icon {
					width: auto;
					height: 65px;
				}
				.file-name {
					width: 100%;
					text-align: center;
					margin-top: 12px;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					color: #000;
					line-height: 16px;
				}
			}
			@media (max-width: 1920px) and (min-width: 1680px)  {
				.file-item-dropdown {
					width: calc((100% - 60px) / 7);
					margin-right: 10px;
					&:nth-child(7n) {
						margin-right: 0;
					}
					.file-icon {
						width: auto;
						height: 70px;
					}
				}
			}
			@media (max-width: 1680px) and (min-width: 1440px) {
				.file-item-dropdown {
					width: calc((100% - 50px) / 6);
					margin-right: 10px;
					&:nth-child(6n) {
						margin-right: 0;
					}
				}
			}
			@media (max-width: 1440px) {
				.file-item-dropdown {
					width: calc((100% - 40px) / 5);
					margin-right: 10px;
					&:nth-child(5n) {
						margin-right: 0;
					}
					.file-icon {
						width: auto;
						height: 60px;
					}
				}
			}
		}
		.el-dropdown.is-disabled {
			color: #000;
		}
	}
}
</style>
<style lang="scss">
.card-item-dropdown {
	margin-top: -35px;
	margin-left: 70px;
	.el-popper__arrow {
		display: none;
	}
}
</style>
