<template>
	<div class="folder-box">
		<div class="folder-header flex justify-between items-center">
			<div class="folder-title">资源目录</div>
			<div class="folder-add" @click="onAddFolder">+ 新增目录</div>
		</div>
		<div class="folder-list">
			<template v-if="folderList.length">
				<div
					v-for="group in folderList"
					:key="group.id"
					@click="onSelectFolder(group)"
					class="folder-item flex justify-between items-center"
					:class="{ active: activeFolder === group.id }"
				>
					<div :title="group.catalogName" class="folder-name">
						<img class="folder-icon" src="/@/assets/img/archives/icon_folder.png" alt="" />
						<span>{{ group.catalogName }}</span>
					</div>
					<el-dropdown>
						<el-icon><MoreFilled /></el-icon>
						<template #dropdown>
							<el-dropdown-menu>
								<el-dropdown-item @click="onClickMenuItem('EDIT', group)"><span>编辑</span></el-dropdown-item>
								<el-dropdown-item @click="onClickMenuItem('DELETE', group)">
									<span type="text" style="color: var(--el-color-danger)">删除</span>
								</el-dropdown-item>
							</el-dropdown-menu>
						</template>
					</el-dropdown>
				</div>
			</template>
			<el-empty v-else style="margin-top: 40px" :image-size="100"></el-empty>
		</div>
		<FolderForm ref="folderFormRef" @refresh="getFolders" :currentParentList="[]" />
	</div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { getFolderList, delFolder } from '/@/api/resource/resourceFile';
import FolderForm from './FolderForm.vue';

let activeFolder = ref(null);
let folderList = ref<any>([]);

const folderFormRef = ref();
const emit = defineEmits(['changeFolder']);

onMounted(() => {
	getFolders();
});

// 获取文件夹列表
const getFolders = () => {
	getFolderList({ catalogParentId: 0 }).then((res) => {
		folderList.value = res?.data?.folders || [];
		if (!activeFolder.value && folderList.value.length) {
			onSelectFolder(folderList.value[0]);
		}
	});
};

// 新增文件夹
const onAddFolder = () => {
	folderFormRef.value.openDialog();
};

// 选中
const onSelectFolder = (folder: any) => {
	activeFolder.value = folder.id;
	emit('changeFolder', folder);
};

// 点击操作菜单
const onClickMenuItem = (type: string, record: any) => {
	switch (type) {
		case 'EDIT':
			folderFormRef.value.openDialog(record);
			break;
		case 'DELETE':
			handleDelete(record.id);
			break;
		default:
			break;
	}
};

// 删除操作
const handleDelete = async (id: string) => {
	try {
		await useMessageBox().confirm('确认删除该目录吗？');
	} catch {
		return;
	}
	try {
		await delFolder({ id });
		activeFolder.value = null;
		getFolders();
		useMessage().success('删除成功');
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};
</script>

<style lang="scss" scoped>
.folder-box {
	width: 300px;
	height: 100%;
	background: #fff;
	padding: 14px;
	border-radius: 5px;

	.folder-header {
		margin-bottom: 10px;

		.folder-title {
			font-size: 16px;
		}

		.folder-add {
			cursor: pointer;
			color: var(--el-color-primary);
		}
	}

	.folder-list {
		height: calc(100% - 70px);
		overflow-y: auto;

		.folder-item {
			cursor: pointer;
			padding: 5px 10px;
			border-radius: 2px;

			&.active {
				background: var(--el-color-primary-light-9);
			}

			.folder-name {
				font-size: 16px;
				width: 200px;
				display: flex;
				align-items: center;
				justify-content: flex-start;
				padding: 8px 0;
				color: #000;
				span {
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}

				.folder-icon {
					margin-right: 16px;
					width: 31px;
					height: 24px;
				}
			}
		}
	}
}
</style>
