import request from "/@/utils/request";

// 查询
export function fetchList(query?: Object) {
  return request({
    url: "/datacenter/catalog/list",
    method: "post",
    data: query,
  });
}

// 查询
export function fetchListForRecursive(query?: Object) {
  return request({
    url: "/datacenter/catalog/listForRecursive",
    method: "post",
    data: query,
  });
}

// 新增
export function addObj(obj?: Object) {
  return request({
    url: "/datacenter/catalog/add",
    method: "post",
    data: obj,
  });
}
// 更新
export function updateObj(obj?: Object) {
  return request({
    url: "/datacenter/catalog/edit",
    method: "post",
    data: obj,
  });
}
// 删除
export const delObj = (obj: Object) => {
	return request({
		url: '/datacenter/catalog/remove',
		method: 'post',
    data: obj,
	});
};

// 查询带元数据的树形结构接口
export function listForResource(query?: Object) {
  return request({
    url: "/datacenter/catalog/listForResource",
    method: "post",
    data: query,
  });
}