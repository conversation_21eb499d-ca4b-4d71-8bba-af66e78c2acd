<template>
  <div class="description-config">
    <div class="config-content">
      <div class="config-section">
        <h4 class="section-title">详情描述编辑</h4>
        <div class="editor-container">
          <div class="editor-toolbar">
            <el-button-group>
              <el-button size="small" @click="insertText('**粗体**')">
                <el-icon><Bold /></el-icon>
                粗体
              </el-button>
              <el-button size="small" @click="insertText('*斜体*')">
                <el-icon><Italic /></el-icon>
                斜体
              </el-button>
              <el-button size="small" @click="insertText('# 标题')">
                <el-icon><Heading /></el-icon>
                标题
              </el-button>
              <el-button size="small" @click="insertText('- 列表项')">
                <el-icon><List /></el-icon>
                列表
              </el-button>
              <el-button size="small" @click="insertText('[链接文字](链接地址)')">
                <el-icon><Link /></el-icon>
                链接
              </el-button>
            </el-button-group>
            <el-button size="small" @click="showPreview = !showPreview">
              <el-icon><View /></el-icon>
              {{ showPreview ? '编辑' : '预览' }}
            </el-button>
          </div>
          
          <div class="editor-content">
            <el-input
              v-if="!showPreview"
              v-model="formData.content"
              type="textarea"
              :rows="20"
              placeholder="请输入详情描述内容，支持 Markdown 语法"
              class="content-editor"
            />
            <div v-else class="content-preview" v-html="renderedContent"></div>
          </div>
        </div>
      </div>

      <div class="config-section">
        <h4 class="section-title">
          相关图片
          <el-button type="primary" size="small" @click="showAddImageDialog = true" style="margin-left: 16px">
            <el-icon><Plus /></el-icon>
            添加图片
          </el-button>
        </h4>
        <div class="image-gallery">
          <div
            v-for="image in imageList"
            :key="image.id"
            class="image-item"
          >
            <div class="image-container">
              <img :src="image.url" :alt="image.title" />
              <div class="image-overlay">
                <el-button type="primary" size="small" @click="insertImageToContent(image)">
                  插入内容
                </el-button>
                <el-button type="danger" size="small" @click="deleteImage(image)">
                  删除
                </el-button>
              </div>
            </div>
            <div class="image-info">
              <div class="image-title">{{ image.title }}</div>
              <div class="image-desc">{{ image.description }}</div>
            </div>
          </div>
          <el-empty v-if="imageList.length === 0" description="暂无图片" :image-size="80" />
        </div>
      </div>

      <div class="config-section">
        <h4 class="section-title">SEO 配置</h4>
        <el-form :model="seoData" label-width="120px">
          <el-form-item label="页面标题">
            <el-input v-model="seoData.title" placeholder="请输入页面标题" maxlength="60" show-word-limit />
          </el-form-item>
          <el-form-item label="页面描述">
            <el-input
              v-model="seoData.description"
              type="textarea"
              :rows="3"
              placeholder="请输入页面描述"
              maxlength="160"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="关键词">
            <el-select
              v-model="seoData.keywords"
              multiple
              filterable
              allow-create
              placeholder="请输入关键词"
              style="width: 100%"
            >
              <el-option
                v-for="keyword in keywordOptions"
                :key="keyword"
                :label="keyword"
                :value="keyword"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <div class="action-buttons">
        <el-button @click="resetForm">重置</el-button>
        <el-button type="primary" @click="saveConfig" :loading="saving">保存配置</el-button>
      </div>
    </div>

    <!-- 添加图片弹窗 -->
    <el-dialog
      v-model="showAddImageDialog"
      title="添加图片"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="imageForm" label-width="80px">
        <el-form-item label="图片标题">
          <el-input v-model="imageForm.title" placeholder="请输入图片标题" />
        </el-form-item>
        <el-form-item label="图片描述">
          <el-input
            v-model="imageForm.description"
            type="textarea"
            :rows="2"
            placeholder="请输入图片描述"
          />
        </el-form-item>
        <el-form-item label="上传图片">
          <el-upload
            class="image-uploader"
            :show-file-list="false"
            :on-success="handleImageSuccess"
            :before-upload="beforeImageUpload"
            action="/api/upload"
          >
            <img v-if="imageForm.url" :src="imageForm.url" class="upload-image" />
            <div v-else class="upload-placeholder">
              <el-icon class="upload-icon"><Plus /></el-icon>
              <div class="upload-text">上传图片</div>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddImageDialog = false">取消</el-button>
        <el-button type="primary" @click="addImage">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { Plus, Bold, Italic, Heading, List, Link, View } from '@element-plus/icons-vue';

interface Props {
  sceneId: string;
}

const props = defineProps<Props>();
const { success, error } = useMessage();
const { confirm } = useMessageBox();

// 响应式数据
const saving = ref(false);
const showPreview = ref(false);
const showAddImageDialog = ref(false);

const formData = reactive({
  content: '',
});

const seoData = reactive({
  title: '',
  description: '',
  keywords: [] as string[],
});

const imageForm = reactive({
  title: '',
  description: '',
  url: '',
});

const imageList = ref<any[]>([]);

const keywordOptions = ref([
  '武当山',
  '道教文化',
  '古建筑',
  '文化遗产',
  '旅游景点',
  '历史文化',
]);

// 计算属性
const renderedContent = computed(() => {
  // 简单的 Markdown 渲染（实际项目中建议使用专业的 Markdown 解析库）
  let html = formData.content;
  
  // 标题
  html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');
  html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
  html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
  
  // 粗体和斜体
  html = html.replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>');
  html = html.replace(/\*(.*)\*/gim, '<em>$1</em>');
  
  // 链接
  html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2" target="_blank">$1</a>');
  
  // 列表
  html = html.replace(/^- (.*$)/gim, '<li>$1</li>');
  html = html.replace(/(<li>.*<\/li>)/gims, '<ul>$1</ul>');
  
  // 换行
  html = html.replace(/\n/gim, '<br>');
  
  return html;
});

// 方法
const insertText = (text: string) => {
  const textarea = document.querySelector('.content-editor textarea') as HTMLTextAreaElement;
  if (textarea) {
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const content = formData.content;
    
    formData.content = content.substring(0, start) + text + content.substring(end);
    
    // 重新设置光标位置
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + text.length, start + text.length);
    }, 0);
  }
};

const insertImageToContent = (image: any) => {
  const imageMarkdown = `![${image.title}](${image.url})`;
  insertText(imageMarkdown);
};

const handleImageSuccess = (response: any) => {
  if (response.code === 200) {
    imageForm.url = response.data.url;
    success('图片上传成功');
  }
};

const beforeImageUpload = (file: File) => {
  const isImage = file.type.startsWith('image/');
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isImage) {
    error('只能上传图片文件!');
    return false;
  }
  if (!isLt2M) {
    error('图片大小不能超过 2MB!');
    return false;
  }
  return true;
};

const addImage = () => {
  if (!imageForm.title || !imageForm.url) {
    error('请填写完整信息');
    return;
  }
  
  const newImage = {
    id: Date.now().toString(),
    ...imageForm,
  };
  
  imageList.value.push(newImage);
  
  // 重置表单
  Object.assign(imageForm, {
    title: '',
    description: '',
    url: '',
  });
  
  showAddImageDialog.value = false;
  success('图片添加成功');
};

const deleteImage = async (image: any) => {
  try {
    await confirm(`确认删除图片"${image.title}"吗？`);
    const index = imageList.value.findIndex(item => item.id === image.id);
    if (index > -1) {
      imageList.value.splice(index, 1);
      success('删除成功');
    }
  } catch {
    // 用户取消删除
  }
};

const resetForm = () => {
  Object.assign(formData, {
    content: '',
  });
  Object.assign(seoData, {
    title: '',
    description: '',
    keywords: [],
  });
  imageList.value = [];
  loadData();
};

const saveConfig = async () => {
  try {
    saving.value = true;
    // TODO: 调用API保存配置
    await new Promise(resolve => setTimeout(resolve, 1000));
    success('保存成功');
  } catch (err) {
    error('保存失败');
  } finally {
    saving.value = false;
  }
};

const loadData = async () => {
  try {
    // TODO: 调用API获取数据
    // 模拟数据
    Object.assign(formData, {
      content: `# 武当山主峰天柱峰

武当山主峰天柱峰，海拔1612米，是道教圣地的核心区域。这里不仅有着壮丽的自然风光，更承载着深厚的道教文化底蕴。

## 历史沿革

天柱峰自古以来就是道教修行的圣地，历代道士在此修炼，留下了丰富的文化遗产。

## 建筑特色

- **金殿**：明代建筑，全铜铸造
- **紫霄宫**：规模宏大的宫观建筑群
- **南岩宫**：悬崖峭壁上的奇迹建筑

更多详细信息请参考 [武当山官方网站](https://www.wudangshan.com)`,
    });
    
    Object.assign(seoData, {
      title: '武当山主峰天柱峰 - 道教圣地核心区域',
      description: '武当山主峰天柱峰，海拔1612米，是道教圣地的核心区域，拥有金殿、紫霄宫等著名古建筑群。',
      keywords: ['武当山', '天柱峰', '道教文化', '金殿', '紫霄宫'],
    });
    
    imageList.value = [
      {
        id: '1',
        title: '天柱峰全景',
        description: '武当山主峰天柱峰的壮丽全景',
        url: '',
      },
      {
        id: '2',
        title: '金殿建筑',
        description: '明代全铜铸造的金殿建筑',
        url: '',
      },
    ];
  } catch (err) {
    error('获取数据失败');
  }
};

onMounted(() => {
  loadData();
});
</script>

<style scoped lang="scss">
.description-config {
  height: 100%;
  overflow: auto;
}

.config-content {
  padding: 24px;
}

.config-section {
  margin-bottom: 32px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 2px solid #409eff;
    display: inline-block;
  }
}

.editor-container {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;

  .editor-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f5f7fa;
    border-bottom: 1px solid #e4e7ed;

    .el-button-group {
      .el-button {
        .el-icon {
          margin-right: 4px;
        }
      }
    }
  }

  .editor-content {
    .content-editor {
      :deep(.el-textarea__inner) {
        border: none;
        border-radius: 0;
        resize: none;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 14px;
        line-height: 1.6;
      }
    }

    .content-preview {
      padding: 16px;
      min-height: 500px;
      line-height: 1.6;
      color: #303133;

      :deep(h1) {
        font-size: 24px;
        font-weight: 600;
        margin: 0 0 16px 0;
        color: #303133;
      }

      :deep(h2) {
        font-size: 20px;
        font-weight: 600;
        margin: 16px 0 12px 0;
        color: #303133;
      }

      :deep(h3) {
        font-size: 16px;
        font-weight: 600;
        margin: 12px 0 8px 0;
        color: #303133;
      }

      :deep(strong) {
        font-weight: 600;
        color: #303133;
      }

      :deep(em) {
        font-style: italic;
        color: #606266;
      }

      :deep(a) {
        color: #409eff;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }

      :deep(ul) {
        margin: 12px 0;
        padding-left: 24px;

        li {
          margin: 4px 0;
        }
      }

      :deep(img) {
        max-width: 100%;
        height: auto;
        border-radius: 4px;
        margin: 12px 0;
      }
    }
  }
}

.image-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;

  .image-item {
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s;

    &:hover {
      border-color: #409eff;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .image-container {
      position: relative;
      height: 150px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .image-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.6);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        opacity: 0;
        transition: opacity 0.3s;

        .el-button {
          font-size: 12px;
        }
      }

      &:hover .image-overlay {
        opacity: 1;
      }
    }

    .image-info {
      padding: 12px;

      .image-title {
        font-size: 14px;
        font-weight: 500;
        color: #303133;
        margin-bottom: 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .image-desc {
        font-size: 12px;
        color: #909399;
        line-height: 1.4;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

.image-uploader {
  :deep(.el-upload) {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s;
    width: 200px;
    height: 150px;

    &:hover {
      border-color: #409eff;
    }
  }
}

.upload-image {
  width: 200px;
  height: 150px;
  object-fit: cover;
  display: block;
}

.upload-placeholder {
  width: 200px;
  height: 150px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #8c939d;

  .upload-icon {
    font-size: 28px;
    margin-bottom: 8px;
  }

  .upload-text {
    font-size: 14px;
  }
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}
</style>
