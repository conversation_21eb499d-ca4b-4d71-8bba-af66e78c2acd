<template>
  <div class="operation-log-container">
    <!-- 统一的日志管理区域 -->
    <div class="log-management-panel">
      <!-- 查询表单区域 -->
      <div class="query-section">
        <el-form :model="queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList" class="query-form">
          <el-form-item label="" prop="state">
            <el-select v-model="queryForm.state" clearable placeholder="请选择操作类型" style="width: 200px">
              <el-option label="查看" value="1" />
              <el-option label="修改" value="23" />
              <el-option label="删除" value="24" />
              <el-option label="评论" value="3" />
              <el-option label="统计" value="28" />
            </el-select>
          </el-form-item>
          <el-form-item style="margin-bottom: 0px !important;">
            <el-button icon="Search" type="primary" @click="getDataList">查询</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格区域 -->
      <div class="table-section">
        <div class="table-container">
          <el-table :data="state.dataList || []" v-loading="state.loading" show-overflow-tooltip border
            @selection-change="handleSelectionChange" class="log-table" header-cell-class-name="custom-table-header">
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column prop="name" label="操作对象" width="200" show-overflow-tooltip>
              <template #default="scope">
                <div class="operation-target">
                  【{{ resourceData?.assets_num || "暂无编码" }}】{{ resourceData?.assets_name }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="operateTypeName" label="操作类型" width="120" align="center">
              <template #default="scope">
                <el-tag :type="getOperationTypeTag(scope.row.operateTypeName)" size="small">
                  {{ scope.row.operateTypeName || '--' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="title" label="操作内容" min-width="200" show-overflow-tooltip />
            <el-table-column prop="createUser" label="操作人员" width="120" align="center" />
            <el-table-column prop="createTime" label="操作时间" width="180" align="center" />
            <el-table-column prop="ip" label="操作IP" width="140" align="center" />
            <el-table-column label="操作详情" width="100" align="center">
              <template #default="scope">
                <el-button @click="viewLogDetail(scope.row)" size="small" type="primary" text>
                  查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 分页区域 -->
      <div class="pagination-section">
        <div class="pagination-container">
          <pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
        </div>
      </div>
    </div>

    <!-- 日志详情弹窗 -->
    <el-dialog v-model="logDetailVisible" title="操作日志详情" width="800px" destroy-on-close>
      <div class="log-detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="操作对象">
            {{ currentLogDetail?.name || '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="操作类型">
            <el-tag :type="getOperationTypeTag(currentLogDetail?.operateTypeName)" size="small">
              {{ currentLogDetail?.operateTypeName || '--' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="操作人员">
            {{ currentLogDetail?.createUser || '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="操作时间">
            {{ currentLogDetail?.createTime || '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="操作IP">
            {{ currentLogDetail?.ip || '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="用户代理">
            {{ currentLogDetail?.userAgent || '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="操作内容" :span="2">
            {{ currentLogDetail?.title || '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="详细描述" :span="2">
            <div class="log-description">
              {{ currentLogDetail?.description || '暂无详细描述' }}
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, defineAsyncComponent } from 'vue';
import { useMessage } from '/@/hooks/message';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { operateLogList, getListAllUser } from '/@/api/resource/filelist/file';
import { Search, Refresh, Download } from '@element-plus/icons-vue';

const Pagination = defineAsyncComponent(() => import('/@/components/Pagination/index.vue'));

interface Props {
  resourceData: any;
}

const props = withDefaults(defineProps<Props>(), {
  resourceData: null,
});

// 响应式数据
const queryRef = ref();
const selectedLogs = ref<any[]>([]);
const userList = ref<any[]>([]);
const logDetailVisible = ref(false);
const currentLogDetail = ref<any>(null);

// 查询表单
const queryForm = reactive({
  tableName: '',
  businessId: '',
  userId: undefined,
  state: undefined,
});

// 表格状态
const state: BasicTableProps = reactive<BasicTableProps>({
  queryForm,
  pageList: operateLogList,
  createdIsNeed: false,
  props: {
    item: 'records',
    totalCount: 'total',
  },
  isPage: true,
});

const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile } = useTable(state);

// 获取操作类型标签样式
const getOperationTypeTag = (type: string) => {
  const typeMap: Record<string, string> = {
    '查看': 'info',
    '修改': 'warning',
    '删除': 'danger',
    '评论': 'success',
    '统计': 'primary',
  };
  return typeMap[type] || 'info';
};

// 重置查询条件
const resetQuery = () => {
  queryRef.value?.resetFields();
  queryForm.state = undefined;
  queryForm.userId = undefined;
  getDataList();
};

// 处理表格选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedLogs.value = selection;
};

// 导出日志
const exportLogs = () => {
  if (selectedLogs.value.length === 0) {
    useMessage().wraning('请选择要导出的日志');
    return;
  }

  const ids = selectedLogs.value.map((log: any) => log.id);
  const params = {
    tableName: queryForm.tableName,
    businessId: props.resourceData?.id,
    ids: ids.join(','),
  };

  downBlobFile('/datacenter/operate/log/export', params, 'operateLog.xlsx');
};

// 查看日志详情
const viewLogDetail = (log: any) => {
  currentLogDetail.value = log;
  logDetailVisible.value = true;
};

// 获取用户列表
const getUserList = async () => {
  try {
    const res = await getListAllUser({});
    if (res.ok) {
      userList.value = res.data || [];
    }
  } catch (error) {
    useMessage().error('获取用户列表失败');
  }
};

// 初始化数据
const initData = () => {
  if (props.resourceData) {
    queryForm.tableName = props.resourceData.tableName || props.resourceData.table_name;
    queryForm.businessId = props.resourceData.id;
    getDataList();
    getUserList();
  }
};

// 监听资源数据变化
watch(() => props.resourceData, (newData) => {
  if (newData) {
    initData();
  }
}, { immediate: true });

// 组件挂载时初始化
onMounted(() => {
  initData();
});
</script>

<style scoped lang="scss">
:deep(.el-form-item) {
  margin-bottom: 0px !important;
}

// 主容器 - 充分利用可用空间
.operation-log-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f7fa;
}

// 统一的日志管理面板
.log-management-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: white;
  overflow: hidden;
}

// 查询区域
.query-section {
  flex-shrink: 0; // 防止压缩
  padding: 10px;

  .query-content {

    .query-form {
      margin: 0;

      :deep(.el-form-item) {
        margin-bottom: 0;
        margin-right: 0px; // 减少间距

        &:last-child {
          margin-right: 0;
        }
      }

      :deep(.el-form-item__label) {
        font-weight: 500;
        color: #606266;
      }

      :deep(.el-button) {
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: 500;
      }
    }
  }
}

// 表格区域
.table-section {
  flex: 1; // 占据剩余空间
  display: flex;
  flex-direction: column;
  min-height: 0; // 重要：允许flex子项收缩
  overflow: hidden; // 防止内容溢出

  .table-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; // 重要：允许flex子项收缩
    overflow: hidden;

    .log-table {
      flex: 1;
      overflow: hidden;

      // 确保表格高度正确计算
      :deep(.el-table) {
        height: 100%;
        display: flex;
        flex-direction: column;

        .el-table__header-wrapper {
          flex-shrink: 0;
        }

        .el-table__body-wrapper {
          overflow-y: auto;
        }

        .el-table__footer-wrapper {
          flex-shrink: 0;
        }
      }

      // 自定义表头样式
      :deep(.custom-table-header) {
        background-color: var(--next-bg-main-color, #f5f7fa);
        color: rgba(0, 0, 0, 0.4) !important;
        font-weight: 400;
        font-size: 14px;
        height: 50px;
      }

      .operation-target {
        font-size: 14px;
        color: #303133;
        font-weight: 500;
      }

      :deep(.el-table__header-wrapper) {
        .el-table__header {
          th {
            background-color: #fafafa;
            color: #303133;
            font-weight: 600;
            border-bottom: 1px solid #e4e7ed;
          }
        }
      }

      :deep(.el-table__body-wrapper) {
        .el-table__row {
          &:hover {
            background-color: #f5f7fa;
          }
        }
      }

      :deep(.el-table__empty-block) {
        min-height: 200px;
      }
    }
  }
}

// 分页区域
.pagination-section {
  flex-shrink: 0; // 防止压缩
  background-color: #fafafa;

  .pagination-container {
    padding: 12px 20px; // 减少垂直间距
    display: flex;
    justify-content: flex-end;
    align-items: center;

    :deep(.el-pagination) {

      .el-pagination__total,
      .el-pagination__jump {
        color: #606266;
        font-size: 13px;
      }

      .el-pager {
        li {
          min-width: 32px;
          height: 32px;
          line-height: 32px;
          border-radius: 4px;
          margin: 0 2px;

          &.is-active {
            background-color: var(--el-color-primary, #A12F2F);
            color: white;
          }
        }
      }

      .btn-prev,
      .btn-next {
        width: 32px;
        height: 32px;
        border-radius: 4px;
      }
    }
  }
}

// 日志详情弹窗
.log-detail-content {
  .log-description {
    max-height: 200px;
    overflow-y: auto;
    padding: 12px;
    font-size: 14px;
    line-height: 1.6;
    color: #606266;
  }

  :deep(.el-descriptions) {
    .el-descriptions__label {
      font-weight: 600;
      color: #303133;
      background-color: #fafafa;
    }

    .el-descriptions__content {
      color: #606266;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .operation-log-container {
    padding: 12px;
  }

  .query-content {
    .query-form {
      :deep(.el-form-item) {
        margin-right: 12px;
      }
    }
  }
}

@media (max-width: 768px) {
  .operation-log-container {
    padding: 8px;
  }

  .query-content {
    padding: 0 16px 12px 16px;

    .query-form {
      :deep(.el-form-item) {
        margin-right: 8px;
        margin-bottom: 12px;

        .el-form-item__label {
          font-size: 13px;
        }
      }
    }
  }

}

// 表格优化样式
:deep(.el-table) {

  .el-table__cell {
    padding: 12px 0; // 减少单元格内边距
  }

  .el-table__header {
    .el-table__cell {
      padding: 14px 0;
    }
  }
}

// 标签样式优化
:deep(.el-tag) {
  font-weight: 500;
  padding: 0 8px;
  height: 24px;
  line-height: 24px;
}

// 按钮样式优化
:deep(.el-button) {
  &.is-text {
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 500;

    &:hover {
      background-color: var(--el-color-primary-light-9, #f0f2ff);
    }
  }
}
</style>
