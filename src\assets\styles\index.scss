@font-face {
    font-family: 'Source Han Serif CN';
    src: url('/@/assets/fonts/SourceHanSerifCN-Regular.otf');
}
.firstTitle {
    font-size: 18px;
    color: #333333;
    font-weight: 700;
    border-left: 2px solid #333;
    padding-left: 10px;
    line-height: 18px;
    height: 18px;
    // margin-bottom: 20px;
}
.secondTitle {
    font-size: 16px;
    line-height: 16px;
    height: 16px;
    font-weight: 700;
    margin-bottom: 10px;
    margin-top: 20px;
    margin-left: 10px;
    padding-left: 5px;
    border-left: 2px solid #333;
}
.thirdTitle {
    font-size: 16px;
    line-height: 16px;
    height: 16px;
    font-weight: 400;
    margin-bottom: 15px;
    // margin-top: 20px;
    margin-left: 10px;
    padding-left: 5px;
    border-left: 1px solid #333;
}
.lookMore{
    // display: inline-block;
    color: #1890FF;
    margin-top: 5px;
    cursor: pointer;
    text-align: center;
}
.topTitle{
    font-weight: 600;
    line-height: 36px;
    height: 36px;
    text-align: center;
    // background: rgba(242, 242, 242, 0.7333333333333333);
    // background: #fff;
    border-bottom: 1px solid #EBEBEB;;
}
.cardContent{
    display: flex;
    justify-content: space-between;
    // background: rgba(242, 242, 242, 0.29411764705882354);
    // background: #fff;
    position: relative;
    text-align: center;
}
.redColor{
    color: #FF2F48;
}
.orangeColor{
    color: #FF7200;
}
.yellowColor{
    color: #FDCD4D;
}
.greenColor{
    color: #70B603;
}
.blueColor{
    color: #0C4EC0;
}
