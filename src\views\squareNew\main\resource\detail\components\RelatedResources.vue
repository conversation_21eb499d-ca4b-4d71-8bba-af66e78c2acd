<template>
  <div class="related-resources">
    <div class="header-section">
      <div class="title">相关资源</div>
      <div class="header-buttons">
        <button
          :class="`related-list-btn ${curTab == 1 ? 'active' : ''}`"
          @click="changeTab(1)"
        >
          相关列表
        </button>
        <button v-if="props.currentType == 1"
          :class="`knowledge-graph-btn ${curTab == 2 ? 'active' : ''}`"
          @click="changeTab(2)"
        >
          知识图谱
        </button>
      </div>
    </div>
    <div class="related-list-container" v-show="curTab == 1">
      <div class="tabs-section">
        <div
          v-for="(tab, index) in tabList"
          :key="index"
          :class="['tab-item', { active: tab.active }]"
          @click="selectTab(tab)"
        >
          <span>{{ tab.itemName || '-' }}</span>
          <span>（{{ tab.itemCount }}）</span>
        </div>
      </div>
      <div class="resources-list" v-loading="isLoading">
        <div
          v-for="(resource, index) in filteredResources"
          :key="index"
          class="resource-item"
          @click="goResourceDetail(resource)"
        >
          <div class="resource-img-box">
            <img :src="resource.image || noCoverImg" :class="{ 'no-cover': !resource.image }" alt="资源图片" @error="onImgError" />
          </div>
          <div class="resource-info">
            <div class="resource-title">{{ resource.assets_name }}</div>
            <div class="resource-meta">
              <span class="label">资源目录:</span>
              <span class="value">{{ resource.directory }}</span>
            </div>
            <div class="resource-meta">
              <span class="label">关键词:</span>
              <span class="value keyword">{{ resource.assets_tag }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="knowledge-map-container" v-if="curTab == 2">
      <NodeGraph ref="graphRef" :data="graphData" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import NodeGraph from '/@/components/NodeGraph/index.vue';
import { getKeywordSearch, getSearchCountByCatalog } from "/@/api/squareNew/index";
import { getFileGraph } from '/@/api/resource/resourceFile';
import noCoverImg from "/@/assets/img/squareNew/no_cover.png";
import { ElMessage } from 'element-plus'

const props = defineProps({
  currentInfo: {
    type: Object,
    default: () => {},
  },
  currentType: {
    type: Number,
    default: () => 1,
  },
});
const isLoading = ref(false);
const tabList = ref([]);
const resourcesList = ref([]);
const graphData = ref({});
const graphRef = ref();
onMounted(() => {
  getRelatedResource();
});

const filteredResources = computed(() => {
  return resourcesList.value;
});

const curTab = ref(1);
const changeTab = (tabValue) => {
  curTab.value = tabValue;
  if (tabValue == 2) {
    getFileGraphData(); 
  }
};

const onImgError = (e) => {
  e.target.src = noCoverImg;
  e.target.classList.add("no-cover");
};

// 获取相关资源
const getRelatedResource = () => {
  if (!props.currentInfo?.name) return;
  getSearchCountByCatalog({ keyword: props.currentInfo.name, groupField: "first_catalog_no" }).then(res => {
    tabList.value = res.data || [];
    if (tabList.value.length) {
      let firstItem = tabList.value[0];
      selectTab(firstItem);
    }
  });
};

const selectTab = (selectedTab) => {
  tabList.value.forEach((tab) => {
    tab.active = tab.itemNo === selectedTab.itemNo;
  });
  isLoading.value = true;
  getKeywordSearch({
    keyword: props.currentInfo.name,
    firstCatalogNo: selectedTab.itemNo,
  }).then(res => {
    resourcesList.value = (res.data || []).map(item => {
      return {
        ...item,
        directory: item.assets_type == 1 ? item.third_catalog_name : `${item.first_catalog_name}${item.second_catalog_name ? ' > ' + item.second_catalog_name : ''}${item.third_catalog_name ? ' > ' + item.third_catalog_name : ''}`,
      };
    });
  }).finally(() => {
    isLoading.value = false;
  });
};

// 获取知识图谱数据
const getFileGraphData = async () => {
	const res = await getFileGraph(props.currentInfo.id);
	graphData.value = res.data || {};
	nextTick(() => {
		graphRef.value.clearGraph();
		graphRef.value.setGraph();
	});
};

// 跳转详情页面
const goResourceDetail = (record) => {
  if (!record.business_id || !record.assets_type || !record.table_name) {
    return ElMessage.warning("该资源暂时无法查看（缺少参数）");
  }
  window.open(`/#/squareNew/resource/detail/${record.business_id}?type=${record.assets_type}&tabName=${record.table_name}`);
};
</script>

<style lang="scss" scoped>
.related-resources {
  background-color: #fff;
  padding: 20px;
  margin-top: 28px;

  .header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    .title {
      position: relative;
      padding-left: 15px; // 为竖线留出空间
      font-weight: 600;
      font-size: 16px;
      color: var(--el-color-primary);

      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 2px;
        height: 20px;
        background-color: var(--el-color-primary);
      }
    }

    .header-buttons {
      button {
        padding: 5px 16px;
        border: none;
        border-radius: 3px;
        background-color: #fff;
        color: var(--el-color-primary);
        cursor: pointer;
        font-family: PingFang SC;
        font-size: 14px;
        margin-left: 10px;
        border: 1px solid var(--el-color-primary);
        &.active {
          background-color: var(--el-color-primary);
          color: #fff;
        }
      }
    }
  }

  .tabs-section {
    display: flex;
    margin-bottom: 20px;

    .tab-item {
      flex: 1;
      margin-right: 20px;
      cursor: pointer;
      font-size: 16px;
      color: #666;
      border-bottom: 2px solid transparent;
      font-weight: 400;
      font-size: 14px;
      line-height: 100%;
      letter-spacing: 0%;
      text-align: center;
      color: #393939;
      height: 46px;
      line-height: 46px;
      text-align: center;
      white-space: nowrap;

      &.active {
        color: #862626;
        font-weight: 600;
        span:last-child {
          color: #862626;
        }
        position: relative;
        &::after {
          content: "";
          position: absolute;
          bottom: 0px;
          width: 100%;
          height: 2px;
          background-color: #862626;
          margin-top: 5px;
          left: 0;
        }
      }
      &:hover:not(.active) {
        color: #862626;
      }
    }
  }

  .resources-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    min-height: 434px;

    .resource-item {
      display: flex;
      flex-direction: column;
      padding: 15px;
      transition: all 0.3s ease;
      width: 19%;
      height: 316px;
      cursor: pointer;
      &:hover {
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        transform: translateY(-3px);
      }

      .resource-img-box {
        width: 100%;
        height: 190px;
        flex-shrink: 0;
        margin-right: 10px;
        border-radius: 4px;
        overflow: hidden;
        background-color: rgba(0, 0, 0, 0.02);
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        .no-cover {
          width: 50%;
          height: auto;
        }
      }

      .resource-info {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;

        .resource-title {
          font-weight: 700;
          font-size: 16px;
          color: #393939;
          margin: 10px 0;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }

        .resource-meta {
          font-weight: 400;
          font-size: 14px;
          line-height: 20px;
          letter-spacing: 0%;
          color: #a1a1a1;
          margin-bottom: 12px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          &:last-child {
            margin-bottom: 0;
          }

          .label {
            margin-right: 5px;
          }
          .keyword {
            color: #af5a5c;
          }
        }
      }
    }
  }

  .knowledge-map-container {
    height: 500px;
  }
}
</style>
