import request from "/@/utils/request"

// 获取回收站列表
export function fetchRecycleList(query?: Object) {
  return request({
    url: '/archive/wb/recycle/pageList',
    method: 'get',
    params: query,
  })
}

// 彻底删除
export function recycleBatchDel(query?: any) {
  return request({
    url: '/archive/wb/recycle/batchDel' + (query || ''),
    method: 'get',
  })
}

// 批量还原
export function recycleBatchRollback(query?: any) {
  return request({
    url: '/archive/wb/recycle/batchRollback' + (query || ''),
    method: 'get',
  })
}