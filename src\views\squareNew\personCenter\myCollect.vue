<template>
  <div class="table-container">
    <div class="table-title-box">{{ dataMap[props.type].title }}</div>
    
    <div class="table-content-box">
      <div v-if="loading" class="loading-container">
        <el-spinner />
      </div>
      <div v-else-if="resourceList.length === 0" class="empty-container">
        <el-empty description="暂无数据" style="margin-top: 200px;" />
      </div>
      <div v-else class="list-box">
        <div class="list-item" v-for="(item, index) in resourceList" :key="index" @click="goResourceDetail(item)">
          <div class="img-box">
            <img 
              :src="item.coverUrl || noCoverImg"
              class="item-image"
              :class="{ 'no-cover': !item.coverUrl }"
              @error="onImgError"
            />
          </div>
          <div class="item-details">
            <div class="resource-title">{{ item.assetsName || '-' }}</div>
            <div class="resource-info">
              <div>资源目录: {{ item.catalogNo || '-' }}</div>
              <div class="description">
                资源描述: {{ item.assetsRemark || '-' }}
              </div>
            </div>
          </div>
          <div class="view-count">
            <i />
            <span>{{ item.viewCount || 0 }}</span>
          </div>
        </div>
      </div>
      
      <!-- Pagination -->
      <div class="pagination-container" v-if="total > 0">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          background 
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { queryList } from "/@/api/personCenter";
import { useMessage } from '/@/hooks/message';
import noCoverImg from "/@/assets/img/squareNew/no_cover.png";
import { ElMessage } from 'element-plus'

const props = defineProps({
  type: {
    type: String,
    default: "myCollect", // Default to myCollect if not specified
  },
});
const dataMap: any = {
  'myCollect': { title: '我的收藏', type: 5 },
  'myLike': { title: '我的点赞', type: 2 },
  'myShare': { title: '我的分享', type: 4 },
  'myDownload': { title: '我的下载', type: 7 },
}

const resourceList = ref<any>([]);
const loading = ref(false);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);

// Handle image loading error
const onImgError = (e: any) => {
  e.target.src = noCoverImg;
  e.target.classList.add("no-cover");
};

// Fetch data based on current type prop
const fetchData = async () => {
  loading.value = true;
  try {
    const apiType = dataMap[props.type].type;
    const res = await queryList({
      current: currentPage.value,
      size: pageSize.value,
      type: apiType
    });
    
    if (res.code === 0 && res.data) {
      resourceList.value = res.data.records || [];
      total.value = res.data.total || 0;
    } else {
      resourceList.value = [];
      total.value = 0;
      useMessage().error(res.msg || '获取数据失败');
    }
  } catch (error) {
    resourceList.value = [];
    total.value = 0;
    useMessage().error('获取数据失败');
  } finally {
    loading.value = false;
  }
};

// Pagination handlers
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  fetchData();
};

const handleCurrentChange = (val: number) => {
  currentPage.value = val;
  fetchData();
};

// Initialize data on component mount
onMounted(() => {
  fetchData();
});

// 跳转详情页面
const goResourceDetail = (record: any) => {
  if (!record.resourceId || !record.tabName) {
    return ElMessage.warning("该资源暂时无法查看（缺少参数）");
  }
  if (!record.viewed) {
    return ElMessage.warning("暂无查看权限");
  }
  window.open(`/#/squareNew/resource/detail/${record.resourceId}?type=${record.tabName === 'lea_material' ? 1 : 2}&tabName=${record.tabName}`);
};
</script>

<style scoped lang="scss">
.table-container {
  display: flex;
  flex-direction: column;
  
  .table-title-box {
    text-align: center;
    position: relative;
    font-weight: 900;
    font-size: 24px;
    line-height: 24.61px;
    letter-spacing: 0%;
    text-align: center;
    vertical-align: middle;
    color: #393939;
    margin-bottom: 30px;
  }
  
  .table-content-box {
    position: relative;
    overflow: visible;

    .loading-container, .empty-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
      color: #a1a1a1;
      font-size: 16px;
    }

    .list-box {
      height: auto;
      overflow: visible;
      
      .list-item {
        display: flex;
        gap: 30px;
        padding: 20px 27px;
        border-bottom: 1px solid #eee;
        background-color: #fff;
        position: relative;
        cursor: pointer;

        &:hover {
          background-color: #fcfcfc;
        }

        &:last-child {
          border-bottom: none;
          margin-bottom: 0;
        }

        .img-box {
          width: 148px;
          height: 187px;
          border-radius: 4px;
          overflow: hidden;
          flex-shrink: 0;
          background-color: #f5f5f5;
          display: flex;
          align-items: center;
          
          .item-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            
            &.no-cover {
              width: 70%;
              height: auto;
              margin: 0 auto;
              object-fit: contain;
            }
          }
        }

        .item-details {
          flex-grow: 1;

          .resource-title {
            font-family: "Source Han Serif CN";
            font-weight: 700;
            font-size: 24px;
            line-height: 24.61px;
            letter-spacing: 0%;
            color: #393939;
            margin-bottom: 30px;
          }

          .resource-info {
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0%;
            color: #a1a1a1;
            > div {
              margin-bottom: 9px;
            }
            .keyword {
              color: #c00;
              margin-right: 5px;
            }
          }
        }

        .view-count {
          display: flex;
          align-items: center;
          position: absolute;
          right: 20px;
          color: #862626;
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 14px;

          i {
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            &::before {
              content: "";
              display: inline-block;
              width: 22px;
              height: 20px;
              background: url("/@/assets/img/squareNew/eyePrimaryIcon.png") no-repeat
                center;
              background-size: contain;
            }
          }
        }
      }
    }
    
    .pagination-container {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
}
</style>
