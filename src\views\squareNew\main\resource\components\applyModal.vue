<template>
  <el-dialog
    v-model="visible"
    title="权限申请"
    width="600px"
    :before-close="handleClose"
    class="apply-modal"
  >
    <div class="apply-content">
      <el-form
        ref="formRef"
        :model="applyForm"
        :rules="formRules"
        label-width="100px"
        label-position="left"
        v-loading="loading"
      >
        <!-- 申请权限 -->
        <el-form-item label="申请权限" prop="permission" class="permission-item">
          <div class="permission-options">
            <el-radio-group v-model="applyForm.permission">
              <div class="radio-item">
                <el-radio label="1">仅查看（仅支持资源查看、点赞、收藏）</el-radio>
              </div>
              <div class="radio-item">
                <el-radio label="2">
                  可下载（支持资源查看、点赞、收藏、下载）
                </el-radio>
              </div>
            </el-radio-group>
          </div>
        </el-form-item>

        <!-- 资源用途 -->
        <el-form-item label="资源用途" prop="purpose" class="purpose-item">
          <el-select
            v-model="applyForm.purpose"
            placeholder="请选择资源用途"
            class="purpose-select"
          >
            <el-option label="学习研究" value="1" />
            <el-option label="文化传播" value="2" />
            <el-option label="商业应用" value="3" />
            <el-option label="其他" value="4" />
          </el-select>
        </el-form-item>

        <!-- 申请事由 -->
        <el-form-item label="申请事由" prop="reason" class="reason-item">
          <el-input
            v-model="applyForm.reason"
            type="textarea"
            :rows="4"
            placeholder="请输入申请事由"
            maxlength="500"
            show-word-limit
            class="reason-textarea"
          />
        </el-form-item>
      </el-form>

      <!-- 备注信息 -->
      <div class="notice-info">
        <el-icon class="notice-icon"><InfoFilled /></el-icon>
        <span class="notice-text">备注：审批通过后将自动发送系统通知，请关注。</span>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import { InfoFilled } from "@element-plus/icons-vue";
import type { FormInstance, FormRules } from "element-plus";
import { useMessage } from "/@/hooks/message";
import {
  getReourceFlowDetail, 
  createReourceFlow, 
  getReourceFlowId 
} from "/@/api/resource/data/resource";

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  resourceId: {
    type: String,
    default: "",
  },
  resourceTitle: {
    type: String,
    default: "",
  },
  tabName: {
    type: String,
    default: "",
  }
});

// Emits
const emit = defineEmits(["update:visible", "close", "refresh"]);

// 响应式数据
const visible = computed({
  get: () => props.visible,
  set: (val) => emit("update:visible", val),
});

const loading = ref(false);
const formRef = ref<FormInstance>();

// 申请表单数据
const applyForm = reactive({
  permission: "1", // 默认选择仅查看权限
  purpose: "",
  reason: "",
});

// 表单验证规则
const formRules: FormRules = {
  permission: [{ required: true, message: "请选择申请权限", trigger: "change" }],
  purpose: [{ required: true, message: "请选择资源用途", trigger: "change" }],
  reason: [
    { required: true, message: "请输入申请事由", trigger: "blur" },
  ],
};

// 方法
const handleClose = () => {
  visible.value = false;
  emit("close");
  resetForm();
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  applyForm.permission = "1";
  applyForm.purpose = "";
  applyForm.reason = "";
};

// 提交申请
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    // 验证表单
    await formRef.value.validate();
    loading.value = true;

    // 获取流程ID
    let res1 = await getReourceFlowId();
    if (res1.data.length == 0) {
      useMessage().wraning("申请流程未配置，请先配置流程");
      return;
    }
    
    let authCode = res1.data[0].items[0].flowId;
    
    // 获取流程详情
    let res2 = await getReourceFlowDetail({ flowId: authCode });
    let list = JSON.parse(res2.data.formItems);
    let obj_c: any = {};
    list.forEach((a: any) => {
      obj_c[a.name] = a.id;
    });
    
    // 构建申请参数
    let options = [
      {
        key: "1",
        value: "查看详情",
      },
      {
        key: "2",
        value: "下载",
      },
    ];
    
    let params: any = {
      type: 1,
      tabName: props.tabName,
      resourceId: props.resourceId,
      flowId: authCode,
      map: {}
    };
    
    // 设置申请权限和申请理由
    params.map[obj_c["申请理由"]] = applyForm.reason;
    params.map[obj_c["申请权限"]] = [options[parseInt(applyForm.permission) - 1]];
    
    // 如果有资源用途字段，也添加进去
    if (obj_c["资源用途"]) {
      const purposeOptions: any = {
        "1": "学习研究",
        "2": "文化传播",
        "3": "商业应用",
        "4": "其他"
      };
      // params.map[obj_c["资源用途"]] = purposeOptions[applyForm.purpose];
      params.useTo = purposeOptions[applyForm.purpose];
    }
    // 提交申请
    await createReourceFlow(params);
    useMessage().success("已发送权限申请，请等待审批通过！");
    emit("refresh");
    // 关闭模态框
    handleClose();
  } catch (err) {
    useMessage().error((err as any)?.msg || "操作失败");
  } finally {
    loading.value = false;
  }
};
</script>

<style lang="scss" scoped>
.apply-modal {
  :deep(.el-dialog__header) {
    padding: 20px 20px 10px;
    border-bottom: 1px solid #ebeef5;
  }

  :deep(.el-dialog__body) {
    padding: 20px;
  }

  :deep(.el-dialog__footer) {
    padding: 10px 20px 20px;
    border-top: 1px solid #ebeef5;
  }
}

.apply-content {
  .permission-item {
    :deep(.el-form-item__label) {
      &::before {
        content: "*";
        color: #f56c6c;
        margin-right: 4px;
      }
    }

    .permission-options {
      width: 100%;

      .radio-item {
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        :deep(.el-radio) {
          width: 100%;
          margin-right: 0;

          .el-radio__label {
            font-size: 14px;
            color: #606266;
          }
        }
      }
    }
  }

  .purpose-select {
    width: 100%;
  }

  .reason-textarea {
    width: 100%;
  }

  .notice-info {
    display: flex;
    align-items: center;
    margin-top: 15px;
    color: #909399;
    font-size: 13px;

    .notice-icon {
      margin-right: 5px;
      color: #e6a23c;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
