import request from "/@/utils/request";

// 查询
export function queryList(params?: any) {
  let url = params?.applyStatus === '2,3' ? '/datacenter/resource/apply/page?applyStatuss=2&applyStatuss=3' : '/datacenter/resource/apply/page'
  if (params?.applyStatus === '2,3') {
    delete params.applyStatus
  }
  return request({
    url: url,
    method: "GET",
    params,
  });
}
// 批量取消
export function applyCancel(data?: Object) {
  return request({
    url: "/datacenter/resource/apply/cancel",
    method: "post",
    data,
  });
}
// 按状态统计
export function queryApplyCount(params?: Object) {
  return request({
    url: "/datacenter/resource/apply/count",
    method: "GET",
    params,
  });
}