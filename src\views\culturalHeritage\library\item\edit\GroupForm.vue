<template>
  <el-dialog class="group-form-dlg" v-model="visible" :width="500" :title="form.id ? '编辑条目' : '新增条目'" align-center
    destroy-on-close :show-close="true" :close-on-click-modal="false" :close-on-press-escape="false" @close="onCancel">
    <el-form ref="dataFormRef" :model="form" :rules="dataRules" label-width="100px" v-loading="loading">
      <el-form-item label="条目名称" prop="groupName">
        <el-input v-model="form.groupName" placeholder="请输入条目名称" :maxlength="16" show-word-limit />
      </el-form-item>
      <el-form-item label="条目类型" prop="groupType">
        <el-select placeholder="请选择条目类型" clearable v-model="form.groupType" style="width: 100%;">
          <el-option label="列表" :value="1" />
          <el-option label="表单" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="条目序号" prop="groupSort">
        <el-input-number v-model="form.groupSort" :min="0" controls-position="right" />
      </el-form-item>
      <el-form-item label="条目备注" prop="groupRemark">
        <el-input type="textarea" maxlength="250" :rows="3" v-model="form.groupRemark" placeholder="请输入条目备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button @click="onCancel">取消</el-button>
        <el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useMessage } from "/@/hooks/message";
import { addColumnGroup, editColumnGroup } from "/@/api/resource/catalog/columnInfo";
const emit = defineEmits(["refresh"]);
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);

// 提交表单数据
const form = reactive({
  id: null,
  groupName: "",
  groupType: "",
  groupSort: 0,
  metaDataId: "",
  groupRemark: "",
  tableName: "",
});

// 定义校验规则
const dataRules = ref({
  groupName: [{ required: true, message: "目录名称不能为空", trigger: "blur" }],
  groupType: [{ required: true, message: "目录类型不能为空", trigger: "blur" }],
});

// 打开弹窗，赋值
const openDialog = (record?: any) => {
  visible.value = true;
  Object.assign(form, {
    id: null,
    groupName: "",
    groupType: "",
    groupSort: 0,
    metaDataId: "",
    groupRemark: "",
    tableName: "",
  });
  if (record?.id) {
    Object.assign(form, record);
  }
  form.metaDataId = record.metaDataId;
};

// 关闭
const onCancel = () => {
  visible.value = false;
};

// 提交
const onSubmit = async () => {
  const valid = await dataFormRef.value.validate().catch(() => { });
  if (!valid) return false;
  try {
    loading.value = true;
    form.id ? await editColumnGroup(form) : await addColumnGroup(form);
    useMessage().success(form.id ? "编辑成功" : "新增成功");
    visible.value = false;
    emit("refresh");
  } catch (err) {
    useMessage().error((err as any).msg);
  } finally {
    loading.value = false;
  }
};

defineExpose({
  openDialog,
});
</script>

<style lang="scss" scoped>
.group-form-dlg {
  .permission-title {
    font-size: 16px;
    margin: 0 0 20px 15px;
    padding-left: 15px;
    position: relative;

    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 15%;
      width: 3px;
      height: 70%;
      border-radius: 6px;
      background: var(--el-color-primary);
    }
  }

  .permission-tip {
    font-size: 12px;
    color: #999;
    margin: 0 0 20px 35px;

    &.important {
      color: #ff7200;
    }
  }
}
</style>
