import request from "/@/utils/request";

// 查询建筑空间类型列表
export function getBuildingType() {
    return request({
        url: "/exhibition/buildingSpace/buildingTypeList",
        method: "get"
    });
}

// 查询建筑空间树
export function getBuildingTree(query?: any) {
    return request({
        url: `/exhibition/buildingSpace/buildingTree?buildingName=${query.buildingName}`,
        method: "get"
    });
}

// 新增或修改建筑空间
export function updateBuilding(data?: Object) {
    return request({
        url: "/exhibition/buildingSpace",
        method: "put",
        data: data,
    });
}

// 新增或修改建筑空间
export function addBuilding(data?: Object) {
    return request({
        url: "/exhibition/buildingSpace",
        method: "post",
        data: data,
    });
}

// 获取建筑信息
export function getBuildingInfo(id?: Object) {
    return request({
        url: `/exhibition/buildingSpace/${id}`,
        method: "get"
    });
}

// 删除建筑空间
export function deleteBuilding(id?: Object) {
    return request({
        url: `/exhibition/buildingSpace/${id}`,
        method: "delete"
    });
}
