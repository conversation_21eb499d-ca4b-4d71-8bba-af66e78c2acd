<template>
  <div class="layout-padding cultural-heritage-catalog">
    <div class="page-header">
      <el-button type="primary" @click="onOperation('DoAdd')" :icon="Plus">新增</el-button>
      <div class="filter-area">
        <el-form :model="state.queryForm" ref="queryRef" :inline="true">
          <el-form-item label="状态" prop="catalogState">
            <el-select @change="getDataList" v-model="state.queryForm.catalogState" clearable placeholder="请选择状态"
              style="width: 150px;">
              <el-option label="启用" :value="1" />
              <el-option label="禁用" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item prop="catalogName">
            <el-input v-model="state.queryForm.catalogName" placeholder="请输入目录名称搜索" clearable style="width: 200px;"
              @keyup.enter="getDataList" />
          </el-form-item>
          <el-form-item>
            <!-- <el-button type="primary" @click="getDataList" :icon="Search">查询</el-button> -->
            <el-button @click="resetQuery" :icon="Refresh">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <el-table ref="tableRef" :data="state.dataList" style="width: calc(100% - 20px)" row-key="id"
      :tree-props="{ children: 'childCatalogs', hasChildren: 'hasChildren' }" v-loading="state.loading"
      header-cell-class-name="custom-table-header">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column prop="catalogName" label="目录名称" min-width="200" show-overflow-tooltip>
        <template #default="scope">
          <span style="display: inline-flex; align-items: center;">
            <!-- <el-icon v-if="scope.row.catalogLevel == 3" style="margin-right: 5px;"><Document /></el-icon>
            <el-icon v-else style="margin-right: 5px;"><FolderOpened /></el-icon> -->
            <span>{{ scope.row.catalogName }}</span>
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="catalogNo" label="目录编号" width="180" align="center" />
      <el-table-column prop="catalogState" label="状态" width="180" align="center">
        <template #default="scope">
          <el-tag :type="scope.row.catalogState == 1 ? 'success' : 'info'" disable-transitions>
            {{ scope.row.catalogState == 1 ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="catalogCreator" label="创建人" width="180" align="center" />
      <el-table-column prop="catalogCreateTm" label="创建时间" width="180" align="center" />
      <el-table-column label="操作" width="190" align="left" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click.stop="onOperation('DoAdd', scope.row)"
            :disabled="scope.row.catalogLevel >= 3" style="color:#4659D3;">
            新增
          </el-button>
          <el-button link type="primary" @click.stop="onOperation('DoEdit', scope.row)" style="color:#4659D3;">
            编辑
          </el-button>
          <el-button link type="danger" @click.stop="onOperation('DoDelete', scope.row)"
            :disabled="scope.row.childCatalogs?.length > 0">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <MenuDialog @refresh="getDataList()" ref="editorDialogRef" :parentData="state.dataList" />
  </div>
</template>

<script setup lang="ts" name="CulturalHeritageCatalog">
import { ref, reactive, defineAsyncComponent, onMounted } from 'vue';
import { ElTable } from 'element-plus';
import { Plus, Refresh } from '@element-plus/icons-vue';
import { BasicTableProps, useTable } from "/@/hooks/table";
import { useMessage, useMessageBox } from "/@/hooks/message";
import { fetchList, delObj } from "/@/api/resource/catalog/catalog";

// 引入组件
const MenuDialog = defineAsyncComponent(() => import("./form.vue"));

const queryRef = ref();
const tableRef = ref<InstanceType<typeof ElTable>>();
const editorDialogRef = ref();

const state = reactive<BasicTableProps>({
  queryForm: {
    catalogName: null,
    catalogState: null,
  },
  dataList: [],
  loading: false,
  pageList: fetchList,
  isPage: false,
});

// 从useTable中获取方法
const { getDataList } = useTable(state);

// 清空搜索条件
const resetQuery = () => {
  queryRef.value?.resetFields();
  getDataList();
};

// 彻底删除
const onDelete = async (id: any) => {
  try {
    await useMessageBox().confirm("确认彻底删除吗？");
  } catch {
    return;
  }
  try {
    await delObj({ id });
    useMessage().success("删除成功");
    getDataList();
  } catch (err) {
    useMessage().error((err as any).msg);
  }
};

// 操作处理
const onOperation = (type: string, record?: any) => {
  switch (type) {
    case "DoAdd":
      editorDialogRef.value.openDialog(type, record);
      break;
    case "DoEdit":
      editorDialogRef.value.openDialog(type, record);
      break;
    case "DoDelete":
      onDelete(record.id);
      break;
  }
};

// 页面加载时获取数据
onMounted(() => {
  getDataList();
});
</script>

<style scoped lang="scss">
.cultural-heritage-catalog {
  margin: 20px;
  padding: 20px;
  background-color: white;
  height: calc(100% - 20px);
  width: calc(100% - 20px) !important;
  box-sizing: border-box;

  // 顶部标题和按钮区域
  .page-header {
    display: flex;
    justify-content: space-between;

    .filter-area {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      margin-left: 20px;
      margin-right: 10px;
    }
  }

  // 表格样式
  .el-table {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
  }

  // 自定义表头样式
  :deep(.custom-table-header) {
    background-color: var(--next-bg-main-color, #f5f7fa);
    color: rgba(0, 0, 0, 0.4);
    font-weight: 400;
    font-size: 14px;
    height: 50px;
  }

  // 目录名称样式，带有图标
  :deep(.el-table .cell) {
    display: flex;
    align-items: center;

    .el-icon {
      margin-right: 5px;
      font-size: 16px;
      color: #606266;
    }

    .el-button--link {
      padding: 5px 8px;

      &.el-button--primary {
        color: var(--el-color-primary);
      }

      &.el-button--danger {
        color: var(--el-color-danger);
      }

      &:disabled {
        color: #c0c4cc;
        cursor: not-allowed;
        pointer-events: none;
      }
    }
  }

  // 修复居中对齐问题
  :deep(.el-table) {

    th.is-center .cell,
    td.is-center .cell {
      justify-content: center !important;
    }

    th.is-right .cell,
    td.is-right .cell {
      justify-content: flex-end !important;
    }

    th.is-left .cell,
    td.is-left .cell {
      justify-content: flex-start !important;
    }
  }

  // 状态标签样式
  :deep(.el-tag) {
    border-radius: 4px;
    padding: 0 10px;
    height: 28px;
    line-height: 28px;
    font-size: 12px;

    &.el-tag--success {
      background-color: var(--next-color-success-lighter, #f0f9eb);
      border-color: #2BA471;
      color: #2BA471;
    }

    &.el-tag--info {
      background-color: #f4f4f5;
      border-color: #0052D9;
      color: #0052D9;
    }
  }
}
</style>