import request from "/@/utils/request";

// 资源列表查询
export function fetchList(data?: Object) {
  return request({
    url: "/datacenter/data/resource/page",
    method: "post",
    data: data,
  });
}

// 资源列表查询
export function getColumn(params?: Object) {
  return request({
    url: "/datacenter/data/resource/column",
    method: "get",
    params: params,
  });
}

// 根据ID查询资源
export function getModelDataById(params?: any, searchFlag = 0) {
  return request({
    url: searchFlag ? "/datacenter/explore/defaultColumn/value" : "/datacenter/data/resource/defaultColumn/value",
    method: "get",
    params: params,
  });
}

// 获取所有动态表单
export function getAllColumns(params?: Object) {
  return request({
    url: "/datacenter/columnInfo/allColumns",
    method: "get",
    params: params,
  });
}

// 按照组分段获取信息
export function getColumnInfo(params?: Object, searchFlag = 0) {
  return request({
    url: searchFlag ? "/datacenter/explore/segment" : "/datacenter/data/resource/segment",
    method: "get",
    params: params,
  });
}
// 新增数据
export function addModelData(data?: Object) {
  return request({
    url: "/datacenter/data/resource/addData",
    method: "post",
    data: data,
  });
}
// 更新数据
export function updateModelData(data?: any) {
  return request({
    url: "/datacenter/data/resource/updateData/" + data.id,
    method: "put",
    data: data,
  });
}

// 更新资源与文件关联关系
export function updateResourceOfFile(data?: any) {
  return request({
    url: "/datacenter/learning/material/batchBindFile",
    method: "post",
    data: data,
  });
}

// 移除资源与文件关联关系
export function delResourceOfFile(data?: any) {
  return request({
    url: "/datacenter/learning/material/unBindFile",
    method: "post",
    data: data,
  });
}

// 删除数据
export function deleteModelData(data?: Object) {
  return request({
    url: "/datacenter/data/resource/delData",
    method: "post",
    data: data,
  });
}

// /api/task/combination/group/listCurrentUserStartGroup?hidden=false&processName=申请资源
// 获取资源申请界面信息
export function getReourceFlowId(params?: Object) {
  return request({
    url: "/task/combination/group/listCurrentUserStartGroup?hidden=false&processName=申请资源",
    method: "get",
    params: params,
  });
}
// /api/task/process/getDetail?flowId=P20240802100241031JPBLZ
// 获取资源申请界面信息
export function getReourceFlowDetail(params?: Object) {
  return request({
    url: "/task/process/getDetail",
    method: "get",
    params: params,
  });
}
// 发起申请
export function createReourceFlow(data?: Object) {
  return request({
    url: "/datacenter/resource/apply/create",
    method: "post",
    data: data,
  });
}

// 资源列表查询
export function fetchListMaterial(params?: Object) {
  return request({
    url: "/datacenter/learning/material/resource/page",
    method: "get",
    params,
  });
}

