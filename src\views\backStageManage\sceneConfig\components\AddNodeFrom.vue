<template>
	<div class="node-form">
		<el-form ref="formRef" :model="formData" :rules="formRules" :label-position="'top'" label-width="100px" class="form-content">
			<!-- 基本信息 -->
			<div class="form-section">
				<h4 class="section-title">
					<el-icon><Location /></el-icon>
					基本信息
				</h4>

				<el-form-item label="节点名称" prop="nodeName" required>
					<el-input v-model="formData.nodeName" placeholder="请输入名称" maxlength="50" show-word-limit />
				</el-form-item>

				<el-form-item label="显隐时间" prop="displayTime" required>
					<el-time-picker v-model="formData.displayTime" format="HH:mm:ss" value-format="HH:mm:ss" placeholder="开始时间" style="width: 48%" />
					<span style="margin: 0 8px">至</span>
					<el-time-picker v-model="formData.endTime" format="HH:mm:ss" value-format="HH:mm:ss" placeholder="结束时间" style="width: 48%" />
				</el-form-item>

				<el-form-item label="节点简介" prop="nodeDescription">
					<el-input v-model="formData.nodeDescription" type="textarea" placeholder="请输入节点简介" :rows="4" maxlength="500" show-word-limit />
				</el-form-item>

				<el-form-item label="节点图片">
					<div class="upload-container">
						<ImageUpload
							ref="imageUploadRef"
							:file-id="formData.imageFileId"
							@upload-success="handleImageUploadSuccess"
							@upload-failure="handleImageUploadFailure"
							@delete="handleImageDelete"
						/>
						<div class="upload-hint">支持JPG/PNG格式，且最大20M</div>
					</div>
				</el-form-item>
			</div>
		</el-form>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, defineAsyncComponent } from 'vue';
import { Location } from '@element-plus/icons-vue';

const ImageUpload = defineAsyncComponent(() => import('/@/components/Upload/Image.vue'));

interface NodeFormData {
	nodeName: string;
	nodeOrder: number;
	nodeDescription: string;
	imageFileId: string;
	imageUrl: string;
	displayTime: string;
	endTime: string;
}

interface Emits {
	(e: 'data-change', data: NodeFormData): void;
}

const emit = defineEmits<Emits>();

// 响应式数据
const formRef = ref();
const imageUploadRef = ref();

const formData = reactive<NodeFormData>({
	nodeName: '',
	nodeOrder: 1,
	nodeDescription: '',
	imageFileId: '',
	imageUrl: '',
	displayTime: '',
	endTime: '',
});

// 表单验证规则
const formRules = {
	nodeName: [
		{ required: true, message: '请输入节点名称', trigger: 'blur' },
		{ min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' },
	],
	displayTime: [{ required: true, message: '请选择显示时间', trigger: 'change' }],
};

// 监听表单数据变化
watch(
	formData,
	(newData) => {
		emit('data-change', { ...newData });
	},
	{ deep: true }
);

// 图片上传成功
const handleImageUploadSuccess = (fileData: any) => {
	formData.imageFileId = fileData.id;
	formData.imageUrl = fileData.url;
};

// 图片上传失败
const handleImageUploadFailure = (error: any) => {
	// 处理上传失败
};

// 图片删除
const handleImageDelete = () => {
	formData.imageFileId = '';
	formData.imageUrl = '';
};

// 获取表单数据
const getFormData = () => {
	return { ...formData };
};

// 设置表单数据
const setFormData = (data: Partial<NodeFormData>) => {
	Object.assign(formData, data);
};

// 验证表单
const validateForm = async () => {
	if (!formRef.value) return false;
	try {
		await formRef.value.validate();
		return true;
	} catch {
		return false;
	}
};

// 重置表单
const resetForm = () => {
	if (formRef.value) {
		formRef.value.resetFields();
	}
	Object.assign(formData, {
		nodeName: '',
		nodeOrder: 1,
		nodeDescription: '',
		imageFileId: '',
		imageUrl: '',
	});
};

// 暴露方法
defineExpose({
	getFormData,
	setFormData,
	validateForm,
	resetForm,
});
</script>

<style lang="scss" scoped>
.node-form {
	padding: 0 20px;

	.form-content {
		.form-section {
			margin-bottom: 24px;

			.section-title {
				display: flex;
				align-items: center;
				margin: 0 0 16px 0;
				font-size: 14px;
				font-weight: 600;
				color: #1d2129;
			}
		}

		.upload-container {
			.upload-hint {
				margin-top: 8px;
				font-size: 12px;
				color: #86909c;
			}
		}
	}
}

:deep(.el-form-item__label) {
	color: #1d2129;
	font-weight: 500;
}

:deep(.el-input__wrapper) {
	border-radius: 6px;
}

:deep(.el-textarea__inner) {
	border-radius: 6px;
}

:deep(.el-input-number) {
	width: 100%;
}
</style>
