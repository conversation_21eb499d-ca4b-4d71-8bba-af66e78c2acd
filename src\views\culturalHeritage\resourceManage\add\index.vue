<template>
  <div class="layout-padding w100">
    <div class="layout-padding-view layout-border-radius" :style="{ backgroundColor: '' }" style="border: none;">
      <div class="editor-header flex" :style="{ backgroundColor: '', borderRadius: '0px' }">
        <el-tabs v-model="activeName" @tab-click="handleClick" class="editor-header-title tabs" :style="{ pointerEvents: 'none' }">
          <el-tab-pane label="新增资源" name="新增资源"></el-tab-pane>
        </el-tabs>
      </div>
      <tab1 :editRecord="props.editRecord" @backTo="backTo" />
    </div>
  </div>
</template>

<script setup lang="ts">
  const props = defineProps({
    editRecord: {
      type: Object,
      default: null,
    },
  });
  const emit = defineEmits(["backTo"]);
  // 引入组件
  const tab1 = defineAsyncComponent(() => import("./tab1.vue"));

  const activeName = ref("基本信息");
  const backTo = () => {
    emit("backTo");
  };
  const handleClick = (tab: any, event: any) => {};
  onMounted(() => {
    props.editRecord.resourceId = props.editRecord?.resourceId || props.editRecord?.id;
    props.editRecord.tabName = props.editRecord?.tabName || props.editRecord?.tableName;
    props.editRecord.tableName = props.editRecord?.tabName || props.editRecord?.tableName;
  });
</script>

<style lang="scss" scoped>
  .editor-header {
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    margin-bottom: -25px;
    .editor-header-title {
      font-size: 18px;
      width: 100%;
    }
  }

  ::v-deep(.form-subTitle) {
    width: 100%;
    font-size: 16px;
    position: relative;
    text-indent: 35px;
    color: #554242;
    .split-line {
      position: absolute;
      left: 25px;
      height: 18px;
      width: 3px;
      background-color: #695858;
      margin-top: 2px;
    }
  }
  ::v-deep(.dialog-footer) {
    position: absolute;
    right: 20px;
    top: -40px;
  }
  ::v-deep(.el-dialog__header) {
    padding-bottom: 0px;
    margin-bottom: -5px;
  }
  .tabs {
    :deep(.el-tabs__nav) {
      padding: 0 20px;
      .el-tabs__item {
        font-size: 16px;
        font-weight: bold;
      }
    }
  }
</style>
