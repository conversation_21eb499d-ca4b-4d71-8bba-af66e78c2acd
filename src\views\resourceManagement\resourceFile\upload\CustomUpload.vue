<template>
  <div class="upload-content mt-button" :style="{ height: '170px' }">
    <el-upload
      ref="fileUpload"
      :before-upload="handleBeforeUpload"
      v-model:file-list="fileList"
      :limit="1"
      :auto-upload="false"
      :on-error="handleUploadError"
      :on-remove="handleRemove"
      :on-change="handleChange"
      :on-success="handleUploadSuccess"
      :accept="'.' + props.fileType.join(',.')"
      :on-progress="handleProgress"
      :on-exceed="handleExceed"
      class="upload-file-uploader"
      :multiple="false"
      :show-file-list="true"
      :http-request="uploadFileToOss"
    >
      <template #trigger>
        <el-button
          class="select-file"
          :disabled="
            ['md5', 'uploading', 'serverHanding'].includes(fileList[0]?.stepCode)
          "
          >选择文件</el-button
        >
      </template>
      <template #tip>
        <div class="ub-left">
          <div class="ubl-title">上传要求-{{ props.fileTypeOBJ?.name }}</div>
          <div
            class="ubl-tip"
            v-for="(obj, index) in props.fileTypeOBJ?.tips"
            :key="index"
            :style="{ color: obj.color || '#000000' }"
          >
            {{ index + 1 + "." + obj.tip }};
          </div>
        </div>
      </template>
      <el-button
        @click="submit"
        type="primary"
        class="start-upload"
        :disabled="['md5', 'uploading', 'serverHanding'].includes(fileList[0]?.stepCode)"
        >开始上传</el-button
      >
      <template #file="obj">
        <div
          class="upload-item"
          :style="{
            pointerEvents: ['serverHanding'].includes(fileList[0].stepCode)
              ? 'none'
              : 'auto',
          }"
        >
          <div class="file-title" style="z-index: 99">
            <el-icon style="font-size: 17px" class="ft-file"><Document /></el-icon>
            <el-icon
              style="font-size: 17px"
              class="ft-delete"
              @click="removeFile(obj.file)"
              ><Delete
            /></el-icon>
            <span>{{ obj.file.name }}</span>
          </div>
          <div class="file-size">
            <span>{{ Math.round((fileList[0].size / 1024 / 1024) * 100) / 100 }}MB</span>
          </div>
          <div class="file-status">
            <span
              :style="{ color: fileList[0].stepCode == 'fail' ? '#ff0000' : '#000000' }"
              >{{ uploadConfig[fileList[0].stepCode] || fileList[0].stepCode }}</span
            >
            <span v-if="fileList[0].stepCode == 'md5'"
              >：{{ (fileList[0].md5Percent || 0.0).toFixed(1) }}%</span
            >
            <span v-else-if="fileList[0].stepCode == 'uploading'"
              >：{{ (fileList[0].uploadingPercent || 0.0).toFixed(1) }}%</span
            >
            <span v-else-if="fileList[0].stepCode == 'serverHanding'"
              >：{{ (fileList[0].serverPercent || 0.0).toFixed(1) }}%</span
            >
            <el-icon
              v-else-if="fileList[0].stepCode == 'fail'"
              style="font-size: 17px; color: #ff0000"
              ><CircleCloseFilled
            /></el-icon>
            <el-icon
              v-else-if="fileList[0].stepCode == 'success'"
              style="font-size: 17px; color: #58d094"
              ><SuccessFilled
            /></el-icon>
          </div>
          <div style="position: relative; width: 100%; height: 100%">
            <el-progress
              style="margin-top: 10px"
              :percentage="fileList[0].md5Percent"
              :color="fileList[0].stepCode == 'fail' ? '#ff0000' : '#58D094'"
              :stroke-width="10"
              :show-text="false"
              striped
            />
            <el-progress
              style="margin-top: 10px"
              :percentage="fileList[0].uploadingPercent"
              :color="fileList[0].stepCode == 'fail' ? '#ff0000' : 'var(--el-color-warning)'"
              :stroke-width="10"
              :show-text="false"
              striped
            />
            <el-progress
              style="margin-top: 10px"
              :percentage="fileList[0].serverPercent"
              :color="fileList[0].stepCode == 'fail' ? '#ff0000' : 'var(--el-color-success)'"
              :stroke-width="10"
              :show-text="false"
              striped
            />
          </div>
        </div>
      </template>
    </el-upload>
  </div>
</template>

<script setup lang="ts" name="upload-file">
import axios from "axios";
import { useMessage } from "/@/hooks/message";
import { Session } from "/@/utils/storage";
import { useI18n } from "vue-i18n";
import { genFileId } from "element-plus";
import { uploadPercent, checkFileUnique } from "/@/api/resource/filelist/file";
import BMF from "browser-md5-file";
import { uploadConfig } from "/@/config/resourceConfig";
import { resourceFileUploaderStore } from "/@/stores/resourceFileUploader";
const rfuStore = resourceFileUploaderStore();

const props = defineProps({
  modelValue: [String, Array],
  // 大小限制(MB)
  fileSize: {
    type: Number,
    default: 5,
  },
  fileType: {
    type: Array,
    // default: () => ["png", "jpg", "jpeg", "doc", "xls", "ppt", "txt", "pdf", "docx", "xlsx", "pptx"],
    default: () => [],
  },
  fileTypeOBJ: {
    type: Object as any,
    default: () => {},
  },
  uploadFileUrl: {
    type: String,
    default: "/datacenter/learning/material/upload",
  },
  format: {
    type: String,
    default: "",
  },
  uploadResType: {
    type: String,
    default: '6',
  }
});

const emit = defineEmits(["update:modelValue", "change", "fileUploadSuccess"]);
const fileList = ref([]) as any;
const fileUpload = ref();
const serverTimer_1 = ref();
const serverTimer_2 = ref();
const { t } = useI18n();

// 请求头处理
const headers = computed(() => {
  return {
    Authorization: "Bearer " + Session.get("token"),
    "TENANT-ID": Session.getTenant(),
    "Content-Type": "multipart/form-data",
  };
});
// 上传前校检格式和大小
const handleBeforeUpload = async (file: File) => {
  if (!validateFile(file)) return false;
  return true;
};
const sleep = (delay: any) => new Promise((resolve) => setTimeout(resolve, delay));
const validateFile = (file: File) => {
  // 校检文件类型
  if (props.fileType.length) {
    const fileName = file.name.split(".");
    const fileExt = fileName[fileName.length - 1];
    const isTypeOk = props.fileType.indexOf(fileExt) >= 0;
    if (!isTypeOk) {
      useMessage().error(`${t("excel.typeErrorText")} ${props.fileType.join("/")}!`);
      return false;
    }
  }
  // 校检文件大小
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize;
    if (!isLt) {
      useMessage().error(`${t("excel.sizeErrorText")} ${props.fileSize} MB!`);
      return false;
    }
  }
  return true;
};
// 上传结束回调
async function handleUploadSuccess(res: any, file: any) {
  clearInterval(serverTimer_1.value);
  clearInterval(serverTimer_2.value);
  if (res?.data) {
    fileList.value[0].stepCode = "success";
    fileList.value[0].serverPercent = 100;
    fileList.value[0].result = res.data;
    rfuStore.updateFileOBJ(fileList.value[0]);
    await sleep(500);
    emit("change", fileList.value);
    emit("update:modelValue", fileList.value);
    emit("fileUploadSuccess", fileList.value[0]);
  }
}
const handleRemove = (file: any) => {
  // debugger;
  clearInterval(serverTimer_1.value);
  clearInterval(serverTimer_2.value);
  // fileList.value = fileList.value.filter((f: any) => !(f === file.url));
  fileList.value = [];
  emit("change", fileList.value);
  emit("update:modelValue", fileList.value);
};
const handleExceed = (files: any) => {
  fileUpload.value!.clearFiles();
  fileList.value = [];
  const file = files[files.length - 1];
  file.uid = genFileId();
  fileUpload.value!.handleStart(file);
};

const handleUploadError = (error: Error, uploadFile: any) => {
  clearInterval(serverTimer_1.value);
  clearInterval(serverTimer_2.value);
  uploadFile.error = "上传文件失败";
  uploadFile.stepCode = "fail";
  // uploadFile.serverPercent = 100;
  // debugger;
  rfuStore.updateFileOBJ(uploadFile);
  useMessage().error("上传文件失败");
};

/**
 * 监听 props 中的 modelValue 值变化，更新 fileList。
 */
watch(
  () => props.modelValue,
  (val: any) => {
    if (val?.length > 0) {
      fileList.value = val;
    } else {
      fileList.value = [];
    }
  },
  { deep: true, immediate: true }
);
const handleChange = async (file: any, uploadFiles: any) => {
  clearInterval(serverTimer_1.value);
  clearInterval(serverTimer_2.value);
  if (file.status == "ready") {
    file.stepCode = "ready";
    let isOk = validateFile(file);
    if (!isOk) {
      // fileList.value = fileList.value.filter((f: any) => f.uid != file.uid);
      return;
    }
    // 校验是否有重复文件
    if (fileList.value.some((f: any) => f.name === file.name)) {
      useMessage().error("文件已存在");
      // fileList.value = fileList.value.filter((f: any) => f.uid != file.uid);
      return;
    }
    fileList.value.push(file);
    emit("change", fileList.value);
    emit("update:modelValue", fileList.value);
  }
};
const handleProgress = async (evt: any, uploadFile: any, uploadFiles: any) => {
  console.log("upload progress:", evt, uploadFile);
  // rfuStore.updateFileOBJ(uploadFile);
  // debugger;
};
const removeFile = (file: any) => {
  fileList.value[0].cancelFile?.();
  fileList.value = [];
  emit("change", fileList.value);
  emit("update:modelValue", fileList.value);
};
const submit = () => {
  fileUpload.value.submit();
};
const uploadFileToOss = async (object: any, index: number) => {
  let file = object.file;
  // 第一步：进行MD5加密 （对文件进行md5计算，节省云端存储空间）
  const bmf = new BMF();
  fileList.value[0].cancelFile = (isHideTip?: any) => {
    !isHideTip && useMessage().wraning("已取消上传");
    bmf && bmf.abort();
    clearInterval(serverTimer_1.value);
    clearInterval(serverTimer_2.value);
  };
  fileList.value[0].stepCode = "md5";
  const md5: any = await new Promise((resolve, reject) => {
    bmf.md5(
      file,
      (err: any, md5: string) => {
        if (err && err != "aborted") {
          reject(err);
          if (fileList.value.length > 0) {
            fileList.value[0].error = err;
            rfuStore.updateFileOBJ(fileList.value[0]);
          }
        }
        resolve(md5);
      },
      (progress: number) => {
        if (fileList.value.length > 0) {
          fileList.value[0].md5Percent = progress * 100;
          rfuStore.updateFileOBJ(fileList.value[0]);
          // console.log("md5Percent：", fileList.value[0].md5Percent);
        }
      }
    );
  });
  await sleep(300);
  // 校验文件是否存在，存在则无需重新上传
  const res_check = await checkFileUnique({ fileIdentifier: md5, totalSize: file.size });
  if (res_check.data) {
    fileList.value[0].uploadingPercent = 100;
    await sleep(500);
    fileList.value[0].stepCode = "uploading";
    fileList.value[0].serverPercent = 100;
    await sleep(500);
    object.onSuccess(res_check);
  } else {
    // 第二步：文件上传后端
    fileList.value[0].fileIdentifier = md5;
    fileList.value[0].stepCode = "uploading";
    fileList.value[0].uploadingPercent = 0;
    rfuStore.updateFileOBJ(fileList.value[0]);
    let formdata = new FormData();
    formdata.append("folderId", "1");
    formdata.append("fileType", props.uploadResType);
    formdata.append("fileFlag", "true");
    formdata.append("fileIdentifier", md5);
    formdata.append("totalSize", file.size);
    formdata.append("format", props.format);
    formdata.append("file", object.file); //file 一定要在 key 之后 不然会报错😂
    await sleep(300);
    const CancelToken = axios.CancelToken;
    axios({
      method: "post",
      url: props.uploadFileUrl,
      data: formdata,
      headers: headers.value,
      baseURL: import.meta.env.VITE_API_URL,
      timeout: 0, // 全局超时时间
      onUploadProgress: async (progressEvent: any) => {
        if (!fileList.value[0]) return;
        fileList.value[0].uploadingPercent = Number(
          (progressEvent.loaded / progressEvent.total) * 100 || 0
        );
        rfuStore.updateFileOBJ(fileList.value[0]);
        clearInterval(serverTimer_1.value);
        clearInterval(serverTimer_2.value);
        if (
          fileList.value[0].uploadingPercent >= 80 &&
          fileList.value[0].stepCode != "serverHanding"
        ) {
          fileList.value[0].uploadingPercent = Math.min(
            fileList.value[0].uploadingPercent + 1,
            100
          );
          rfuStore.updateFileOBJ(fileList.value[0]);
          serverTimer_1.value = setInterval(async () => {
            fileList.value[0].uploadingPercent = Math.min(
              fileList.value[0].uploadingPercent + 1,
              100
            );
            rfuStore.updateFileOBJ(fileList.value[0]);
            // 第三步：后端解析文件，查询进度
            if (fileList.value[0].uploadingPercent == 100) {
              clearInterval(serverTimer_1.value);
              clearInterval(serverTimer_2.value);
              fileList.value[0].stepCode = "serverHanding";
              fileList.value[0].serverPercent = 0;
              rfuStore.updateFileOBJ(fileList.value[0]);
              await sleep(300);
              let num_index = 0;
              serverTimer_2.value = setInterval(async () => {
                num_index++;
                if (num_index % 5 == 1) {
                  let res = await uploadPercent({ fileIdentifier: md5 });
                  fileList.value[0].serverPercent = Math.min(
                    Math.max(fileList.value[0].serverPercent, (res.data || 0) * 100),
                    99
                  );
                } else {
                  fileList.value[0].serverPercent = Math.min(
                    fileList.value[0].serverPercent + 0.05,
                    99
                  );
                }
                progressEvent.svp = fileList.value[0].serverPercent;
                rfuStore.updateFileOBJ(fileList.value[0]);
              }, 3000);
            }
          }, 1000);
        }
      },
      cancelToken: new CancelToken(function executor(cancel) {
        fileList.value[0].cancelFile = (isHideTip?: any) => {
          !isHideTip && useMessage().wraning("已取消上传");
          clearInterval(serverTimer_1.value);
          clearInterval(serverTimer_2.value);
          bmf && bmf.abort();
          cancel && cancel();
        };
      }),
    })
      .then(async (res: any) => {
        clearInterval(serverTimer_1.value);
        clearInterval(serverTimer_2.value);
        object.onSuccess(res.data);
      })
      .catch((err: any) => {
        fileList.value[0].cancelFile(true);
        clearInterval(serverTimer_1.value);
        clearInterval(serverTimer_2.value);
        if (err.message != "canceled") {
          object.onError(err);
        }
      });
  }
  // debugger;
};
defineExpose({
  submit,
});
</script>
<style lang="scss" scoped>
.upload-content {
  position: relative;
  width: 100%;
  height: 285px;
  .ub-left {
    width: calc(100% - 120px);
    height: 92px;
    background-color: #fafafa;
    padding: 5px 10px;
    border-radius: 5px;
    border: 1px solid #cecece;
    position: absolute;
    top: 0px;
    left: 0px;
    .ubl-title {
      color: var(--el-color-primary);
      font-weight: bolder;
      font-size: 14px;
      letter-spacing: 0.1em;
      height: 30px;
      line-height: 30px;
    }
    .ubl-tip {
      color: #7b7b7b;
      font-size: 12px;
      letter-spacing: 0.1em;
      // height: 25px;
      line-height: 25px;
    }
  }
  .el-button {
    width: 110px;
    height: 40px;
    font-size: 13px;
    font-weight: bolder;
    letter-spacing: 0.2em;
  }
  .select-file {
    position: absolute;
    top: 0px;
    right: 0px;
  }
  .start-upload {
    position: absolute;
    top: 50px;
    right: 0px;
  }
  .upload-list,
  ::v-deep(.el-upload-list) {
    position: absolute;
    top: 95px;
    left: 0px;
    width: 100%;
    height: calc(100% - 95px);
    overflow-y: auto;
    overflow-x: hidden;
    border: 1px solid #dfdfdf;
    padding: 0px 10px;
    .el-upload-list__item:first-child {
      margin-top: 10px;
    }
    .upload-item {
      position: relative;
      width: 100%;
      height: 55px;
      cursor: pointer;
      .ft-delete {
        display: none;
        color: #ff0000;
      }
      &:hover {
        color: var(--el-color-primary);
        .ft-file {
          display: none;
        }
        .ft-delete {
          display: block;
        }
      }
      .file-title {
        position: absolute;
        top: 0px;
        left: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
        span {
          margin-left: 5px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          -o-text-overflow: ellipsis;
          width: 270px;
        }
      }
      .file-size {
        position: absolute;
        top: 0px;
        right: 180px;
      }
      .file-status {
        position: absolute;
        top: 0px;
        right: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
        span {
          margin-right: 5px;
        }
      }
      .el-progress {
        position: relative;
        top: 27px;
        right: 0px;
        width: 33.33%;
        float: left;
        &:first-child {
          .el-progress-bar__outer {
            border-radius: 100px 0px 0px 100px;
            .el-progress-bar__inner {
              border-radius: 100px 0px 0px 100px;
            }
          }
        }
        &:nth-child(2) {
          .el-progress-bar__outer {
            border-radius: 0px 0px 0px 0px;
            .el-progress-bar__inner {
              border-radius: 0px 0px 0px 0px;
            }
          }
        }
        &:last-child {
          .el-progress-bar__outer {
            border-radius: 0px 100px 100px 0px;
            .el-progress-bar__inner {
              border-radius: 0px 100px 100px 0px;
            }
          }
        }
      }
    }
  }
}
</style>
