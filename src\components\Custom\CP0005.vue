<template>
  <div class="mtc-box">
    <div class="mtcb-title">{{ data.title }}</div>
    <div class="mtcb-value">
      <div class="mtcb-num">{{ data.num }}</div>
    </div>
    <div class="mtcb-bottom">
      <slot></slot>
    </div>
  </div>
</template>

<script lang="ts" name="CP0005" setup>
const props = defineProps({
  data: {
    type: Object as any,
    default: { title: "资源项数量（项）", num: "1000" },
  },
});
</script>
<style lang="scss" scoped>
.mtc-box {
  position: relative;
  border-radius: 0;
  height: 100%;
  width: 100%;
  border-right: 2px solid #000000ab;
  background-image: url("/@/assets/img/resourceStatistics/bg_zytj.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-color: #fff3f3;
  border-radius: 8px;
  padding: 15px 20px;
  // box-shadow: 0px 0px 3px 0px #ffb6b6;
  &:last-child {
    border-right: none;
  }
  .mtcb-title {
    width: 100%;
    height: 40px;
    position: absolute;
    margin-bottom: 20px;
    font-family: PingFang SC, PingFang SC;
    font-weight: bold;
    font-size: 15px;
    color: #7c7c7c;
    letter-spacing: 0.1em;
  }
  .mtcb-value {
    width: 100%;
    height: 40px;
    position: absolute;
    margin-top: 25px;
    // display: flex;
    // align-items: baseline;
    // justify-content: center;
    .mtcb-num {
      font-family: yjsz;
      font-weight: bold;
      font-size: 36px;
      color: #000000;
      margin-right: 5px;
      letter-spacing: 0.1em;
    }
    .mtcb-unit {
      font-family: PingFang SC, PingFang SC;
      font-weight: bold;
      font-size: 15px;
      color: #7c7c7c;
    }
  }
  .mtcb-bottom {
    width: 100%;
    height: 40px;
    position: absolute;
    bottom: 0px;
    font-size: 15px;
    font-weight: 400;
    color: #554242;
    font-weight: bolder;
    letter-spacing: 0.1em;
  }
}
</style>
