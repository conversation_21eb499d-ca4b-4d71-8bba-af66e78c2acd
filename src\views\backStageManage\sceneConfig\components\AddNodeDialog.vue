<template>
  <el-dialog
    v-model="dialogVisible"
    title="添加信息节点"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      class="node-form"
    >
      <el-form-item label="节点名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入节点名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="节点描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="2"
          placeholder="请输入节点描述"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="触发时间" prop="timestamp">
        <div class="timestamp-input">
          <el-input-number
            v-model="timestampMinutes"
            :min="0"
            :max="59"
            placeholder="分"
            @change="updateTimestamp"
          />
          <span class="time-separator">:</span>
          <el-input-number
            v-model="timestampSeconds"
            :min="0"
            :max="59"
            placeholder="秒"
            @change="updateTimestamp"
          />
          <span class="form-tip">节点在视频播放到此时间时触发显示</span>
        </div>
      </el-form-item>

      <el-form-item label="显示位置" prop="position">
        <el-select v-model="formData.position" placeholder="请选择显示位置">
          <el-option label="左上角" value="top-left" />
          <el-option label="上方中央" value="top-center" />
          <el-option label="右上角" value="top-right" />
          <el-option label="左侧中央" value="middle-left" />
          <el-option label="屏幕中央" value="center" />
          <el-option label="右侧中央" value="middle-right" />
          <el-option label="左下角" value="bottom-left" />
          <el-option label="下方中央" value="bottom-center" />
          <el-option label="右下角" value="bottom-right" />
        </el-select>
      </el-form-item>

      <el-form-item label="显示时长" prop="duration">
        <el-input-number
          v-model="formData.duration"
          :min="1"
          :max="60"
          placeholder="秒"
          style="width: 200px"
        />
        <span class="form-tip">节点信息显示的持续时间（秒）</span>
      </el-form-item>

      <el-form-item label="内容类型" prop="contentType">
        <el-radio-group v-model="formData.contentType" @change="handleContentTypeChange">
          <el-radio label="text">纯文本</el-radio>
          <el-radio label="image">图片+文字</el-radio>
          <el-radio label="video">视频+文字</el-radio>
          <el-radio label="audio">音频+文字</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="文本内容" prop="textContent">
        <el-input
          v-model="formData.textContent"
          type="textarea"
          :rows="3"
          placeholder="请输入文本内容"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <!-- 图片内容 -->
      <el-form-item v-if="formData.contentType === 'image'" label="图片" prop="imageUrl">
        <div class="media-upload">
          <el-upload
            class="image-uploader"
            :show-file-list="false"
            :on-success="handleImageSuccess"
            :before-upload="beforeImageUpload"
            action="/api/upload"
          >
            <img v-if="formData.imageUrl" :src="formData.imageUrl" class="uploaded-image" />
            <div v-else class="upload-placeholder">
              <el-icon class="upload-icon"><Plus /></el-icon>
              <div class="upload-text">上传图片</div>
            </div>
          </el-upload>
        </div>
      </el-form-item>

      <!-- 视频内容 -->
      <el-form-item v-if="formData.contentType === 'video'" label="视频URL" prop="videoUrl">
        <el-input v-model="formData.videoUrl" placeholder="请输入视频URL" />
      </el-form-item>

      <!-- 音频内容 -->
      <el-form-item v-if="formData.contentType === 'audio'" label="音频URL" prop="audioUrl">
        <el-input v-model="formData.audioUrl" placeholder="请输入音频URL" />
      </el-form-item>

      <el-form-item label="排序" prop="sort">
        <el-input-number
          v-model="formData.sort"
          :min="1"
          :max="999"
          placeholder="请输入排序值"
          style="width: 200px"
        />
        <span class="form-tip">数值越小排序越靠前</span>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { useMessage } from '/@/hooks/message';
import { Plus } from '@element-plus/icons-vue';

interface Props {
  visible: boolean;
  palaceData?: any;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm'): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  palaceData: null,
});

const emit = defineEmits<Emits>();

const { success, error } = useMessage();

// 响应式数据
const formRef = ref();
const loading = ref(false);

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

const formData = reactive({
  name: '',
  description: '',
  timestamp: 0,
  position: 'center',
  duration: 5,
  contentType: 'text',
  textContent: '',
  imageUrl: '',
  videoUrl: '',
  audioUrl: '',
  sort: 1,
});

// 时间输入相关
const timestampMinutes = ref(0);
const timestampSeconds = ref(0);

const formRules = {
  name: [
    { required: true, message: '请输入节点名称', trigger: 'blur' },
    { min: 2, max: 50, message: '节点名称长度在 2 到 50 个字符', trigger: 'blur' },
  ],
  timestamp: [
    { required: true, message: '请设置触发时间', trigger: 'blur' },
  ],
  position: [
    { required: true, message: '请选择显示位置', trigger: 'change' },
  ],
  duration: [
    { required: true, message: '请设置显示时长', trigger: 'blur' },
  ],
  contentType: [
    { required: true, message: '请选择内容类型', trigger: 'change' },
  ],
  textContent: [
    { required: true, message: '请输入文本内容', trigger: 'blur' },
  ],
  sort: [
    { required: true, message: '请输入排序值', trigger: 'blur' },
  ],
};

// 方法
const updateTimestamp = () => {
  formData.timestamp = timestampMinutes.value * 60 + timestampSeconds.value;
};

const handleContentTypeChange = () => {
  // 清空其他类型的内容
  if (formData.contentType !== 'image') formData.imageUrl = '';
  if (formData.contentType !== 'video') formData.videoUrl = '';
  if (formData.contentType !== 'audio') formData.audioUrl = '';
};

const handleClose = () => {
  dialogVisible.value = false;
  resetForm();
};

const resetForm = () => {
  formRef.value?.resetFields();
  Object.assign(formData, {
    name: '',
    description: '',
    timestamp: 0,
    position: 'center',
    duration: 5,
    contentType: 'text',
    textContent: '',
    imageUrl: '',
    videoUrl: '',
    audioUrl: '',
    sort: 1,
  });
  timestampMinutes.value = 0;
  timestampSeconds.value = 0;
};

const handleConfirm = async () => {
  try {
    await formRef.value?.validate();
    loading.value = true;

    const nodeData = {
      ...formData,
      palaceId: props.palaceData?.id,
      type: 'node',
      content: {
        contentType: formData.contentType,
        textContent: formData.textContent,
        imageUrl: formData.imageUrl,
        videoUrl: formData.videoUrl,
        audioUrl: formData.audioUrl,
      },
    };

    // TODO: 调用API保存节点
    await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟API调用

    success('信息节点添加成功');
    emit('confirm');
    handleClose();
  } catch (err) {
    if (err !== false) { // 不是表单验证错误
      error('信息节点添加失败');
    }
  } finally {
    loading.value = false;
  }
};

const handleImageSuccess = (response: any) => {
  if (response.code === 200) {
    formData.imageUrl = response.data.url;
    success('图片上传成功');
  } else {
    error('图片上传失败');
  }
};

const beforeImageUpload = (file: File) => {
  const isImage = file.type.startsWith('image/');
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isImage) {
    error('只能上传图片文件!');
    return false;
  }
  if (!isLt2M) {
    error('图片大小不能超过 2MB!');
    return false;
  }
  return true;
};
</script>

<style scoped lang="scss">
.node-form {
  .form-tip {
    margin-left: 12px;
    color: #909399;
    font-size: 12px;
  }
}

.timestamp-input {
  display: flex;
  align-items: center;
  gap: 8px;

  .el-input-number {
    width: 80px;
  }

  .time-separator {
    font-size: 16px;
    font-weight: bold;
    color: #606266;
  }

  .form-tip {
    margin-left: 12px;
    color: #909399;
    font-size: 12px;
  }
}

.media-upload {
  .image-uploader {
    :deep(.el-upload) {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: all 0.3s;
      width: 200px;
      height: 150px;

      &:hover {
        border-color: #409eff;
      }
    }
  }

  .uploaded-image {
    width: 200px;
    height: 150px;
    object-fit: cover;
    display: block;
  }

  .upload-placeholder {
    width: 200px;
    height: 150px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #8c939d;

    .upload-icon {
      font-size: 28px;
      margin-bottom: 8px;
    }

    .upload-text {
      font-size: 14px;
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
