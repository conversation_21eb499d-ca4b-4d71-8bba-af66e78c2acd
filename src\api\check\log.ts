import request from "/@/utils/request"
import { logListParams } from './planInterface'

// 查询日程计划列表
export function queryLogPlanList(params: logListParams) {
  return request({
    url: '/check/patrolPlanInstance/planInstanceList',
    method: 'get',
    params
  })
}

// 日程计划统计
export function queryLogStatics(params: logListParams) {
    return request({
      url: '/check/patrolPlanInstance/planInstanceStatistics',
      method: 'get',
      params
    })
  }

  export function getPlanInstanceListByLogId(params: any) {
    return request({
      url: '/check/patrolPlanInstance/planInstanceListByLogId',
      method:'get',
      params
    })
  }
  export function queryPlanDetail(params: any) {
    return request({
      url: '/check/patrolPlan/planInfoById',
      method:'get',
      params
    })
  }

  
// 日志详情
export function getLogDetail(params: any) {
	return request({
		url: '/check/patrolPlanInstance/getById',
    method:'get',
    params
	})
}
  

// 查询点位的所有照片或者视频
export function queryPartolPic (params: any) {
  return request({
      url: '/check/patrolFile/listByPointIdAndType',
      method:'get',
      params,
  });
};

  

// 历史病害列表
export function getHistoryList (params: any) {
  return request({
      url: '/check/disease/queryHistoryDiseaseForMin',
      method:'get',
      params,
  });
};

    

// 新增病害列表
export function queryNewDiseaseList (params: any) {
  return request({
      url: '/check/disease/queryNewDiseaseForMin',
      method:'get',
      params,
  });
};


// 查询点位详情
export function getListByPoint(params: any) {
	return request({
		url: '/check/patrolFile/listByPointId',
    method:'get',
		params
	})
}
  

// 新增事件时候 查询病害类型
export function queryDiseaseType(params: any) {
	return request({
		url: '/check/disease/queryDiseaseType',
    method:'get',
		params
	})
}


// 新增事件时候 根据typeid 查询问题描述
export function queryDiseaseDescription(params: any) {
	return request({
		url: '/check/disease/queryDiseaseDescription',
    method:'get',
		params
	})
}


// 查询事件详情
export function queryEventDetail (params: any) {
  return request({
      // url: '/patrolEvent/queryByPointIdAndLogId',
      url: '/check/patrolEvent/queryEventDetailByPointIdAndLogIdForMin',
      method:'get',
      params
  });
};

// 病害对比
export function diseaseCompare (params: any) {
  return request({
      url: '/check/patrolFile/compare',
      method:'get',
      params,
  });
};

  

// 病害分析
export function diseaseAnalysis (params: any) {
  return request({
      url: '/check/diseaseInstance/lineByPointAndDisease',
      method:'get',
      params,
  });
};


// 查询病害历史详情
export function queryDiseaseHistoryDetail (params: any) {
  return request({
      url: '/check/diseaseInstance/queryHistoryDiseaseDetail',
      method:'get',
      params,
  });
};


// 查询新增病害详情
export function queryNewDiseaseDetailForMin (params: any) {
  return request({
      url: '/check/diseaseInstance/queryNewDiseaseDetailForMin',
      method:'get',
      params,
  });
};


// 新增病害 查询问题类型
export function queryQuestionType (params: any) {
  return request({
      url: '/check/disease/queryDiseaseTypeForApp',
      method:'get',
      params,
  });
};

// 删除巡查日志列表
export function delPatrolLog(params: any) {
  return request({
    url: '/check/patrolPlanInstance/delPlanInstance',
    method: 'get',
    params
  })
}
