<template>
	<div class="layout-padding">
		<FileDetail ref="fileDetailRef" v-if="isShowDetail" @close="isShowDetail = false" :type="2" />
		<div class="layout-padding-view res-file-recycle" style="padding: 15px 25px" v-show="!isShowDetail">
			<el-row>
				<el-col :span="6">
					<div>
						<el-button @click="onOperation('BATCH_REVERT')" :disabled="selectDatas.length === 0" type="primary">批量还原</el-button>
						<el-button @click="onOperation('BATCH_DELETE')" :disabled="selectDatas.length === 0" type="danger" plain>批量删除</el-button>
					</div>
				</el-col>
				<el-col :span="18" style="display: flex; justify-content: end">
					<el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList" class="flex" label-width="0">
						<el-form-item label="" prop="catalogId" style="width: 140px">
							<el-select v-model="state.queryForm.catalogId" placeholder="资源目录">
                                <el-option v-for="item in folderList" :key="item.id" :label="item.catalogName" :value="item.id"></el-option>
                            </el-select>
						</el-form-item>
						<el-form-item prop="resourceName" style="width: 220px">
							<el-input placeholder="请输入名称搜索" v-model="state.queryForm.resourceName" clearable>
                                <template #suffix>
									<el-icon @click="getDataList" class="el-input__icon cursor-pointer"><search /></el-icon>
								</template>
                            </el-input>
						</el-form-item>
                        <!-- <el-button icon="search" type="primary"> 查询 </el-button> -->
						<el-button icon="Refresh" @click="resetQuery"></el-button>
					</el-form>
				</el-col>
			</el-row>
			<div style="display: flex; flex-direction: column; height: calc(100% - 140px)">
				<el-table
					:data="state.dataList || []"
					v-loading="state.loading"
					show-overflow-tooltip
					border
					:cell-style="tableStyle.cellStyle"
					:header-cell-style="tableStyle.headerCellStyle"
					@selection-change="selectionChangHandle"
					class="custom-table-control"
				>
					<el-table-column type="selection" width="40" align="center" />
					<el-table-column
						v-for="(item, k) in tableColumn"
						align="center"
						:key="k"
						:prop="item.key"
						:label="item.title"
						:width="item.width || 'auto'"
					>
						<template #default="scope">
							<div style="height: 34px; line-height: 34px">
								<span v-if="item.key === 'expireDays'">
									<span>{{ scope.row.expireDays }}天</span>
								</span>
								<span v-else-if="item.key === 'folderPath'">
									<span>{{ scope.row.folderPath?.split(',').join('/') }}</span>
								</span>
								<span v-else>
									<span>{{ scope.row[item.key] || '--' }}</span>
								</span>
							</div>
						</template>
					</el-table-column>
					<el-table-column align="center" prop="operation" label="操作" :width="'auto'">
						<template #default="scope">
							<div class="operation-btns">
								<el-button
									@click="onOperation('DoView', scope.row)"
									link
									type="primary"
									:disabled="scope.row.level > 2"
									>查看</el-button
								>
								<el-button @click="onOperation('DoRevert', scope.row)" link type="primary">还原</el-button>
								<el-button
									@click="onOperation('DoDelete', scope.row)"
									link
									type="danger"
									:disabled="scope.row.children?.length > 0"
									>删除</el-button
								>
							</div>
						</template>
					</el-table-column>
				</el-table>
				<pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
			</div>
		</div>
	</div>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { fetchList, updateObj } from '/@/api/resource/recycle/bin';
import { getFolderList } from '/@/api/resource/resourceFile';
import FileDetail from '/@/views/resourceManagement/resourceFile/detail/FileDetail.vue';
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		type: 1,
		resourceName: '',
		catalogId: '',
	},
	createdIsNeed: false,
	pageList: fetchList,
	props: {
		item: 'records',
		totalCount: 'total',
	},
	isPage: true,
});
const { tableStyle, getDataList, currentChangeHandle, sizeChangeHandle } = useTable(state);

const isShowDetail = ref<boolean>(false);
const fileDetailRef = ref();
const queryRef = ref();
const folderList = ref<any[]>([]);
const selectDatas = ref([]);
const tableColumn = ref([
	{ title: '资源名称', key: 'resourceName' },
	{ title: '资源目录', key: 'folderPath' },
	{ title: '过期剩余日期', key: 'expireDays', width: 'auto' },
	{ title: '创建人', key: 'resourceCreateByName' },
	{ title: '创建时间', key: 'resourceCreateTime' },
]);

onMounted(() => {
    getDataList();
    getFolderOptions();
});

const getFolderOptions = () => {
    getFolderList({ catalogParentId: 0 }).then((res) => {
        folderList.value = res?.data?.folders || [];
    });
};
// 清空搜索条件
const resetQuery = () => {
	queryRef.value?.resetFields();
	getDataList(1);
};
// 多选事件
const selectionChangHandle = (objs: any) => {
	selectDatas.value = objs;
};

// 彻底删除
const onDelete = async (ids: any) => {
	try {
		await useMessageBox().confirm('确认彻底删除吗？');
	} catch {
		return;
	}
	try {
		await updateObj({ operateType: 1, ids });
		useMessage().success('删除成功');
		getDataList();
	} catch (err) {
		useMessage().error((err as any).msg);
	}
};

// 还原
const onRevert = async (ids: any) => {
	try {
		await useMessageBox().confirm('确认恢复资源吗？');
	} catch {
		return;
	}
	try {
		await updateObj({ operateType: 2, ids });
		useMessage().success('操作成功');
		getDataList();
	} catch (err) {
		useMessage().error((err as any).msg);
	}
};

const onOperation = (type: string, record?: any) => {
	switch (type) {
		case 'DoView':isShowDetail.value = true;
			nextTick(() => {
				fileDetailRef.value.initInfo(record);
			});
			break;
		case 'DoRevert':
			onRevert([record.id]);
			break;
		case 'BATCH_REVERT':
			onRevert(selectDatas.value.map((item: any) => item.id));
			break;
		case 'DoDelete':
			onDelete([record.id]);
			break;
		case 'BATCH_DELETE':
			onDelete(selectDatas.value.map((item: any) => item.id));
	}
};
</script>

<style scoped lang="scss">
.res-file-recycle {
    :deep(.el-input) {
        .el-input-group__append {
            background-color: transparent;
        }
    }
}
</style>
