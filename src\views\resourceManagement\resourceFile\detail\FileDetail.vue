<template>
	<div class="file-detail-box" v-loading="isLoading">
		<div class="file-detail-back" @click="emit('close')">
			<el-button icon="DArrowLeft" plain>返回</el-button>
		</div>
		<div class="file-detail-header">
			<el-tabs v-model="activeName" class="editor-header-title tabs">
				<el-tab-pane label="资源信息" name="1"></el-tab-pane>
				<el-tab-pane label="操作日志" name="2"></el-tab-pane>
			</el-tabs>
		</div>
		<div class="file-detail-content">
			<FileInfoTab v-if="activeName == '1'" :record="detailInfo" />
			<FileLogTab v-if="activeName == '2'" :record="detailInfo" />
		</div>
	</div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { getObj } from '/@/api/resource/filelist/file';
import { getRecycleObj } from '/@/api/resource/recycle/bin';
import FileLogTab from '/@/views/resourceManagement/resourceFile/detail/FileLogTab.vue';
import FileInfoTab from '/@/views/resourceManagement/resourceFile/detail/FileInfoTab.vue';

const emit = defineEmits(['close']);
const detailInfo = ref({});
const activeName = ref('1');
const isLoading = ref(false);
const props = defineProps({
	type: {
		type: Number,
		default: () => 1, // 1默认，2回收站
	},
});

onMounted(() => {});

// 初始化文件基本信息
const initInfo = (record: any) => {
	isLoading.value = true;
	let detailFunc = props.type === 1 ? getObj : getRecycleObj;
	detailFunc(props.type === 1 ? record.id : {id: record.resourceId}).then((res) => {
		detailInfo.value = res.data;
	}).finally(() => {
		isLoading.value = false;
	});
};
defineExpose({
	initInfo,
});
</script>

<style lang="scss" scoped>
.file-detail-box {
	position: relative;
	height: 100%;
	display: flex;
	flex-direction: column;
	.file-detail-back {
		position: absolute;
		right: 10px;
		top: 9px;
		cursor: pointer;
		z-index: 999;
		.el-icon {
			font-size: 18px;
		}
	}
	:deep(.file-detail-header) {
		height: 50px;
		background: #fff;
		border-radius: 5px;
		padding: 0 20px;
		.el-tabs__item {
			height: 50px;
			line-height: 50px;
		}
		.el-tabs__header {
			margin-bottom: 0;
		}
		.el-tabs__nav-wrap::after {
			height: 0 !important;
		}
	}
	.file-detail-content {
		flex: 1;
		height: calc(100% - 100px);
		margin-top: 20px;
	}
}
</style>
