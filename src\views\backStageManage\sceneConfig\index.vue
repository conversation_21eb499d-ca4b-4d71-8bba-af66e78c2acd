<template>
	<div class="scene-config">
		<!-- 页面头部 -->
		<div class="config-header">
			<div class="header-left">
				<h2 class="scene-title">当前场景：{{ sceneInfo.sceneName }}</h2>
			</div>
			<div class="header-right">
				<el-button icon="ArrowLeft" plain @click="goBack">返回</el-button>
				<el-button type="success">发布</el-button>
			</div>
		</div>

		<!-- 配置内容 -->
		<div class="config-content">
			<div class="config-layout">
				<!-- 左侧：章节树形结构 -->
				<div class="tree-panel">
					<div class="panel-header">
						<h3 class="panel-title">漫游节点</h3>
						<el-button link type="primary" icon="Plus" @click="addChapter">新增章节</el-button>
					</div>
					<div class="tree-content">
						<el-tree
							ref="treeRef"
							:data="treeData"
							:props="treeProps"
							node-key="id"
							:expand-on-click-node="false"
							:default-expand-all="true"
							class="scene-tree"
							:highlight-current="true"
							@node-click="handleNodeClick"
						>
							<template #default="{ node, data }">
								<div class="tree-node">
									<div class="node-content" @click="handleNodeClick(data)">
										<span class="node-label">{{ data.nodeName || data.name }}</span>
									</div>
									<div class="node-actions">
										<el-dropdown @command="handleCommand" trigger="click">
											<el-button type="text" size="small" @click.stop>
												<el-icon><MoreFilled /></el-icon>
											</el-button>
											<template #dropdown>
												<el-dropdown-menu>
													<el-dropdown-item v-if="data.type === 'chapter'" :command="{ action: 'addPalace', data }">添加宫观</el-dropdown-item>
													<el-dropdown-item v-if="data.type === 'palace'" :command="{ action: 'addNode', data }">添加节点</el-dropdown-item>
													<el-dropdown-item :command="{ action: 'edit', data }">编辑</el-dropdown-item>
													<el-dropdown-item :command="{ action: 'delete', data }" divided>删除</el-dropdown-item>
												</el-dropdown-menu>
											</template>
										</el-dropdown>
									</div>
								</div>
							</template>
						</el-tree>
					</div>
				</div>
				<!-- 中间：全景视频面板 -->
				<div class="video-panel">全景视频区域</div>
				<!-- 右侧：详细配置面板 -->
				<div class="config-panel">
					<div class="panel-header">
						<div class="edit-title" :class="{ active: activeEditType == '1' }" @click="changeEditForm('1')" v-if="selectedNode">
							<el-icon><Files /></el-icon>
							<span>{{ currentEditType === 'chapter' ? '章节' : currentEditType === 'palace' ? '宫观' : '节点' }}配置</span>
						</div>
						<div class="edit-title" :class="{ active: activeEditType == '2' }" @click="changeEditForm('2')">
							<el-icon><Setting /></el-icon>
							<span>全局配置</span>
						</div>
					</div>
					<div class="panel-content">
						<!-- 章节/宫观/节点配置表单 -->
						<div v-if="activeEditType === '1' && selectedNode">
							<!-- 章节表单 -->
							<AddChapterForm v-if="currentEditType === 'chapter'" ref="chapterFormRef" @data-change="handleFormDataChange" />
							<!-- 宫观表单 -->
							<AddPalaceForm v-if="currentEditType === 'palace'" ref="palaceFormRef" @data-change="handleFormDataChange" />
							<!-- 节点表单 -->
							<AddNodeForm v-if="currentEditType === 'node'" ref="nodeFormRef" @data-change="handleFormDataChange" />
						</div>

						<!-- 全局配置 -->
						<div v-if="activeEditType === '2'">
							<div class="global-config">
								<h4>全局配置</h4>
								<p>这里可以配置场景的全局设置</p>
							</div>
						</div>
					</div>
					<div class="panel-btn">
						<el-button @click="handleCancel" v-if="activeEditType == '1'">取消</el-button>
						<el-button @click="handleSave" type="primary">保存</el-button>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- 弹窗组件 -->
	<BatchAddPalaceDialog ref="batchPalaceDialogRef" @confirm="handleBatchPalaceConfirm" />
	<SelectNodeDialog ref="selectNodeDialogRef" @confirm="handleSelectNodeConfirm" />
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, defineAsyncComponent } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { Setting, Files, MoreFilled } from '@element-plus/icons-vue';
import {
	getSceneDetail,
	getSceneRoamTree,
	addRoamFirst,
	addBatchRoamSecond,
	addBatchRoamThird,
	updateRoamFirst,
	updateRoamSecond,
	updateRoamThird,
	deleteRoam,
	getSceneRoamDetail,
} from '/@/api/backStageManage/scene';

// 异步组件
const AddChapterForm = defineAsyncComponent(() => import('./components/AddChapterForm.vue'));
const AddPalaceForm = defineAsyncComponent(() => import('./components/AddPalaceForm.vue'));
const AddNodeForm = defineAsyncComponent(() => import('./components/AddNodeFrom.vue'));
const BatchAddPalaceDialog = defineAsyncComponent(() => import('./components/BatchAddPalaceDialog.vue'));
const SelectNodeDialog = defineAsyncComponent(() => import('./components/SelectNodeDialog.vue'));

const router = useRouter();
const route = useRoute();
const { success, error } = useMessage();
const { confirm } = useMessageBox();

// 响应式数据
const sceneId = ref(route.params.sceneId as string);
const sceneInfo = ref<any>({});
const treeRef = ref();
const selectedNode = ref<any>(null);
const treeData = ref<any[]>([]);
const currentChapter = ref<any>(null);
const currentPalace = ref<any>(null);
const activeEditType = ref<string>('2'); //1章节/宫观/节点，2场景

// 表单组件引用
const chapterFormRef = ref();
const palaceFormRef = ref();
const nodeFormRef = ref();
const batchPalaceDialogRef = ref();
const selectNodeDialogRef = ref();

// 表单数据
const currentFormData = ref<any>({});
const editMode = ref<'add' | 'edit'>('add');
const currentEditType = ref<'chapter' | 'palace' | 'node'>('chapter');

const treeProps = {
	children: 'children',
	label: 'nodeName',
};

onMounted(() => {
	nextTick(() => {
		getSceneConfig();
		getSceneDetailInfo();
	});
});

// 获取场景基本信息
const getSceneDetailInfo = async () => {
	const response = await getSceneDetail(sceneId.value);
	sceneInfo.value = response?.data || {};
};

// 获取树配置
const getSceneConfig = async () => {
	try {
		const response = await getSceneRoamTree({ sceneId: sceneId.value });
		const rawData = response?.data || [];
		treeData.value = processTreeData(rawData, 0);
	} catch (err) {
		error('获取场景配置失败');
	}
};
// 返回
const goBack = () => {
	router.push('/backStageManage/sceneManage');
};

const getNodeTypeText = (type: string) => {
	const typeMap: Record<string, string> = {
		chapter: '章节配置',
		palace: '宫观配置',
		node: '节点配置',
	};
	return typeMap[type] || '配置';
};

// 新增章节
const addChapter = () => {
	selectedNode.value = { type: 'chapter' };
	activeEditType.value = '1';
	currentEditType.value = 'chapter';
	editMode.value = 'add';
	currentChapter.value = null;
	// 重置表单
	nextTick(() => {
		if (chapterFormRef.value) {
			chapterFormRef.value.resetForm();
		}
	});
};

const addPalace = (chapterData: any) => {
	// 打开批量添加宫观弹窗
	if (batchPalaceDialogRef.value) {
		batchPalaceDialogRef.value.openDialog();
	}
	currentChapter.value = chapterData;
};

const addNode = (palaceData: any) => {
	// 打开选择节点弹窗
	if (selectNodeDialogRef.value && palaceData.sourceDataId) {
		selectNodeDialogRef.value.openDialog(palaceData.sourceDataId);
	}
	currentPalace.value = palaceData;
};

// 选择章节/宫观/节点
const handleNodeClick = async (data: any) => {
	selectedNode.value = data;
	activeEditType.value = '1';
	// let res = await getSceneRoamDetail(data.id);
	// 根据节点类型设置当前编辑类型
	if (data.type === 'chapter') {
		currentEditType.value = 'chapter';
		editMode.value = 'edit';
		// 加载章节数据到表单
		nextTick(() => {
			if (chapterFormRef.value) {
				chapterFormRef.value.setFormData({
					nodeName: data.name,
					description: data.description || '',
					serialNumber: data.weight || 1,
				});
			}
		});
	} else if (data.type === 'palace') {
		currentEditType.value = 'palace';
		editMode.value = 'edit';
		// 加载宫观数据到表单
		nextTick(() => {
			if (palaceFormRef.value) {
				palaceFormRef.value.setFormData({
					palaceName: data.name || data.nodeName,
					palaceOrder: data.order || 1,
					videoUrl: data.videoUrl || '',
					hasCommentary: data.hasCommentary !== false,
					avatarType: data.avatarType || '',
					commentaryContent: data.commentaryContent || '',
					audioFileId: data.audioFileId || '',
					audioUrl: data.audioUrl || '',
					allowComments: data.allowComments !== false,
					showComments: data.showComments !== false,
				});
			}
		});
	} else if (data.type === 'node') {
		currentEditType.value = 'node';
		editMode.value = 'edit';
		// 加载节点数据到表单
		nextTick(() => {
			if (nodeFormRef.value) {
				nodeFormRef.value.setFormData({
					nodeName: data.name || data.nodeName,
					nodeOrder: data.order || 1,
					nodeDescription: data.description || '',
					imageFileId: data.imageFileId || '',
					imageUrl: data.imageUrl || '',
				});
			}
		});
	}
};

// 编辑项目
const editItem = (data: any) => {
	handleNodeClick(data);
};

// 处理下拉菜单命令
const handleCommand = (command: any) => {
	const { action, data } = command;

	switch (action) {
		case 'addPalace':
			addPalace(data);
			break;
		case 'addNode':
			addNode(data);
			break;
		case 'edit':
			editItem(data);
			break;
		case 'delete':
			deleteItem(data);
			break;
	}
};

const deleteItem = async (data: any) => {
	try {
		await confirm(`确认删除${getNodeTypeText(data.type)}"${data.name || data.nodeName}"吗？`);

		// 调用删除API
		await deleteRoam(data.id);
		success('删除成功');
		getSceneConfig();

		// 如果删除的是当前选中的节点，清空选中状态
		if (selectedNode.value?.id === data.id) {
			selectedNode.value = null;
			activeEditType.value = '2';
		}
	} catch (err) {
		if (err !== 'cancel') {
			error('删除失败');
		}
	}
};

// 保存
const handleSave = async () => {
	if (activeEditType.value === '2') {
		// 全局配置保存
		success('全局配置保存成功');
		return;
	}

	const formRef = getCurrentFormRef();
	if (!formRef) {
		error('表单组件未找到');
		return;
	}

	// 验证表单
	const isValid = await formRef.validateForm();
	if (!isValid) {
		error('请检查表单数据');
		return;
	}

	// 获取表单数据
	const formData = formRef.getFormData();

	try {
		if (editMode.value === 'add') {
			// 新增逻辑
			switch (currentEditType.value) {
				case 'chapter':
					await addRoamFirst({
						sceneId: sceneId.value,
						nodeName: formData.nodeName,
						serialNumber: formData.serialNumber,
						description: formData.description,
					});
					success('章节添加成功');
					break;
			}
		} else {
			// 编辑逻辑
			switch (currentEditType.value) {
				case 'chapter':
					await updateRoamFirst({
						id: selectedNode.value.id,
						sceneId: sceneId.value,
						nodeName: formData.nodeName,
						displayTime: formData.displayTime,
						endTime: formData.endTime,
						description: formData.description,
						coverFileId: formData.coverFileId,
					});
					success('章节更新成功');
					break;
				case 'palace':
					await updateRoamSecond({
						id: selectedNode.value.id,
						palaceName: formData.palaceName,
						palaceOrder: formData.palaceOrder,
						videoUrl: formData.videoUrl,
						hasCommentary: formData.hasCommentary,
						avatarType: formData.avatarType,
						commentaryContent: formData.commentaryContent,
						audioFileId: formData.audioFileId,
						allowComments: formData.allowComments,
						showComments: formData.showComments,
					});
					success('宫观更新成功');
					break;
				case 'node':
					await updateRoamThird({
						id: selectedNode.value.id,
						nodeName: formData.nodeName,
						nodeOrder: formData.nodeOrder,
						description: formData.nodeDescription,
						imageFileId: formData.imageFileId,
					});
					success('节点更新成功');
					break;
			}
		}

		// 刷新树数据
		getSceneConfig();

		// 重置状态
		if (editMode.value === 'add') {
			handleCancel();
		}
	} catch (err) {
		error('保存失败');
	}
};

const handleCancel = () => {
	activeEditType.value = '2';
	selectedNode.value = null;
	treeRef.value.setCurrentKey(null);
};

const changeEditForm = (type: any) => {
	activeEditType.value = type;
};

// 表单数据变化处理
const handleFormDataChange = (data: any) => {
	currentFormData.value = data;
};

// 批量添加宫观确认
const handleBatchPalaceConfirm = async (palaces: any[]) => {
	try {
		const sourceDataIdList = palaces.map((palace) => palace.buildingId);
		await addBatchRoamSecond({
			sceneId: sceneId.value,
			parentId: currentChapter.value?.id,
			sourceDataIdList,
		});

		success(`成功添加${palaces.length}个宫观`);
		getSceneConfig(); // 刷新树数据
	} catch (err: any) {
		error(err?.msg || '批量添加宫观失败');
	}
};

// 选择节点确认
const handleSelectNodeConfirm = async (nodes: any[]) => {
	try {
		const nodeData = nodes.map((node) => ({
			resourceId: node.resourceId,
			nodeName: node.name,
			palaceId: currentPalace.value?.id,
		}));

		await addBatchRoamThird({
			sceneId: sceneId.value,
			palaceId: currentPalace.value?.id,
			nodes: nodeData,
		});

		success(`成功添加${nodes.length}个节点`);
		getSceneConfig(); // 刷新树数据
	} catch (err) {
		error('批量添加节点失败');
	}
};

// 获取当前表单组件引用
const getCurrentFormRef = () => {
	switch (currentEditType.value) {
		case 'chapter':
			return chapterFormRef.value;
		case 'palace':
			return palaceFormRef.value;
		case 'node':
			return nodeFormRef.value;
		default:
			return null;
	}
};

// 处理树数据，确保每个节点都有正确的类型标识
// 层级规则：章节-宫观-节点
const processTreeData = (data: any[], level: number = 0): any[] => {
	return data.map((item) => {
		let type = 'node'; // 默认为节点

		// 根据层级确定类型
		if (level === 0) {
			type = 'chapter'; // 第一层为章节
		} else if (level === 1) {
			type = 'palace'; // 第二层为宫观
		} else {
			type = 'node'; // 第三层及以下为节点
		}

		const processedItem = {
			...item,
			type: item.type || type,
			nodeName: item.nodeName || item.name,
		};

		if (item.children && item.children.length > 0) {
			processedItem.children = processTreeData(item.children, level + 1);
		}

		return processedItem;
	});
};
</script>

<style scoped lang="scss">
.scene-config {
	height: 100%;
	display: flex;
	flex-direction: column;
}

.config-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 24px;
	height: 54px;
	background: white;
	border-radius: 6px;
	margin-bottom: 14px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

	.header-left {
		.scene-title {
			font-size: 18px;
			font-weight: 600;
			color: rgba(0, 0, 0, 0.9);
		}
	}
}

.config-content {
	flex: 1;
	overflow: hidden;
}

.config-layout {
	display: flex;
	height: 100%;
	gap: 16px;
}

.tree-panel {
	width: 330px;
	background: white;
	border-radius: 6px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
	display: flex;
	flex-direction: column;

	.panel-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 16px 20px 0 20px;

		.panel-title {
			font-size: 16px;
			color: #1d2129;
		}
	}

	.tree-content {
		flex: 1;
		padding: 16px;
		overflow: auto;

		.scene-tree {
			:deep(.el-tree-node__content) {
				height: auto;
				padding: 8px 0;

				&:hover {
					background-color: #f5f7fa;
				}

				&.is-current {
					background-color: #e6f7ff;

					&:hover {
						background-color: #e6f7ff;
					}
				}
			}

			.tree-node {
				display: flex;
				justify-content: space-between;
				align-items: center;
				width: 100%;
				padding: 4px 8px;
				border-radius: 4px;

				.node-content {
					display: flex;
					align-items: center;
					flex: 1;
					cursor: pointer;
					padding: 4px 0;

					.node-label {
						font-size: 14px;
						color: #303133;
						font-weight: 400;
					}
				}

				.node-actions {
					display: flex;
					align-items: center;
					opacity: 0;
					transition: opacity 0.3s;

					.el-button {
						padding: 4px;
						font-size: 14px;
						color: #909399;

						&:hover {
							color: #409eff;
						}
					}
				}

				&:hover .node-actions {
					opacity: 1;
				}
			}
		}
	}
}

.video-panel {
	flex: 1;
	background: white;
	border-radius: 6px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
	display: flex;
	align-items: center;
	justify-content: center;
}

.config-panel {
	width: 420px;
	background: white;
	border-radius: 6px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
	display: flex;
	flex-direction: column;

	.panel-header {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 16px 110px 0 110px;
		margin-bottom: 16px;
		.edit-title {
			display: flex;
			align-items: center;
			justify-content: center;
			color: rgba(0, 0, 0, 0.6);
			padding: 5px 8px;
			cursor: pointer;
			.el-icon {
				margin-right: 8px;
			}
			&.active {
				border-radius: 3px;
				background: var(--el-color-primary);
				color: #fff;
			}
			& + .edit-title {
				margin-left: 10px;
			}
		}
	}

	.panel-content {
		flex: 1;
		overflow-y: auto;
		overflow-x: hidden;
	}

	.panel-btn {
		text-align: center;
		padding: 16px;
	}
}

.global-config {
	padding: 20px;
	text-align: center;

	h4 {
		margin-bottom: 16px;
		color: #1d2129;
	}

	p {
		color: #86909c;
	}
}
</style>
