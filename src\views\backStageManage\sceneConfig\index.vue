<template>
  <div class="scene-config">
    <!-- 页面头部 -->
    <div class="config-header">
      <div class="header-left">
        <el-button @click="goBack" class="back-btn">
          <el-icon>
            <ArrowLeft />
          </el-icon>
          返回场景管理
        </el-button>
        <div class="title-area">
          <h2 class="scene-title">{{ sceneInfo.name }} - 场景配置</h2>
          <span class="scene-desc">配置章节、宫观和信息节点的层级结构</span>
        </div>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showAddChapterDialog = true">
          <el-icon>
            <Plus />
          </el-icon>
          添加章节
        </el-button>
      </div>
    </div>

    <!-- 配置内容 -->
    <div class="config-content">
      <div class="config-layout">
        <!-- 左侧：章节树形结构 -->
        <div class="tree-panel">
          <div class="panel-header">
            <h3 class="panel-title">场景结构</h3>
            <el-button size="small" @click="expandAll">
              <el-icon>
                <Expand />
              </el-icon>
              展开全部
            </el-button>
          </div>
          <div class="tree-content">
            <el-tree ref="treeRef" :data="treeData" :props="treeProps" node-key="id" :expand-on-click-node="false"
              :default-expand-all="false" class="scene-tree" @node-click="handleNodeClick">
              <template #default="{ node, data }">
                <div class="tree-node">
                  <div class="node-content">
                    <el-icon class="node-icon">
                      <Collection v-if="data.type === 'chapter'" />
                      <OfficeBuilding v-else-if="data.type === 'palace'" />
                      <Location v-else />
                    </el-icon>
                    <span class="node-label">{{ data.name }}</span>
                    <el-tag v-if="data.type === 'palace' && data.videoUrl" size="small" type="success"
                      class="video-tag">
                      已配置视频
                    </el-tag>
                  </div>
                  <div class="node-actions">
                    <el-button v-if="data.type === 'chapter'" type="primary" link size="small"
                      @click.stop="addPalace(data)">
                      添加宫观
                    </el-button>
                    <el-button v-if="data.type === 'palace'" type="primary" link size="small"
                      @click.stop="addNode(data)">
                      添加节点
                    </el-button>
                    <el-button type="primary" link size="small" @click.stop="editItem(data)">
                      编辑
                    </el-button>
                    <el-button type="danger" link size="small" @click.stop="deleteItem(data)">
                      删除
                    </el-button>
                  </div>
                </div>
              </template>
            </el-tree>
          </div>
        </div>

        <!-- 右侧：详细配置面板 -->
        <div class="config-panel">
          <div class="panel-header">
            <h3 class="panel-title">
              {{ selectedNode ? `${getNodeTypeText(selectedNode.type)} - ${selectedNode.name}` : '请选择配置项' }}
            </h3>
          </div>
          <div class="panel-content">
            <!-- 章节配置 -->
            <ChapterConfig v-if="selectedNode && selectedNode.type === 'chapter'" :chapter-data="selectedNode"
              @update="handleNodeUpdate" />
            <!-- 宫观配置 -->
            <PalaceConfig v-else-if="selectedNode && selectedNode.type === 'palace'" :palace-data="selectedNode"
              @update="handleNodeUpdate" />
            <!-- 信息节点配置 -->
            <NodeConfig v-else-if="selectedNode && selectedNode.type === 'node'" :node-data="selectedNode"
              @update="handleNodeUpdate" />
            <!-- 空状态 -->
            <div v-else class="empty-config">
              <el-icon class="empty-icon">
                <Setting />
              </el-icon>
              <p class="empty-text">请在左侧选择要配置的项目</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加章节弹窗 -->
    <AddChapterDialog v-model:visible="showAddChapterDialog" :scene-id="sceneId" @confirm="handleChapterAdd" />

    <!-- 添加宫观弹窗 -->
    <AddPalaceDialog v-model:visible="showAddPalaceDialog" :chapter-data="currentChapter" @confirm="handlePalaceAdd" />

    <!-- 添加节点弹窗 -->
    <AddNodeDialog v-model:visible="showAddNodeDialog" :palace-data="currentPalace" @confirm="handleNodeAdd" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineAsyncComponent } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useMessage, useMessageBox } from '/@/hooks/message';
import {
  ArrowLeft,
  Plus,
  Expand,
  Collection,
  OfficeBuilding,
  Location,
  Setting,
} from '@element-plus/icons-vue';

// 异步组件
const ChapterConfig = defineAsyncComponent(() => import('./components/ChapterConfig.vue'));
const PalaceConfig = defineAsyncComponent(() => import('./components/PalaceConfig.vue'));
const NodeConfig = defineAsyncComponent(() => import('./components/NodeConfig.vue'));
const AddChapterDialog = defineAsyncComponent(() => import('./components/AddChapterDialog.vue'));
const AddPalaceDialog = defineAsyncComponent(() => import('./components/AddPalaceDialog.vue'));
const AddNodeDialog = defineAsyncComponent(() => import('./components/AddNodeDialog.vue'));

const router = useRouter();
const route = useRoute();
const { success, error } = useMessage();
const { confirm } = useMessageBox();

// 响应式数据
const sceneId = ref(route.params.sceneId as string);
const sceneInfo = ref<any>({});
const treeRef = ref();
const selectedNode = ref<any>(null);
const treeData = ref<any[]>([]);
const showAddChapterDialog = ref(false);
const showAddPalaceDialog = ref(false);
const showAddNodeDialog = ref(false);
const currentChapter = ref<any>(null);
const currentPalace = ref<any>(null);

const treeProps = {
  children: 'children',
  label: 'name',
};

// 方法
const goBack = () => {
  router.push('/backStageManage/sceneManage');
};

const expandAll = () => {
  treeRef.value?.expandAll();
};

const handleNodeClick = (data: any) => {
  selectedNode.value = data;
};

const getNodeTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    chapter: '章节配置',
    palace: '宫观配置',
    node: '节点配置',
  };
  return typeMap[type] || '配置';
};

const addPalace = (chapterData: any) => {
  currentChapter.value = chapterData;
  showAddPalaceDialog.value = true;
};

const addNode = (palaceData: any) => {
  currentPalace.value = palaceData;
  showAddNodeDialog.value = true;
};

const editItem = (data: any) => {
  selectedNode.value = data;
  // TODO: 根据类型打开对应的编辑弹窗
};

const deleteItem = async (data: any) => {
  try {
    await confirm(`确认删除${getNodeTypeText(data.type)}"${data.name}"吗？`);
    // TODO: 调用删除API
    success('删除成功');
    getSceneConfig();
  } catch {
    // 用户取消删除
  }
};

const handleNodeUpdate = (updatedData: any) => {
  // 更新树形数据
  updateTreeNode(treeData.value, updatedData);
  success('更新成功');
};

const updateTreeNode = (nodes: any[], updatedData: any): boolean => {
  for (const node of nodes) {
    if (node.id === updatedData.id) {
      Object.assign(node, updatedData);
      return true;
    }
    if (node.children && updateTreeNode(node.children, updatedData)) {
      return true;
    }
  }
  return false;
};

const handleChapterAdd = () => {
  showAddChapterDialog.value = false;
  getSceneConfig();
};

const handlePalaceAdd = () => {
  showAddPalaceDialog.value = false;
  currentChapter.value = null;
  getSceneConfig();
};

const handleNodeAdd = () => {
  showAddNodeDialog.value = false;
  currentPalace.value = null;
  getSceneConfig();
};

const getSceneConfig = async () => {
  try {
    // TODO: 调用API获取场景配置
    // 模拟数据
    sceneInfo.value = {
      id: sceneId.value,
      name: '武当山主峰天柱峰',
    };

    treeData.value = [
      {
        id: 'chapter_1',
        name: '第一章：武当山概览',
        type: 'chapter',
        sort: 1,
        children: [
          {
            id: 'palace_1_1',
            name: '金殿',
            type: 'palace',
            sort: 1,
            videoUrl: 'https://example.com/video1.mp4',
            children: [
              {
                id: 'node_1_1_1',
                name: '金殿历史介绍',
                type: 'node',
                sort: 1,
                timestamp: 30,
              },
              {
                id: 'node_1_1_2',
                name: '建筑特色说明',
                type: 'node',
                sort: 2,
                timestamp: 60,
              },
            ],
          },
          {
            id: 'palace_1_2',
            name: '紫霄宫',
            type: 'palace',
            sort: 2,
            videoUrl: '',
            children: [],
          },
        ],
      },
      {
        id: 'chapter_2',
        name: '第二章：道教文化',
        type: 'chapter',
        sort: 2,
        children: [],
      },
    ];
  } catch (err) {
    error('获取场景配置失败');
  }
};

onMounted(() => {
  getSceneConfig();
});
</script>

<style scoped lang="scss">
.scene-config {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: white;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .back-btn {
      .el-icon {
        margin-right: 4px;
      }
    }

    .title-area {
      .scene-title {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
        margin: 0 0 4px 0;
      }

      .scene-desc {
        font-size: 13px;
        color: #909399;
      }
    }
  }

  .header-right {
    .el-button {
      .el-icon {
        margin-right: 4px;
      }
    }
  }
}

.config-content {
  flex: 1;
  overflow: hidden;
}

.config-layout {
  display: flex;
  height: 100%;
  gap: 16px;
}

.tree-panel {
  width: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e4e7ed;

    .panel-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin: 0;
    }
  }

  .tree-content {
    flex: 1;
    padding: 16px;
    overflow: auto;

    .scene-tree {
      :deep(.el-tree-node__content) {
        height: auto;
        padding: 8px 0;

        &:hover {
          background-color: #f5f7fa;
        }
      }

      .tree-node {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        padding: 8px 12px;
        border-radius: 6px;

        .node-content {
          display: flex;
          align-items: center;
          gap: 8px;
          flex: 1;

          .node-icon {
            font-size: 16px;
            color: #409eff;
          }

          .node-label {
            font-size: 14px;
            color: #303133;
          }

          .video-tag {
            margin-left: 8px;
          }
        }

        .node-actions {
          display: flex;
          gap: 4px;
          opacity: 0;
          transition: opacity 0.3s;

          .el-button {
            padding: 4px 8px;
            font-size: 12px;
          }
        }

        &:hover .node-actions {
          opacity: 1;
        }
      }
    }
  }
}

.config-panel {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;

  .panel-header {
    padding: 16px 20px;
    border-bottom: 1px solid #e4e7ed;

    .panel-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin: 0;
    }
  }

  .panel-content {
    flex: 1;
    overflow: auto;

    .empty-config {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: #909399;

      .empty-icon {
        font-size: 64px;
        margin-bottom: 16px;
      }

      .empty-text {
        font-size: 16px;
        margin: 0;
      }
    }
  }
}
</style>
