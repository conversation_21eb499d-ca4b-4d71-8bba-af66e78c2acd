<template>
  <div class="resource-content-box" v-loading="loading">
    <!-- 顶部搜索栏 -->
    <div class="top-box">
      <div class="search-container">
        <div class="search-bar">
          <el-select v-model="firstCatalogNo" placeholder="">
            <el-option
              :label="item.itemName"
              :value="item.itemNo"
              v-for="item in topicList"
              :key="item.itemNo"
            />
          </el-select>
          <el-input
            v-model="searchText"
            placeholder="请输入关键词，点击搜索按钮进行检索"
            class="search-input"
          />
          <el-button type="primary" @click="handleSearch"></el-button>
        </div>
        <!-- 热门搜索 -->
        <div class="hot-search">
          <span>热门搜索:</span>
          <span
            class="hot-search-item"
            v-for="item in hotKeyWord"
            :key="item"
            @click="searchText = item"
            >{{ item }}</span
          >
        </div>
      </div>
    </div>

    <!-- 类别统计区 -->
    <div class="category-stats">
      <div
        :class="{ 'stat-item': true, active: item.itemNo == topicId }"
        :key="item.itemNo"
        v-for="item in categoryList"
        @click="categoryClick(item)"
      >
        <span class="label">{{ item.itemName || "-" }}</span>
        <span class="count">{{ item.itemCount }}</span>
      </div>
    </div>

    <div class="main-content-wrapper">
      <!-- 左侧边栏 -->
      <div class="sidebar">
        <div class="sidebar-section">
          <div class="title">来源单位</div>
          <div class="checkbox-group">
            <el-checkbox-group v-model="typeList" @change="queryKeywordSearch">
              <el-checkbox
                :label="item.itemNo"
                :key="item.itemNo"
                v-for="item in sourceDept"
              >
                <div :title="item.itemName">
                  {{ item.itemName }}
                </div>
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
        <div class="sidebar-section" v-if="false && groupField == 'third_catalog_no'">
          <div class="title">资源类型</div>
          <div class="checkbox-group">
            <el-checkbox
              :label="item.itemNo"
              :key="item.itemNo"
              v-for="item in catalogList"
            >
              <div :title="item.itemName">
                {{ item.itemName || "" }}
              </div>
            </el-checkbox>
          </div>
        </div>
      </div>

      <!-- 右侧内容列表区 -->
      <div class="content-list">
        <div class="sort-options">
          <span>排序:</span>
          <span
            v-for="item in sortList"
            :key="item.value"
            class="sort-item flex items-center"
            :class="{
              active: sortField === item.value,
              asc: sortField === item.value && sortType === 'asc',
            }"
            @click="handleSortChange(item.value)"
          >
            <span>{{ item.label }}</span>
            <span class="sort-arrow">{{
              sortField === item.value ? (sortType === "desc" ? "↓" : "↑") : "↓"
            }}</span>
          </span>
        </div>

        <!-- 列表项示例 -->
        <div class="list-box">
          <div
            :class="`list-item ${item.isLock ? 'lock' : ''}`"
            :key="item.business_id"
            v-for="item in list"
            @click="handleClick(item)"
          >
            <div :class="{ 'no-cover': !item?.assets_cover, 'img-box': true }">
              <img
                class="item-image"
                :src="item?.assets_cover ? item?.assets_cover : noCoverImg"
                alt=""
                @error="onImgError"
              />

              <div class="type-box" v-if="item.first_catalog_name">
                {{ item.first_catalog_name }}
              </div>
            </div>
            <div class="item-details">
              <div class="resource-title">{{ item.assets_name }}</div>
              <div class="resource-info">
                <div>
                  资源目录:
                  {{
                    item.assets_type == 1
                      ? item.third_catalog_name?.split(",").join(" > ")
                      : `${item.first_catalog_name || "-"}${
                          item.second_catalog_name ? " > " + item.second_catalog_name : ""
                        }${
                          item.third_catalog_name ? " > " + item.third_catalog_name : ""
                        }`
                  }}
                </div>
                <div>
                  关键词: <span class="keyword">{{ item.assets_tag }}</span>
                </div>
                <div class="description">
                  资源描述:
                  {{ item.asset_remark }}
                </div>
              </div>
            </div>
            <div class="view-count">
              <i />
              <span>{{ item.view_count || 0 }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <ApplyModal
      v-model:visible="applyModalVisible"
      :tabName="currentResource.table_name"
      :resourceId="currentResource.business_id"
      @close="handleApplyModalClose"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from "vue";
import { useRouter, useRoute } from "vue-router";
import {
  getKeywordSearch,
  getSearchCountByDept,
  getSearchCountByCatalog,
} from "/@/api/squareNew/index";
import noCoverImg from "/@/assets/img/squareNew/no_cover.png";
import { getCountByCatalog, getHotKeyWord } from "/@/api/squareNew/index";
import { ElMessage } from "element-plus";
import ApplyModal from "/@/views/squareNew/main/resource/components/applyModal.vue";

const router = useRouter();
const route = useRoute();
const searchText = ref<any>("");
const topicId = ref();
const firstCatalogNo = ref();
const groupField = ref("third_catalog_no");
const hotKeyWord = ref([]);
const categoryList = ref<any>([]);
const sourceDept = ref<any>([]);
const catalogList = ref<any>([]);
const topicList = ref<any>([]);
const list = ref<any>([]);
const sortField = ref("create_time"); // 排序字段 create_time,view_count,download_count
const sortType = ref("desc"); // 排序方式 asc,desc
const sortList = ref([
  { label: "按创建时间", value: "create_time" },
  { label: "按浏览量", value: "view_count" },
  { label: "按下载量", value: "download_count" },
]);
const typeList = ref([]);
const applyModalVisible = ref(false); // 权限申请
const currentResource = ref<any>({});
const loading = ref(false);

const handleClick = (record: any) => {
  if (!record.business_id || !record.assets_type || !record.table_name) {
    return ElMessage.warning("该资源暂时无法查看（缺少参数）");
  }
  // 申请权限
  if (record.isLock) {
    currentResource.value = record;
    applyModalVisible.value = true;
    return;
  }
  router.push(
    `/squareNew/resource/detail/${record.business_id}?type=${record.assets_type}&tabName=${record.table_name}`
  );
};

const categoryClick = (record: any) => {
  console.log("123123", record, "topicId.value", topicId.value);
  topicId.value = record.itemNo;
  queryKeywordSearch();
  // querySearchCountByCatalog();
};

const handleSearch = async () => {
  await querySearchCountByCatalog();
  queryKeywordSearch();
  // querySearchCountByDept();
  // querySearchCountByCatalog();
};

// 排序处理
const handleSortChange = (field: string) => {
  if (sortField.value === field) {
    // 如果点击的是当前排序字段，则切换排序方向
    sortType.value = sortType.value === "desc" ? "asc" : "desc";
  } else {
    // 如果点击的是其他排序字段，则切换到该字段，并设置为降序
    sortField.value = field;
    sortType.value = "desc";
  }

  // 重新获取数据
  queryKeywordSearch();
};

// 列表查询
const queryKeywordSearch = () => {
  loading.value = true;
  let params = {
    keyword: searchText.value,
    sortField: sortField.value,
    sortType: sortType.value,
    firstCatalogNo: firstCatalogNo.value,
    thirdCatalogNo: topicId.value,
    deptIds: typeList.value,
  };
  getKeywordSearch(params)
    .then((res) => {
      if (res.ok) {
        list.value = (res.data || []).map((item) => {
          if (!item.viewed || !item.downloaded) {
            item.isLock = true;
          }
          return item;
        });
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

// 来源单位
const querySearchCountByDept = () => {
  getSearchCountByDept({ keyword: searchText.value }).then((res) => {
    if (res.ok) {
      sourceDept.value = res.data || [];
    }
  });
};

// 查询资源类型列表
const querySearchCountByCatalog = async () => {
  const res = await getSearchCountByCatalog({
    keyword: searchText.value,
    groupField: groupField.value,
    firstCatalogNo: firstCatalogNo.value,
  });
  if (res.ok) {
    let total = res.data.reduce((acc: any, cur: any) => acc + cur.itemCount, 0);
    categoryList.value = [{ itemName: "全部", itemCount: total }].concat(res.data || []);
    topicId.value = undefined;
  }
};

// 查询专题库
const queryCountByCatalog = () => {
  getCountByCatalog().then((res) => {
    if (res.ok) {
      let total = res.data.reduce((acc: any, cur: any) => acc + cur.itemCount, 0);
      topicList.value = [{ itemName: "全部", itemCount: total }].concat(res.data || []);
    }
  });
};

const queryHotKeyWord = () => {
  getHotKeyWord({}).then((res) => {
    hotKeyWord.value = res?.data || [];
  });
};

onMounted(() => {
  if (route.query.keyword) {
    searchText.value = route.query.keyword;
  }
  if (route.query.topicId) {
    firstCatalogNo.value = route.query.topicId;
  }
  queryCountByCatalog(); // 查询所有专题库
  queryHotKeyWord(); // 热门关键词
  querySearchCountByCatalog();
  queryKeywordSearch(); // 查询列表
  querySearchCountByDept(); // 查询来源单位
});

const onImgError = (e: any) => {
  e.target.src = noCoverImg;
  e.target.parentElement.classList.add("no-cover");
};

const handleApplyModalClose = () => {
  currentResource.value = {};
  applyModalVisible.value = false;
};

onBeforeUnmount(() => {
  topicId.value = "";
});
</script>

<style lang="scss" scoped>
.resource-content-box {
  padding: 0 210px 26px;

  .top-box {
    background-color: #fff;
    padding: 60px 190px 25px;
    margin-bottom: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    .search-container {
      width: 627px;
      .search-bar {
        display: flex;
        margin-bottom: 12px;
        height: 40px;
        .el-select {
          width: 92px;
          height: 40px;
          margin-right: -3px;
          z-index: 99;

          :deep(.select-trigger) {
            height: 40px;

            .el-input {
              height: 40px;

              .el-input__wrapper {
                background-color: var(--el-color-primary);
                box-shadow: none;
                border-radius: 5px;
                .el-input__inner {
                  font-weight: 400;
                  font-size: 14px;
                  line-height: 20px;
                  letter-spacing: 0%;
                  text-align: center;
                  color: #ffffff;
                }
                .el-input__suffix {
                  .el-input__suffix-inner {
                    .el-icon {
                      color: #fff;
                    }
                  }
                }
              }
            }
            input::placeholder {
              color: #fff;
            }
          }
        }
        .search-input {
          width: calc(100% - 92px - 55px);
          :deep(.el-input__wrapper) {
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0%;
            text-align: center;
            color: #a1a1a1;
            border-radius: 0;
            box-shadow: none;
            border: 1px solid var(--el-color-primary);
            border-width: 1px 0;
          }
        }
        .el-button {
          width: 72px;
          height: 40px;
          border: none;
          border-radius: 5px;
          background-color: var(--el-color-primary);
          margin-left: -3px;
          z-index: 99;
          &::before {
            content: "";
            width: 24px;
            height: 24px;
            background-image: url("/@/assets/img/squareNew/searchIcon.png");
            background-size: 100% 100%;
          }
        }
      }

      .hot-search {
        margin-bottom: 20px;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        letter-spacing: 0%;
        color: #595959;

        span {
          margin-right: 10px;
        }

        .hot-search-item {
          color: #999;
          cursor: pointer;

          &:hover {
            color: #333;
          }
        }
      }
    }
  }

  .category-stats {
    margin-bottom: 15px;
    width: 100%;
    height: 66px;
    display: flex;
    justify-content: center;
    background-image: url("/@/assets/square/statsBg.png");
    background-size: 100% 100%;
    padding: 5px 0;

    .stat-item {
      text-align: center;
      font-size: 16px;
      width: 110px;
      height: 100%;
      margin-right: 50px;
      color: #fff;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      .label {
        font-weight: 400;
        font-size: 14px;
        line-height: 24.61px;
        letter-spacing: 0%;
        text-align: center;
        white-space: nowrap;
        height: 25px;
      }

      .count {
        font-weight: 700;
        font-size: 18px;
        line-height: 24.61px;
        letter-spacing: 0%;
        text-align: center;
      }

      &:last-child {
        margin-right: 0;
      }
    }

    .active {
      background-color: #ffffff;
      border-radius: 5px;
      color: #862626;
    }
  }
}

.breadcrumb-box {
  padding-top: 8px;
}

.main-content-wrapper {
  display: flex;
  gap: 20px;
  min-height: 400px;
}

.sidebar {
  width: 240px;
  background-color: #fff;
  flex-shrink: 0;

  .title {
    font-weight: 700;
    font-size: 20px;
    line-height: 26px;
    letter-spacing: 0%;
    color: #393939;
  }

  .sidebar-section {
    padding: 42px 24px 18px;
    border-bottom: 1px solid #e2e2e2;

    .checkbox-group {
      display: flex;
      flex-direction: column;
      margin-top: 12px;

      .el-checkbox-group {
        .el-checkbox {
          margin-bottom: 7px;
          height: 25px;
          margin-right: 0;
          width: 100%;
          :deep(.el-checkbox__input) {
            .el-checkbox__inner {
              background-color: transparent;
              &::after {
                border-color: var(--el-color-primary);
              }
            }
          }
          :deep(.el-checkbox__label) {
            font-family: Source Han Sans CN;
            font-weight: 400;
            font-size: 14px;
            line-height: 24.61px;
            color: rgba(57, 57, 57, 1);
            width: calc(100% - 14px);
            > div {
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }

  .sidebar-section:last-child {
    padding-top: 12px;
  }
}

.content-list {
  flex-grow: 1;

  .sort-options {
    display: flex;
    align-items: center;
    background-color: #fff;
    padding: 8px 0 0 28px;

    .sort-item {
      margin-left: 20px;
      color: #999;
      text-decoration: none;
      transition: all 0.3s;
      cursor: pointer;

      &:hover {
        color: var(--el-color-primary);
      }

      &.active {
        color: var(--el-color-primary);
        font-weight: 500;
      }

      .sort-arrow {
        margin-top: -3px;
        margin-left: 5px;
      }
    }
  }

  .list-box {
    .list-item {
      display: flex;
      gap: 30px;
      margin-bottom: 20px;
      padding: 20px 27px;
      border-bottom: 1px solid #eee;
      background-color: #fff;
      position: relative;
      cursor: pointer;

      &:last-child {
        border-bottom: none;
        margin-bottom: 0;
      }

      .img-box {
        position: relative;
        width: 148px;
        height: 187px;
        display: flex;
        align-items: center;

        .item-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          flex-shrink: 0;
        }
        .type-box {
          position: absolute;
          bottom: 0;
          text-align: center;
          width: 100%;
          background-color: rgba(0, 0, 0, 0.6);
          height: 34px;
          line-height: 34px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 16px;
          letter-spacing: 0%;
          text-align: center;
          vertical-align: middle;
          color: rgba(255, 255, 255, 1);
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          padding: 0 10px;
        }
      }

      .no-cover {
        .item-image {
          width: 70%;
          height: auto;
          margin: 0 auto;
        }
      }

      .item-details {
        flex-grow: 1;

        .resource-title {
          font-weight: 700;
          font-size: 24px;
          line-height: 24.61px;
          letter-spacing: 0%;
          color: #393939;
          margin-bottom: 30px;
        }

        .resource-info {
          font-weight: 400;
          font-size: 14px;
          line-height: 20px;
          letter-spacing: 0%;
          color: #a1a1a1;
          > div {
            margin-bottom: 9px;
          }
          .keyword {
            color: #c00;
            margin-right: 5px;
          }
        }
      }

      .view-count {
        display: flex;
        align-items: center;

        position: absolute;
        right: 20px;
        color: var(--el-color-primary);
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 14px;

        i {
          width: 30px;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
          &::before {
            content: "";
            display: inline-block;
            width: 22px;
            height: 20px;
            background: url("/@/assets/img/squareNew/eyePrimaryIcon.png") no-repeat center;
            background-size: contain;
          }
        }
      }
    }

    .lock {
      position: relative;

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(21, 21, 21, 0.6);
        z-index: 1;
      }
      &::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 126px;
        height: 107px;
        background: url("/@/assets/img/squareNew/lockIcon.png") no-repeat center;
        background-size: 100% 100%;
        z-index: 2;
        transform: translate(-50%, -50%);
      }
    }
  }
}
</style>
