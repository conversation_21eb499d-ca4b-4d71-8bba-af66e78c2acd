import request from '/@/utils/request'

export function fetchList(query?: Object) {
	return request({
		url: '/business/wb/auto/page',
		method: 'get',
		params: query,
	})
}

export function addObj(obj?: Object) {
	return request({
		url: '/business/wb/auto',
		method: 'post',
		data: obj,
	})
}

export function getObj(id?: string) {
	return request({
		url: '/business/wb/auto/' + id,
		method: 'get',
	})
}

export function delObjs(ids?: Object) {
	return request({
		url: '/business/wb/auto',
		method: 'delete',
		data: ids,
	})
}

export function putObj(obj?: Object) {
	return request({
		url: '/business/wb/auto',
		method: 'put',
		data: obj,
	})
}
