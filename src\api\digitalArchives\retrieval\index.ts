import request from '/@/utils/request';

/**
 * 检索首页按编目统计
 *
 */
export function countByCatalog(data: any) {
    return request({
        url: '/datacenter/archive/countByCatalog',
        method: 'post',
        data
    });
}


// 关键字检索
export function keywordSearch(data?: Object) {
    return request({
        url: "/datacenter/archive/keywordSearch",
        method: "post",
        data: data,
    });
}

// 高级检索
export function advancedSearch(data?: Object) {
    return request({
        url: "/datacenter/archive/advancedSearch",
        method: "post",
        data: data,
    });
}

// 周期分类统计
export function searchCountByCatalog(data?: Object) {
    return request({
        url: "/datacenter/archive/searchCountByCatalog",
        method: "post",
        data: data,
    });
}

// 周期分类统计
export function searchCountByCycle(data?: Object) {
    return request({
        url: "/datacenter/data/resource/searchCountByCycle",
        method: "post",
        data: data,
    });
}

// 申请借阅
export function applyBorrow(data?: Object) {
    return request({
        url: "/datacenter/archive/borrow/add",
        method: "post",
        data: data,
    });
}

// 获取资源申请界面信息
export function getReourceFlowId(params?: Object) {
    return request({
        url: "/task/combination/group/listCurrentUserStartGroup?hidden=false&processName=借阅申请",
        method: "get",
        params: params,
    });
}

// 获取资源申请界面信息
export function getReourceFlowDetail(params?: Object) {
    return request({
        url: "/task/process/getDetail",
        method: "get",
        params: params,
    });
}
// 发起申请
export function createReourceFlow(data?: Object) {
    return request({
        url: "/datacenter/resource/apply/create",
        method: "post",
        data: data,
    });
}

// 收藏
export function handleCollect(data?: Object) {
    return request({
        url: "/datacenter/archive/collect",
        method: "post",
        data: data,
    });
}


// 档案搜索结果导出
export function searchExport(data?: Object) {
    return request({
        url: "/datacenter/archive/searchExport",
        method: "post",
		responseType: 'blob',
        data: data,
    });
}
