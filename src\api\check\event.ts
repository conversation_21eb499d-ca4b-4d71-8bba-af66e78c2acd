import request from '/@/utils/request'

// 事件统计
export function eventStatistics(query?: any) {
	return request({
		url: '/check/patrolEvent/eventStatistics',
		method: 'get',
		params: query,
	})
}

// 事件类型统计
export function eventStatisticsByType(query?: any) {
	return request({
		url: '/check/patrolEvent/eventStatisticsByType',
		method: 'get',
		params: query,
	})
}

// 事件等级统计
export function eventStatisticsByLevel(query?: any) {
	return request({
		url: '/check/patrolEvent/eventStatisticsByLevel',
		method: 'get',
		params: query,
	})
}

// 事件趋势统计
export function eventStatisticsByTypeForPC(query?: any) {
	return request({
		url: '/check/patrolEvent/eventStatisticsByTypeForPC',
		method: 'get',
		params: query,
	})
}

// 事件高发点位排行
export function eventStatisticsByPoint(query?: any) {
	return request({
		url: '/check/patrolEvent/eventStatisticsByPoint',
		method: 'get',
		params: query,
	})
}

// 业务处理 事件列表
export function getPatrolEventListForPC(query?: any) {
	return request({
		url: '/check/patrolEvent/getPatrolEventListForPC',
		method: 'get',
		params: query,
	})
}

// 根据eventId查询工作流
export function queryWorkFlowEventId(query?: any) {
	return request({
		url: '/check/patrolWorkFlow/queryWorkFlowEventId',
		method: 'get',
		params: query,
	})
}


// 查日常巡查详情
export function queryEventDetailByPointIdAndLogId(query?: any) {
	return request({
		url: '/check/patrolEvent/queryEventDetailByPointIdAndLogIdForMin',
		method: 'get',
		params: query,
	})
}

// 根据eventId 查询 病害列表
export function queryDiseaseListById(query?: any) {
	return request({
		url: '/check/disease/queryDiseaseListById',
		method: 'get',
		params: query,
	})
}

// 通过点位id查询查询新增的病害详情
export function queryNewDiseaseDetailForMin(query?: any) {
	return request({
		url: '/check/diseaseInstance/queryNewDiseaseDetailForMin',
		method: 'get',
		params: query,
	})
}

// 查询病害历史详情
export function queryHistoryDisease(query?: any) {
	return request({
		url: '/check/diseaseInstance/queryHistoryDiseaseDetail',
		method: 'get',
		params: query,
	})
}

// 问题类型 - 查询事件类型字典项
export function getEventDict() {
	return request({
		url: '/check/disease/queryDiseaseType',
		method: 'get',
	})
}

// 新增病害 - 查询病害类型字典项
export function queryDiseaseTypeDict() {
	return request({
		url: '/check/disease/queryDiseaseTypeForApp',
		method: 'get',
	})
}

// 查询问题描述字典项
export function queryDiseaseDescription(query?: any) {
	return request({
		url: '/check/disease/queryDiseaseDescription',
		method: 'get',
		params: query,
	})
}

// 安保部领导处理事件
export function handleEventForXCLeader(data?: any) {
	return request({
		url: '/check/patrolEvent/handleEventForXCLeader',
		method: 'post',
		data,
	})
}

// 文旅部处理事件
export function handleEventForWB(data?: any) {
	return request({
		url: '/check/patrolEvent/handleEventForWB',
		method: 'post',
		data,
	})
}

// 获取当前角色岗位
export function userInfo(query?: any) {
	return request({
		url: '/check/user/userInfo',
		method: 'get',
		params: query,
	})
}

// 获取工程处置人员
export function getGCHandleUser(query?: any) {
	return request({
		url: '/check/user/getGCHandleUser',
		method: 'get',
		params: query,
	})
}

// 获取文旅部门
export function getWLDept(query?: any) {
	return request({
		url: '/check/user/getWLDept',
		method: 'get',
		params: query,
	})
}