<template>
  <el-dialog
    v-model="dialogVisible"
    title="添加宫观"
    width="700px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      class="palace-form"
    >
      <el-form-item label="宫观名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入宫观名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="宫观描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入宫观描述"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="排序" prop="sort">
        <el-input-number
          v-model="formData.sort"
          :min="1"
          :max="999"
          placeholder="请输入排序值"
          style="width: 200px"
        />
        <span class="form-tip">数值越小排序越靠前</span>
      </el-form-item>

      <el-form-item label="全景视频URL" prop="videoUrl">
        <el-input
          v-model="formData.videoUrl"
          placeholder="请输入全景视频URL"
          @blur="handleVideoUrlChange"
        />
        <div class="form-tip">支持 MP4、WebM 等格式的全景视频</div>
      </el-form-item>

      <el-form-item label="视频时长" prop="duration" v-if="formData.videoUrl">
        <el-input-number
          v-model="formData.duration"
          :min="1"
          :max="3600"
          placeholder="秒"
          style="width: 200px"
        />
        <span class="form-tip">单位：秒</span>
      </el-form-item>

      <el-form-item label="封面图片" prop="coverImage">
        <div class="cover-upload">
          <el-upload
            class="cover-uploader"
            :show-file-list="false"
            :on-success="handleCoverSuccess"
            :before-upload="beforeCoverUpload"
            action="/api/upload"
          >
            <img v-if="formData.coverImage" :src="formData.coverImage" class="cover-image" />
            <div v-else class="cover-placeholder">
              <el-icon class="cover-icon"><Plus /></el-icon>
              <div class="cover-text">上传封面</div>
            </div>
          </el-upload>
          <div class="upload-tips">
            <p>建议尺寸：16:9，支持 JPG、PNG 格式，大小不超过 2MB</p>
          </div>
        </div>
      </el-form-item>

      <!-- 视频预览 -->
      <el-form-item v-if="formData.videoUrl" label="视频预览">
        <div class="video-preview">
          <video
            ref="videoRef"
            :src="formData.videoUrl"
            controls
            preload="metadata"
            class="preview-video"
            @loadedmetadata="handleVideoLoaded"
          >
            您的浏览器不支持视频播放
          </video>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { useMessage } from '/@/hooks/message';
import { Plus } from '@element-plus/icons-vue';

interface Props {
  visible: boolean;
  chapterData?: any;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm'): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  chapterData: null,
});

const emit = defineEmits<Emits>();

const { success, error } = useMessage();

// 响应式数据
const formRef = ref();
const videoRef = ref();
const loading = ref(false);

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

const formData = reactive({
  name: '',
  description: '',
  sort: 1,
  videoUrl: '',
  duration: 0,
  coverImage: '',
});

const formRules = {
  name: [
    { required: true, message: '请输入宫观名称', trigger: 'blur' },
    { min: 2, max: 50, message: '宫观名称长度在 2 到 50 个字符', trigger: 'blur' },
  ],
  description: [
    { max: 200, message: '描述长度不能超过 200 个字符', trigger: 'blur' },
  ],
  sort: [
    { required: true, message: '请输入排序值', trigger: 'blur' },
    { type: 'number', min: 1, max: 999, message: '排序值范围为 1-999', trigger: 'blur' },
  ],
  videoUrl: [
    { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' },
  ],
  duration: [
    { type: 'number', min: 1, max: 3600, message: '视频时长范围为 1-3600 秒', trigger: 'blur' },
  ],
};

// 方法
const handleClose = () => {
  dialogVisible.value = false;
  resetForm();
};

const resetForm = () => {
  formRef.value?.resetFields();
  Object.assign(formData, {
    name: '',
    description: '',
    sort: 1,
    videoUrl: '',
    duration: 0,
    coverImage: '',
  });
};

const handleConfirm = async () => {
  try {
    await formRef.value?.validate();
    loading.value = true;

    const palaceData = {
      ...formData,
      chapterId: props.chapterData?.id,
      type: 'palace',
      children: [],
    };

    // TODO: 调用API保存宫观
    await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟API调用

    success('宫观添加成功');
    emit('confirm');
    handleClose();
  } catch (err) {
    if (err !== false) { // 不是表单验证错误
      error('宫观添加失败');
    }
  } finally {
    loading.value = false;
  }
};

const handleVideoUrlChange = () => {
  if (videoRef.value) {
    videoRef.value.load();
  }
};

const handleVideoLoaded = () => {
  if (videoRef.value) {
    formData.duration = Math.floor(videoRef.value.duration);
  }
};

const handleCoverSuccess = (response: any) => {
  if (response.code === 200) {
    formData.coverImage = response.data.url;
    success('封面上传成功');
  } else {
    error('封面上传失败');
  }
};

const beforeCoverUpload = (file: File) => {
  const isJPGOrPNG = file.type === 'image/jpeg' || file.type === 'image/png';
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isJPGOrPNG) {
    error('封面图片只能是 JPG 或 PNG 格式!');
    return false;
  }
  if (!isLt2M) {
    error('封面图片大小不能超过 2MB!');
    return false;
  }
  return true;
};
</script>

<style scoped lang="scss">
.palace-form {
  .form-tip {
    margin-left: 12px;
    color: #909399;
    font-size: 12px;
  }
}

.cover-upload {
  .cover-uploader {
    :deep(.el-upload) {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: all 0.3s;
      width: 200px;
      height: 112px;

      &:hover {
        border-color: #409eff;
      }
    }
  }

  .cover-image {
    width: 200px;
    height: 112px;
    object-fit: cover;
    display: block;
  }

  .cover-placeholder {
    width: 200px;
    height: 112px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #8c939d;

    .cover-icon {
      font-size: 28px;
      margin-bottom: 8px;
    }

    .cover-text {
      font-size: 14px;
    }
  }

  .upload-tips {
    margin-top: 8px;

    p {
      margin: 0;
      color: #909399;
      font-size: 12px;
      line-height: 1.4;
    }
  }
}

.video-preview {
  .preview-video {
    width: 100%;
    max-width: 400px;
    height: auto;
    border-radius: 8px;
    border: 1px solid #e4e7ed;
  }
}

.dialog-footer {
  text-align: right;
}
</style>
