<template>
  <div class="mt-content">
    <div ref="mtChart" class="chart-box"></div>
  </div>
</template>

<script lang="ts" name="CP0003" setup>
import * as echarts from "echarts";
let myChart = null;
const props = defineProps({
  data: {
    type: Object,
    default: {
      xAxis: [1, 2, 3, 5, 5, 9],
      datas: [
        {
          lineColor: "#FF8B9A",
          areaColor: "#FFD7DC",
          name: "使用率",
          smooth: false,
          data: [0, 10, 20, 30, 50, 60],
        },
      ],
      yAxis: {},
    },
  },
});
const mtChart: any = ref(null);

const resizeFn = () => {
  myChart?.resize();
};

onUnmounted(() => {
  window.removeEventListener("resize", resizeFn);
});

onMounted(() => {
  initChart();
  window.addEventListener("resize", resizeFn);
});

const initChart = () => {
  myChart = echarts.init(mtChart.value);
  mtChart.value.removeAttribute("_echarts_instance_");
  let series: any = [];
  props.data.datas?.forEach((obj: any) => {
    series.push({
      name: obj.name,
      type: "line",
      smooth: obj.smooth, //是否平滑曲线显示
      showAllSymbol: true,
      symbol: obj.symbol || "circle",
      symbolSize: obj.symbolSize > -1 ? obj.symbolSize : 1,
      lineStyle: {
        color: obj.lineColor,
      },
      areaStyle: obj.areaStyle || {
        color: obj.areaColor,
      },
      itemStyle: {
        color: obj.lineColor,
        borderWidth: 3,
        borderColor: obj.lineColor,
      },
      data: obj.data,
    });
  });

  let option: any = {
    backgroundColor: "transparent",
    legend: {
      show: true,
      right: "center",
      top: 0,
      borderRadius: 4,
      itemWidth: 20,
      itemHeight: 6,
      icon: "roundRect",
      itemGap: 24,
      textStyle: {
        fontSize: 12,
        color: "#3B0000",
      },
    },
    tooltip: {
      trigger: "axis",
    },
    grid: {
      top: "45px",
      left: "0px",
      right: "30px",
      bottom: "30px",
      containLabel: true,
      ...(props.data?.grid || {}),
    },
    xAxis: [
      {
        type: "category",
        boundaryGap: false,
        axisLine: {
          //坐标轴轴线相关设置。数学上的x轴
          show: true,
          lineStyle: {
            color: "rgba(59, 0, 0, 0.2)",
          },
        },
        axisLabel: {
          //坐标轴刻度标签的相关设置
          fontSize: 12,
          color: "#3B0000",
          ...(props.data?.xAxisStyle?.axisLabel || {}),
        },
        axisTick: { show: false },
        data: props.data.xAxis,
        offset: 10,
      },
    ],
    yAxis: {
      name: "单位",
      type: "value",
      splitNumber: 7,
      splitLine: {
        //坐标轴轴线相关设置。数学上的x轴
        show: true,
        lineStyle: {
          color: "rgba(59, 0, 0, 0.2)",
        },
      },
      axisLine: { show: false },
      axisLabel: {
        fontSize: 12,
        color: "#3B0000",
      },
      axisTick: { show: false },
      ...(props.data?.yAxis || {}),
      // offset: 23,
    },
    series: series,
  };
  myChart.setOption(option);
};
</script>
<style lang="scss" scoped>
.mt-content {
  height: 100%;
  padding: 10px;
  .chart-box {
    width: 100%;
    height: calc(100% - 0px);
  }
}
</style>
