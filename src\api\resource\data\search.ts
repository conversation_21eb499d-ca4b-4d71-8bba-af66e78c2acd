import request from "/@/utils/request";

// 检索分类统计
export function countByCatalog(data?: Object) {
  return request({
    url: "/datacenter/data/resource/countByCatalog",
    method: "post",
    data: data,
  });
}
// 周期分类统计
export function searchCountByCycle(data?: Object) {
  return request({
    url: "/datacenter/data/resource/searchCountByCycle",
    method: "post",
    data: data,
  });
}
// 周期分类统计
export function searchCountByCatalog(data?: Object) {
  return request({
    url: "/datacenter/data/resource/searchCountByCatalog",
    method: "post",
    data: data,
  });
}

// 关键字检索
export function keywordSearch(data?: Object) {
  return request({
    url: "/datacenter/data/resource/keywordSearch",
    method: "post",
    data: data,
  });
}

// 高级检索
export function advancedSearch(data?: Object) {
  return request({
    url: "/datacenter/data/resource/advancedSearch",
    method: "post",
    data: data,
  });
}
