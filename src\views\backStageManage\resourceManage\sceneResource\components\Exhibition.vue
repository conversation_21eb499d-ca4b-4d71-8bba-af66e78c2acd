<template>
    <div class="exhibition-container">
        <div class="exhibition-layout">
            <!-- 左侧子Tab导航 -->
            <div class="sub-tabs">
                <div v-for="tab in subTabs" :key="tab.key"
                    :class="['sub-tab-item', { active: activeSubTab === tab.key }]" @click="activeSubTab = tab.key">
                    {{ tab.label }}
                </div>
            </div>

            <!-- 右侧内容区域 -->
            <div class="content-area">
                <!-- 图片+文字 -->
                <div v-if="activeSubTab === 'imageText'" class="tab-content">
                    <div class="form-content">
                        <h3>图片</h3>
                        <el-form label-width="120px">
                            <el-form-item label="图片">
                                <el-upload class="image-uploader" action="#" :show-file-list="false" accept="image/*">
                                    <img v-if="formData.imageText.imageUrl" :src="formData.imageText.imageUrl"
                                        class="uploaded-image" />
                                    <el-icon v-else class="uploader-icon">
                                        <Plus />
                                    </el-icon>
                                </el-upload>
                            </el-form-item>
                            <el-form-item label="文字内容">
                                <el-input v-model="formData.imageText.content" type="textarea" :rows="6"
                                    placeholder="请输入文字内容" />
                            </el-form-item>
                        </el-form>
                    </div>
                </div>

                <!-- 音频+文字 -->
                <div v-if="activeSubTab === 'audioText'" class="tab-content">
                    <div class="form-content">
                        <h3>音频</h3>
                        <el-form label-width="120px">
                            <el-form-item label="音频文件">
                                <UploadItem />
                            </el-form-item>
                            <el-form-item label="文字内容">
                                <el-input v-model="formData.audioText.content" type="textarea" :rows="6"
                                    placeholder="请输入文字内容" />
                            </el-form-item>
                        </el-form>
                    </div>
                </div>

                <!-- 视频+文字 -->
                <div v-if="activeSubTab === 'videoText'" class="tab-content">
                    <div class="form-content">
                        <h3>视频</h3>
                        <el-form label-width="120px">
                            <el-form-item label="视频文件">
                                <el-upload class="video-uploader" action="#" accept="video/*">
                                    <el-button type="primary">上传视频</el-button>
                                </el-upload>
                            </el-form-item>
                            <el-form-item label="文字内容">
                                <el-input v-model="formData.videoText.content" type="textarea" :rows="6"
                                    placeholder="请输入文字内容" />
                            </el-form-item>
                        </el-form>
                    </div>
                </div>

                <!-- 模型+文字 -->
                <div v-if="activeSubTab === 'modelText'" class="tab-content">
                    <div class="form-content">
                        <h3>模型</h3>
                        <el-form label-width="120px">
                            <el-form-item label="3D模型文件">
                                <el-upload class="model-uploader" action="#" accept=".obj,.fbx,.gltf,.glb">
                                    <el-button type="primary">上传模型</el-button>
                                </el-upload>
                            </el-form-item>
                            <el-form-item label="文字内容">
                                <el-input v-model="formData.modelText.content" type="textarea" :rows="6"
                                    placeholder="请输入文字内容" />
                            </el-form-item>
                        </el-form>
                    </div>
                </div>

                <!-- 时间轴 -->
                <div v-if="activeSubTab === 'timeline'" class="tab-content">
                    <div class="form-content">
                        <h3>时间轴</h3>
                        <el-form label-width="120px">
                            <el-form-item label="时间轴标题">
                                <el-input v-model="formData.timeline.title" placeholder="请输入时间轴标题" />
                            </el-form-item>

                            <el-form-item label="时间点个数">
                                <el-input-number v-model="formData.timeline.pointCount" :min="1" :max="8"
                                    @change="handlePointCountChange" style="width: 200px;" />
                                <span style="margin-left: 8px; color: #909399;">最多8个时间点</span>
                            </el-form-item>

                            <el-form-item label="时间点配置" v-if="formData.timeline.pointCount > 0">
                                <div class="timeline-points">
                                    <!-- 时间点选项卡 -->
                                    <div class="point-tabs">
                                        <div v-for="(point, index) in formData.timeline.points" :key="index"
                                            :class="['point-tab', { active: activeTimelinePoint === index }]"
                                            @click="activeTimelinePoint = index">
                                            时间点{{ index + 1 }}
                                        </div>
                                    </div>

                                    <!-- 当前时间点配置 -->
                                    <div class="point-config" v-if="formData.timeline.points[activeTimelinePoint]">
                                        <el-form label-width="80px">
                                            <el-form-item label="时相">
                                                <el-input v-model="formData.timeline.points[activeTimelinePoint].year"
                                                    placeholder="请输入年份，如：1985" style="width: 200px;" />
                                            </el-form-item>

                                            <el-form-item label="标题">
                                                <el-input v-model="formData.timeline.points[activeTimelinePoint].title"
                                                    placeholder="请输入标题" />
                                            </el-form-item>

                                            <el-form-item label="描述">
                                                <el-input
                                                    v-model="formData.timeline.points[activeTimelinePoint].description"
                                                    type="textarea" :rows="4" placeholder="请输入描述" />
                                            </el-form-item>

                                            <el-form-item label="插图">
                                                <el-upload class="timeline-image-uploader" action="#"
                                                    :show-file-list="false" accept="image/*">
                                                    <img v-if="formData.timeline.points[activeTimelinePoint].imageUrl"
                                                        :src="formData.timeline.points[activeTimelinePoint].imageUrl"
                                                        class="timeline-image" />
                                                    <el-icon v-else class="timeline-image-uploader-icon">
                                                        <Plus />
                                                    </el-icon>
                                                </el-upload>
                                            </el-form-item>
                                        </el-form>
                                    </div>
                                </div>
                            </el-form-item>
                        </el-form>
                    </div>
                </div>

                <!-- 长卷形式 -->
                <div v-if="activeSubTab === 'scroll'" class="tab-content">
                    <div class="form-content">
                        <h3>长卷</h3>
                        <el-form label-width="120px">
                            <el-form-item label="长卷标题">
                                <el-input v-model="formData.scroll.title" placeholder="请输入长卷标题" />
                            </el-form-item>

                            <el-form-item label="播放时间段">
                                <el-row :gutter="16">
                                    <el-col :span="12">
                                        <el-time-picker v-model="formData.scroll.startTime" placeholder="开始时间"
                                            format="HH:mm:ss" value-format="HH:mm:ss" style="width: 100%" />
                                    </el-col>
                                    <el-col :span="12">
                                        <el-time-picker v-model="formData.scroll.endTime" placeholder="结束时间"
                                            format="HH:mm:ss" value-format="HH:mm:ss" style="width: 100%" />
                                    </el-col>
                                </el-row>
                            </el-form-item>

                            <el-form-item label="长卷图片">
                                <el-upload class="scroll-uploader" action="#" accept="image/*">
                                    <el-button type="primary">上传长卷图片</el-button>
                                </el-upload>
                            </el-form-item>

                            <el-form-item label="滚动速度">
                                <el-slider v-model="formData.scroll.scrollSpeed" :min="1" :max="10" :step="0.1"
                                    show-input :show-input-controls="false" style="width: 300px;" />
                                <span style="margin-left: 16px; color: #909399;">像素/秒</span>
                            </el-form-item>

                            <el-form-item label="长卷描述">
                                <el-input v-model="formData.scroll.content" type="textarea" :rows="6"
                                    placeholder="请输入长卷描述" />
                            </el-form-item>
                        </el-form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import UploadItem from './UploadItem.vue'
interface Props {
    data: any
}

interface Emits {
    (e: 'update', field: string, value: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 子Tab配置
const subTabs = [
    { key: 'imageText', label: '图片+文字' },
    { key: 'audioText', label: '音频+文字' },
    { key: 'videoText', label: '视频+文字' },
    { key: 'modelText', label: '模型+文字' },
    { key: 'timeline', label: '时间轴' },
    { key: 'scroll', label: '长卷形式' }
]

// 当前激活的子Tab
const activeSubTab = ref('imageText')

// 当前激活的时间点
const activeTimelinePoint = ref(0)

// 表单数据
const formData = reactive({
    imageText: {
        imageUrl: '',
        content: ''
    },
    audioText: {
        audioUrl: '',
        content: ''
    },
    videoText: {
        videoUrl: '',
        content: ''
    },
    modelText: {
        modelUrl: '',
        content: ''
    },
    timeline: {
        title: '',
        content: '',
        pointCount: 1,
        points: [{
            year: '',
            title: '',
            description: '',
            imageUrl: ''
        }] as Array<{ year: string, title: string, description: string, imageUrl: string }>
    },
    scroll: {
        title: '',
        imageUrl: '',
        content: '',
        startTime: '',
        endTime: '',
        scrollSpeed: 5
    }
})

// 监听props数据变化
watch(() => props.data, (newData) => {
    if (newData) {
        // 处理时间轴数据
        if (newData.timeAxisList && newData.timeAxisList.length > 0) {
            formData.timeline.pointCount = newData.timeAxisQuantity || newData.timeAxisList.length
            formData.timeline.points = newData.timeAxisList.map((item: any) => ({
                year: item.timePhase?.toString() || '',
                title: item.title || '',
                description: item.description || '',
                imageUrl: item.cover || ''
            }))
        }

        // 处理长卷数据
        if (newData.mainScroll) {
            Object.assign(formData.scroll, {
                title: newData.mainScroll.title || '',
                imageUrl: newData.mainScroll.cover || '',
                content: newData.mainScroll.description || ''
            })
        }

        // 处理文件数据
        if (newData.pictureFiles && newData.pictureFiles.length > 0) {
            formData.imageText.imageUrl = newData.pictureFiles[0].url || ''
            formData.imageText.content = newData.pictureNote || ''
        }

        if (newData.audioFiles && newData.audioFiles.length > 0) {
            formData.audioText.audioUrl = newData.audioFiles[0].url || ''
            formData.audioText.content = newData.audioNote || ''
        }

        if (newData.videoFiles && newData.videoFiles.length > 0) {
            formData.videoText.videoUrl = newData.videoFiles[0].url || ''
            formData.videoText.content = newData.videoNote || ''
        }

        if (newData.subModelFiles && newData.subModelFiles.length > 0) {
            formData.modelText.modelUrl = newData.subModelFiles[0].url || ''
            formData.modelText.content = newData.subModelNote || ''
        }
    }
}, { immediate: true, deep: true })

// 监听表单数据变化，向父组件发送更新事件
watch(formData, (newData) => {
    emit('update', 'exhibitionConfig', { ...newData })
}, { deep: true })

// 处理时间点个数变化
const handlePointCountChange = (count: number) => {
    const currentLength = formData.timeline.points.length

    if (count > currentLength) {
        // 增加时间点
        for (let i = currentLength; i < count; i++) {
            formData.timeline.points.push({
                year: '',
                title: '',
                description: '',
                imageUrl: ''
            })
        }
    } else if (count < currentLength) {
        // 减少时间点
        formData.timeline.points.splice(count)
    }

    // 确保当前激活的时间点在有效范围内
    if (activeTimelinePoint.value >= count) {
        activeTimelinePoint.value = Math.max(0, count - 1)
    }
}

// 获取表单数据
const getData = async () => {
    // 转换时间轴数据为API格式
    const timeAxisList = formData.timeline.points.map((point, index) => ({
        timeId: index + 1,
        sortOrder: index + 1,
        timePhase: parseInt(point.year) || 0,
        title: point.title,
        description: point.description,
        cover: point.imageUrl
    }))

    // 转换长卷数据为API格式
    const mainScroll = {
        mainPageFlag: '1',
        sortOrder: 1,
        cover: formData.scroll.imageUrl,
        coverDescription: '',
        title: formData.scroll.title,
        subtitle: '',
        description: formData.scroll.content
    }

    return {
        // 时间轴配置
        timeAxisQuantity: formData.timeline.pointCount,
        timeAxisList: timeAxisList,

        // 长卷配置
        subScrollQuantity: 1,
        mainScroll: mainScroll,
        subScrollList: [],

        // 文件描述
        pictureNote: formData.imageText.content,
        audioNote: formData.audioText.content,
        videoNote: formData.videoText.content,
        subModelNote: formData.modelText.content
    }
}

// 暴露方法给父组件
defineExpose({
    getData
})
</script>

<style lang="scss" scoped>
$colorBorder: var(--el-color-primary);
$colorBg: var(--el-color-primary-light-9);
.exhibition-container {
    height: 100%;
    overflow: hidden;
    padding-top: 16px;

    .exhibition-layout {
        display: flex;
        height: 100%;

        .sub-tabs {
            width: 200px;
            border-right: 1px solid #e4e7ed;

            .sub-tab-item {
                padding: 12px 24px;
                cursor: pointer;
                color: #606266;
                transition: all 0.3s;
                border-right: 3px solid transparent;

                &:hover {
                    background: $colorBg;
                    color: $colorBorder;
                }

                &.active {
                    background: $colorBg;
                    color: $colorBorder;
                    border-right-color: $colorBorder;
                    font-weight: 500;
                }
            }
        }

        .content-area {
            flex: 1;
            overflow: auto;

            .tab-content {
                height: 100%;

                .form-content {
                    padding: 0 20px 20px 20px;

                    h3 {
                        margin: 0 0 24px 0;
                        color: #303133;
                        font-size: 18px;
                        font-weight: 500;
                    }
                }
            }
        }
    }

    .image-uploader,
    .audio-uploader,
    .video-uploader,
    .model-uploader,
    .scroll-uploader {
        .uploaded-image {
            width: 178px;
            height: 178px;
            object-fit: cover;
            border-radius: 6px;
        }

        .uploader-icon {
            font-size: 28px;
            color: #8c939d;
            width: 178px;
            height: 178px;
            text-align: center;
            line-height: 178px;
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;

            &:hover {
                border-color: $colorBorder;
            }
        }
    }

    .timeline-points {
        .point-tabs {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 24px;
            padding: 16px;
            background: #f5f7fa;
            border-radius: 4px;

            .point-tab {
                padding: 8px 16px;
                background: #fff;
                border: 1px solid #e4e7ed;
                border-radius: 4px;
                cursor: pointer;
                transition: all 0.3s;
                font-size: 14px;

                &:hover {
                    border-color: $colorBorder;
                    color: $colorBorder;
                }

                &.active {
                    background: $colorBorder;
                    border-color: $colorBorder;
                    color: #fff;
                }
            }
        }

        .point-config {
            padding: 24px;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            background: #fff;
        }
    }

    .timeline-image-uploader {
        .timeline-image {
            width: 120px;
            height: 120px;
            object-fit: cover;
            border-radius: 4px;
        }

        .timeline-image-uploader-icon {
            font-size: 28px;
            color: #8c939d;
            width: 120px;
            height: 120px;
            text-align: center;
            line-height: 120px;
            border: 1px dashed #d9d9d9;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;

            &:hover {
                border-color: $colorBorder;
            }
        }
    }
}
</style>