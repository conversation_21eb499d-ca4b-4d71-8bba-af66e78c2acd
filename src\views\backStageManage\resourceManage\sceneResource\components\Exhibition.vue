<template>
	<div class="exhibition-container">
		<div class="exhibition-layout">
			<!-- 左侧子Tab导航 -->
			<div class="sub-tabs">
				<div v-for="tab in subTabs" :key="tab.key" :class="['sub-tab-item', { active: activeSubTab === tab.key }]" @click="activeSubTab = tab.key">
					{{ tab.label }}
				</div>
			</div>

			<!-- 右侧内容区域 -->
			<div class="content-area">
				<!-- 图片+文字 -->
				<div v-if="activeSubTab === 'imageText'" class="tab-content">
					<div class="form-content">
						<h3>图片</h3>
						<el-form label-width="120px">
							<el-form-item label="图片">
								<UploadItem
									file-type="image"
									:max-size="10"
									:file-info="formData.imageText.file"
									@upload-success="(data) => handleUploadSuccess('imageText', data)"
									@upload-error="(error) => handleUploadError(error)"
									@delete-file="() => handleDeleteFile('imageText')"
								/>
							</el-form-item>
							<el-form-item label="文字内容">
								<el-input
									v-model="formData.imageText.content"
									type="textarea"
									:rows="10"
									maxlength="500"
									show-word-limit
									placeholder="请输入文字内容"
								/>
							</el-form-item>
						</el-form>
					</div>
				</div>

				<!-- 音频+文字 -->
				<div v-if="activeSubTab === 'audioText'" class="tab-content">
					<div class="form-content">
						<h3>音频</h3>
						<el-form label-width="120px">
							<el-form-item label="音频文件">
								<UploadItem
									file-type="audio"
									:max-size="50"
									:file-info="formData.audioText.file"
									@upload-success="(data) => handleUploadSuccess('audioText', data)"
									@upload-error="(error) => handleUploadError(error)"
									@delete-file="() => handleDeleteFile('audioText')"
								/>
							</el-form-item>
							<el-form-item label="文字内容">
								<el-input
									v-model="formData.audioText.content"
									type="textarea"
									:rows="10"
									maxlength="500"
									show-word-limit
									placeholder="请输入文字内容"
								/>
							</el-form-item>
						</el-form>
					</div>
				</div>

				<!-- 视频+文字 -->
				<div v-if="activeSubTab === 'videoText'" class="tab-content">
					<div class="form-content">
						<h3>视频</h3>
						<el-form label-width="120px">
							<el-form-item label="视频文件">
								<UploadItem
									file-type="video"
									:max-size="500"
									:file-info="formData.videoText.file"
									@upload-success="(data) => handleUploadSuccess('videoText', data)"
									@upload-error="(error) => handleUploadError(error)"
									@delete-file="() => handleDeleteFile('videoText')"
								/>
							</el-form-item>
							<el-form-item label="文字内容">
								<el-input
									v-model="formData.videoText.content"
									type="textarea"
									:rows="10"
									maxlength="500"
									show-word-limit
									placeholder="请输入文字内容"
								/>
							</el-form-item>
						</el-form>
					</div>
				</div>

				<!-- 模型+文字 -->
				<div v-if="activeSubTab === 'modelText'" class="tab-content">
					<div class="form-content">
						<h3>模型</h3>
						<el-form label-width="120px">
							<el-form-item label="3D模型文件">
								<UploadItem
									file-type="model"
									:max-size="1024"
									:file-info="formData.modelText.file"
									@upload-success="(data) => handleUploadSuccess('modelText', data)"
									@upload-error="(error) => handleUploadError(error)"
									@delete-file="() => handleDeleteFile('modelText')"
								/>
							</el-form-item>
							<el-form-item label="文字内容">
								<el-input
									v-model="formData.modelText.content"
									type="textarea"
									:rows="10"
									maxlength="500"
									show-word-limit
									placeholder="请输入文字内容"
								/>
							</el-form-item>
						</el-form>
					</div>
				</div>

				<!-- 时间轴 -->
				<div v-if="activeSubTab === 'timeline'" class="tab-content">
					<div class="form-content">
						<h3>时间轴</h3>
						<el-form label-width="120px" label-position="top">
							<el-form-item label="时间点个数">
								<el-input-number v-model="formData.timeline.pointCount" :min="1" :max="8" @change="handlePointCountChange" style="width: 200px" />
								<span style="margin-left: 12px; color: #909399">最多8个时间点</span>
							</el-form-item>
							<el-form-item label="" v-if="formData.timeline.pointCount > 0">
								<div class="timeline-points">
									<!-- 时间点选项卡 -->
									<div class="point-tabs">
										<div
											v-for="(point, index) in formData.timeline.points"
											:key="index"
											:class="['point-tab', { active: activeTimelinePoint === index }]"
											@click="activeTimelinePoint = index"
										>
											时间点{{ index + 1 }}
										</div>
									</div>
									<!-- 当前时间点配置 -->
									<div class="point-config" v-if="formData.timeline.points[activeTimelinePoint]">
										<el-form label-width="80px" label-position="top">
											<el-form-item label="时相" required>
												<el-input
													v-model="formData.timeline.points[activeTimelinePoint].timePhase"
													maxlength="4"
													show-word-limit
													placeholder="请输入年份，如：1985"
												/>
											</el-form-item>
											<el-form-item label="标题">
												<el-input
													v-model="formData.timeline.points[activeTimelinePoint].title"
													maxlength="50"
													show-word-limit
													placeholder="请输入标题"
												/>
											</el-form-item>
											<el-form-item label="描述">
												<el-input
													v-model="formData.timeline.points[activeTimelinePoint].description"
													type="textarea"
													:rows="3"
													maxlength="100"
													show-word-limit
													placeholder="请输入描述"
												/>
											</el-form-item>
											<el-form-item label="插图" prop="cover" style="width: 440px">
												<ImageUpload
													v-model:imageUrl="formData.timeline.points[activeTimelinePoint].cover"
													height="120px"
													width="120px"
													borderRadius="0%"
													uploadFileUrl="/exhibition/buildingResourceFile/cover"
												>
													<template #empty>
														<el-icon><Picture /></el-icon>
														<span>点击上传插图</span>
													</template>
												</ImageUpload>
												<div style="position: absolute; right: 0px; width: 300px; color: #999999">
													<div>图片支持JPG/PNG格式且最大5M</div>
												</div>
											</el-form-item>
										</el-form>
									</div>
								</div>
							</el-form-item>
						</el-form>
					</div>
				</div>

				<!-- 长卷形式 -->
				<div v-if="activeSubTab === 'scroll'" class="tab-content">
					<div class="form-content">
						<h3>长卷</h3>
						<div class="section-title">
							<img src="/@/assets/img/backManage/Union.png" alt="基本信息" class="section-icon" />
							<span>主页面</span>
						</div>
						<el-form label-width="120px" label-position="top">
							<el-form-item label="插图" prop="cover" style="width: 440px">
								<ImageUpload
									v-model:imageUrl="formData.scroll.cover"
									height="120px"
									width="120px"
									borderRadius="0%"
									uploadFileUrl="/exhibition/buildingResourceFile/cover"
								>
									<template #empty>
										<el-icon><Picture /></el-icon>
										<span>点击上传插图</span>
									</template>
								</ImageUpload>
								<div style="position: absolute; right: 0px; width: 300px; color: #999999">
									<div>图片支持JPG/PNG格式且最大5M</div>
								</div>
							</el-form-item>
							<el-form-item label="插图描述">
								<el-input
									v-model="formData.scroll.coverDescription"
									type="textarea"
									:rows="3"
									maxlength="100"
									show-word-limit
									placeholder="请输入插图描述"
								/>
							</el-form-item>
							<el-form-item label="主标题">
								<el-input v-model="formData.scroll.title" maxlength="50" show-word-limit placeholder="请输入插图描述" />
							</el-form-item>
							<el-form-item label="副标题">
								<el-input v-model="formData.scroll.subtitle" maxlength="50" show-word-limit placeholder="请输入插图描述" />
							</el-form-item>
							<el-form-item label="全局描述">
								<el-input
									v-model="formData.scroll.description"
									type="textarea"
									:rows="3"
									maxlength="100"
									show-word-limit
									placeholder="请输入插图描述"
								/>
							</el-form-item>
							<div class="section-title">
								<img src="/@/assets/img/backManage/Union.png" alt="基本信息" class="section-icon" />
								<span>子页面</span>
							</div>
							<el-form-item label="子页面个数">
								<el-input-number v-model="formData.scroll.subPageCount" :min="1" :max="5" @change="updateSubPageCount" style="width: 200px" />
								<span style="margin-left: 12px; color: #999999">最大支持5个子页面</span>
							</el-form-item>
							<el-form-item label="">
								<div class="timeline-points">
									<div class="point-tabs">
										<div
											v-for="(subPage, index) in formData.scroll.subPages"
											:key="subPage.scrollId"
											class="timeline-point"
											:class="['point-tab', { active: activeSubPage === index }]"
											@click="activeSubPage = index"
										>
											<div class="point-label">子页面{{ index + 1 }}</div>
										</div>
									</div>
									<div v-if="formData.scroll.subPages[activeSubPage]" class="point-config">
										<el-form label-width="120px" label-position="top">
											<el-form-item label="插图" prop="cover" style="width: 440px">
												<ImageUpload
													v-model:imageUrl="formData.scroll.subPages[activeSubPage].cover"
													height="120px"
													width="120px"
													borderRadius="0%"
													uploadFileUrl="/exhibition/buildingResourceFile/cover"
												>
													<template #empty>
														<el-icon><Picture /></el-icon>
														<span>点击上传插图</span>
													</template>
												</ImageUpload>
												<div style="position: absolute; right: 0px; width: 300px; color: #999999">
													<div>图片支持JPG/PNG格式且最大5M</div>
												</div>
											</el-form-item>
											<el-form-item label="插图描述">
												<el-input
													v-model="formData.scroll.subPages[activeSubPage].coverDescription"
													type="textarea"
													:rows="3"
													maxlength="100"
													show-word-limit
													placeholder="请输入插图描述"
												/>
											</el-form-item>
											<el-form-item label="主标题">
												<el-input v-model="formData.scroll.subPages[activeSubPage].title" maxlength="50" show-word-limit placeholder="请输入主标题" />
											</el-form-item>
											<el-form-item label="副标题">
												<el-input
													v-model="formData.scroll.subPages[activeSubPage].subtitle"
													maxlength="50"
													show-word-limit
													placeholder="请输入副标题"
												/>
											</el-form-item>
											<el-form-item label="全局描述">
												<el-input
													v-model="formData.scroll.subPages[activeSubPage].description"
													type="textarea"
													:rows="3"
													maxlength="250"
													show-word-limit
													placeholder="请输入全局描述"
												/>
											</el-form-item>
										</el-form>
									</div>
								</div>
							</el-form-item>
						</el-form>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { Picture } from '@element-plus/icons-vue';
import UploadItem from './UploadItem.vue';
import ImageUpload from '/@/components/Upload/Image.vue';

// 文件信息接口
interface FileInfo {
	id?: string;
	name?: string;
	url?: string;
	size?: number;
	type?: string;
}

interface Props {
	data: any;
}

const props = defineProps<Props>();

// 子Tab配置
const subTabs = [
	{ key: 'imageText', label: '图片+文字' },
	{ key: 'audioText', label: '音频+文字' },
	{ key: 'videoText', label: '视频+文字' },
	{ key: 'modelText', label: '模型+文字' },
	{ key: 'timeline', label: '时间轴' },
	{ key: 'scroll', label: '长卷形式' },
];

// 当前激活的子Tab
const activeSubTab = ref('imageText');

// 当前激活的时间点
const activeTimelinePoint = ref(0);

// 当前激活的子页面
const activeSubPage = ref(0);

// 表单数据
const formData = reactive({
	imageText: {
		file: {} as FileInfo,
		content: '',
	},
	audioText: {
		file: {} as FileInfo,
		content: '',
	},
	videoText: {
		file: {} as FileInfo,
		content: '',
	},
	modelText: {
		file: {} as FileInfo,
		content: '',
	},
	timeline: {
		pointCount: 1,
		points: [
			{
				timePhase: '',
				title: '',
				description: '',
				cover: '',
			},
		] as Array<{ timePhase: string; title: string; description: string; cover: string }>,
	},
	scroll: {
		title: '',
		subtitle: '',
		cover: '',
		coverDescription: '',
		description: '',
		subPageCount: 1,
		subPages: [
			{
				scrollId: 1,
				cover: '',
				coverDescription: '',
				title: '',
				subtitle: '',
				description: '',
			},
		] as Array<{
			scrollId: number;
			cover: string;
			coverDescription: string;
			title: string;
			subtitle: string;
			description: string;
		}>,
	},
});

onMounted(() => {
	initForm(props.data);
});

// 初始化数据
const initForm = (newData: any) => {
	if (newData) {
		// 处理时间轴数据
		if (newData.timeAxisList && newData.timeAxisList.length > 0) {
			formData.timeline.pointCount = newData.timeAxisQuantity || newData.timeAxisList.length;
			formData.timeline.points = newData.timeAxisList || [];
		}

		// 处理长卷数据
		if (newData.mainScroll) {
			Object.assign(formData.scroll, newData.mainScroll);
		}

		// 处理长卷子页面数据
		if (newData.subScrollList && newData.subScrollList.length > 0) {
			formData.scroll.subPageCount = newData.subScrollList.length;
			formData.scroll.subPages = newData.subScrollList || [];
		}

		// 处理文字描述数据
		formData.imageText.content = newData.pictureNote || '';
		formData.audioText.content = newData.audioNote || '';
		formData.videoText.content = newData.videoNote || '';
		formData.modelText.content = newData.subModelNote || '';

		// 处理文件数据
		if (newData.pictureFiles && newData.pictureFiles.length > 0) {
			const fileData = newData.pictureFiles[0];
			formData.imageText.file = {
				...fileData,
				name: fileData.mainFile && fileData.fileExtension ? `${fileData.mainFile}.${fileData.fileExtension}` : fileData.mainFile || '',
				type: fileData.type || fileData.fileExtension || '',
			};
		}

		if (newData.audioFiles && newData.audioFiles.length > 0) {
			const fileData = newData.audioFiles[0];
			formData.audioText.file = {
				...fileData,
				name: fileData.mainFile && fileData.fileExtension ? `${fileData.mainFile}.${fileData.fileExtension}` : fileData.mainFile || '',
				type: fileData.type || fileData.fileExtension || '',
			};
		}

		if (newData.videoFiles && newData.videoFiles.length > 0) {
			const fileData = newData.videoFiles[0];
			formData.videoText.file = {
				...fileData,
				name: fileData.mainFile && fileData.fileExtension ? `${fileData.mainFile}.${fileData.fileExtension}` : fileData.mainFile || '',
				type: fileData.type || fileData.fileExtension || '',
			};
		}

		if (newData.subModelFiles && newData.subModelFiles.length > 0) {
			const fileData = newData.subModelFiles[0];
			formData.modelText.file = {
				...fileData,
				name: fileData.mainFile && fileData.fileExtension ? `${fileData.mainFile}.${fileData.fileExtension}` : fileData.mainFile || '',
				type: fileData.type || fileData.fileExtension || '',
			};
		}
	}
};

// 处理时间点个数变化
const handlePointCountChange = (count: number) => {
	const currentLength = formData.timeline.points.length;

	if (count > currentLength) {
		// 增加时间点
		for (let i = currentLength; i < count; i++) {
			formData.timeline.points.push({
				timePhase: '',
				title: '',
				description: '',
				cover: '',
			});
		}
	} else if (count < currentLength) {
		// 减少时间点
		formData.timeline.points.splice(count);
	}

	// 确保当前激活的时间点在有效范围内
	if (activeTimelinePoint.value >= count) {
		activeTimelinePoint.value = Math.max(0, count - 1);
	}
};

// 更新子页面数量
const updateSubPageCount = (count: number) => {
	const currentLength = formData.scroll.subPages.length;

	if (count > currentLength) {
		// 增加子页面
		for (let i = currentLength; i < count; i++) {
			formData.scroll.subPages.push({
				scrollId: i + 1,
				cover: '',
				coverDescription: '',
				title: '',
				subtitle: '',
				description: '',
			});
		}
	} else if (count < currentLength) {
		// 减少子页面
		formData.scroll.subPages.splice(count);
	}

	// 确保当前激活的子页面在有效范围内
	if (activeSubPage.value >= count) {
		activeSubPage.value = Math.max(0, count - 1);
	}
};

// 通用上传成功处理
const handleUploadSuccess = (type: 'imageText' | 'audioText' | 'videoText' | 'modelText', data: any) => {
	// 存储文件信息，主要是文件ID
	formData[type].file = {
		id: data.id,
		name: data.mainFile + '.' + data.fileExtension,
		url: data.url,
		size: data.size || 0,
	};
};

// 通用上传失败处理
const handleUploadError = (error: any) => {
	ElMessage.error(error?.msg || `上传失败`);
};

// 通用删除文件处理
const handleDeleteFile = (type: 'imageText' | 'audioText' | 'videoText' | 'modelText') => {
	// 清空文件信息
	formData[type].file = {};
};

// 获取表单数据
const getData = async () => {
	const configType = {
		imageText: 1,
		audioText: 2,
		videoText: 3,
		modelText: 4,
		timeline: 5,
		scroll: 6,
	};
	switch (activeSubTab.value) {
		case 'imageText':
		case 'audioText':
		case 'videoText':
		case 'modelText':
            const data: any = {
				configType: configType[activeSubTab.value],
				fileIds: formData[activeSubTab.value].file.id ? [formData[activeSubTab.value].file.id] : [],
			};
            if (activeSubTab.value === 'imageText') {
                data.pictureNote = formData.imageText.content;
            } else if (activeSubTab.value === 'audioText') {
                data.audioNote = formData.audioText.content;
            } else if (activeSubTab.value === 'videoText') {
                data.videoNote = formData.videoText.content;
            } else if (activeSubTab.value === 'modelText') {
                data.modelNote = formData.modelText.content;
            }
			return data;
		case 'timeline':
			if (formData.timeline.points.some((point) => !point.timePhase)) {
				throw new Error('时相不能为空，请检查时间点配置是否完整');
			}
			const timeAxisList = formData.timeline.points.map((point, index) => ({
				...point,
				timeId: index + 1,
				sortOrder: index + 1,
			}));
			return {
				configType: configType[activeSubTab.value],
				timeAxisQuantity: formData.timeline.pointCount,
				timeAxisList: timeAxisList,
			};
		case 'scroll':
			const mainScroll = {
				mainPageFlag: '1',
				sortOrder: 1,
				cover: formData.scroll.cover,
				coverDescription: formData.scroll.coverDescription,
				title: formData.scroll.title,
				subtitle: formData.scroll.subtitle,
				description: formData.scroll.description,
			};
			const subScrollList = formData.scroll.subPages.map((subPage, index) => ({
				...subPage,
				mainPageFlag: '0',
				sortOrder: index + 1,
			}));
			return {
				configType: configType[activeSubTab.value],
				subScrollQuantity: formData.scroll.subPageCount,
				mainScroll: mainScroll,
				subScrollList: subScrollList,
			};
		default:
			return {};
	}
};

// 暴露方法给父组件
defineExpose({
	getData,
});
</script>

<style lang="scss" scoped>
$colorBorder: var(--el-color-primary);
$colorBg: var(--el-color-primary-light-9);
.exhibition-container {
	height: 100%;
	overflow: hidden;
	padding-top: 16px;

	.exhibition-layout {
		display: flex;
		height: 100%;

		.sub-tabs {
			width: 200px;
			border-right: 1px solid #e4e7ed;

			.sub-tab-item {
				padding: 12px 24px;
				cursor: pointer;
				color: #606266;
				transition: all 0.3s;
				border-right: 3px solid transparent;

				&:hover {
					background: $colorBg;
					color: $colorBorder;
				}

				&.active {
					background: $colorBg;
					color: $colorBorder;
					border-right-color: $colorBorder;
					font-weight: 500;
				}
			}
		}

		.content-area {
			flex: 1;
			overflow: auto;

			.tab-content {
				height: 100%;

				.form-content {
					padding: 0 120px 20px 24px;

					h3 {
						margin: 0 0 24px 0;
						color: #000;
						font-size: 18px;
						font-weight: 600;
					}

					.section-title {
						font-size: 14px;
						font-weight: 400;
						color: rgba(0, 0, 0, 0.9);
						padding-bottom: 18px;
						position: relative;
						display: flex;
						align-items: center;

						.section-icon {
							width: 16px;
							height: 16px;
							margin-right: 8px;
							object-fit: contain;
						}
					}
				}
			}
		}
	}

	.image-uploader,
	.audio-uploader,
	.video-uploader,
	.model-uploader,
	.scroll-uploader {
		.uploaded-image {
			width: 178px;
			height: 178px;
			object-fit: cover;
			border-radius: 6px;
		}

		.uploader-icon {
			font-size: 28px;
			color: #8c939d;
			width: 178px;
			height: 178px;
			text-align: center;
			line-height: 178px;
			border: 1px dashed #d9d9d9;
			border-radius: 6px;
			cursor: pointer;
			transition: all 0.3s;

			&:hover {
				border-color: $colorBorder;
			}
		}
	}

	.timeline-points {
		width: 100%;
		.point-tabs {
			display: flex;
			flex-wrap: wrap;
			gap: 8px;
			margin-bottom: 10px;
			padding: 16px;
			background: #f5f7fa;
			border-radius: 4px;

			.point-tab {
				padding: 4px 16px;
				background: #fff;
				border: 1px solid #e4e7ed;
				border-radius: 4px;
				cursor: pointer;
				transition: all 0.3s;
				font-size: 14px;

				&:hover {
					border-color: $colorBorder;
					color: $colorBorder;
				}

				&.active {
					background: $colorBorder;
					border-color: $colorBorder;
					color: #fff;
				}
			}
		}

		.point-config {
			padding: 10px 16px 16px 16px;
			border: 1px solid #e4e7ed;
			border-radius: 4px;
			background: #fff;
			.el-form-item {
				margin-bottom: 10px;
			}
		}
	}
}
</style>
