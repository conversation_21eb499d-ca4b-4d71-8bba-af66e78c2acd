<template>
  <div class="detail-page" v-loading="isLoading">
    <!-- 文化遗产资源类型详情页 -->
    <div class="detail-page-content" v-if="currentType == 2">
      <div class="header">
        <div class="img-box">
          <img :src="currentInfo.cover || noCoverImg" :class="{ 'no-cover': !currentInfo.cover }" @error="onImgError" />
        </div>
        <div class="title-section">
          <div class="resouce-name">{{ currentInfo.name }}</div>
          <div class="info-grid">
            <div class="info-item">
              <span class="label">资源目录：</span>
              <span class="value">{{ currentInfo.assets_num_name }}</span>
            </div>
            <div class="info-item">
              <span class="label">公开状态：</span>
              <span class="value">{{ currentInfo.public_state }}</span>
            </div>
            <div class="info-item">
              <span class="label">所属单位：</span>
              <span class="value">{{ currentInfo.create_dept }}</span>
            </div>
            <div class="info-item">
              <span class="label">资源标签：</span>
              <span class="value keyword">{{ currentInfo.assets_tag }}</span>
            </div>
            <div class="info-item">
              <span class="label">创建时间：</span>
              <span class="value">{{ currentInfo.create_time }}</span>
            </div>
          </div>
        </div>
        <div class="actions">
          <div class="stats">
            <span class="views">
              <i />
              <span>{{ operateSummary.infoCount }}</span>
            </span>
            <span class="downloads">
              <i></i>
              <span>{{ operateSummary.downloadCount }}</span>
            </span>
          </div>
          <div class="action-buttons" v-if="!token">
            <span class="action-button" @click="onOperate(operateSummary.isPraise ? 'unlike' : 'like')">
              <img v-if="operateSummary.isPraise" src="/@/assets/img/squareNew/icon_like_active.png"/>
              <img v-else src="/@/assets/img/squareNew/icon_like.png"/>
              <span>点赞</span>
            </span>
            <span class="action-button" @click="onOperate(operateSummary.isCollect ? 'unCollect' : 'collect')">
              <img v-if="operateSummary.isCollect" src="/@/assets/img/squareNew/icon_collect_active.png" />
              <img v-else src="/@/assets/img/squareNew/icon_collect.png"/>
              <span>收藏</span>
            </span>
          </div>
        </div>
      </div>
      <div class="content-section">
        <div class="content-title">内容简介</div>
        <div class="content-desc">
          {{ currentInfo.desc || '暂无简介' }}
        </div>
      </div>
      <div class="main-content-area" v-if="previewType == '1' && currentType != 1 && allFileList.length">
        <div class="previer-content">
          <FileViewer :record="currentPreviewRecord" v-if="currentPreviewRecord?.id" />
        </div>
        <div class="file-list-section">
          <div class="list-title">文件列表</div>
          <div class="list-box">
            <div class="tabbar-box">
              <div class="tabbar-item" v-for="(item, index) in tabbarList" :key="index"
                :class="{ active: item.type == currentFileListType }"
                @click="swichFileType(item.type)">
                <span>{{ item.label }}</span>
                <span>（{{ item.count || 0 }}）</span>
              </div>
            </div>
            <div class="list-outbox">
              <div class="item-box" v-for="item in currentShowFileList" :key="item.id"
                :class="{ active: item.id == currentPreviewRecord.id }"
                @click="switchFile(item)">
                <img class="cover-box" :src="item.cover || noCoverImg" :class="{ 'no-cover': !item.cover }" @error="onImgError" />
                <div class="type-box">{{ getFileType(item.type)?.name }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="preview-pdf" v-if="previewType === '2'">
        <PdfViewer :fileUrl="null" />
      </div>
    </div>
    <!-- 文件类型详情页 -->
    <div class="detail-page-content file-detail-page" v-if="currentType == 1 && currentInfo.id">
      <div class="file-name">{{ currentInfo.name }}</div>
      <div class="file-base-info flex justify-between">
        <div class="flex items-center">
          <div>发布时间：{{ currentInfo.createTime }}</div>
          <div class="ml-10">© 数字武当<span class="ml-2 copy-right">版权所有</span></div>
          <div class="ml-10 cursor-pointer flex items-center" v-if="!token || currentInfo.sharePermission == 2" @click="onDownload">
            <el-icon><Download /></el-icon>
            <span class="ml-1">下载</span>
          </div>
        </div>
        <div class="stats" v-if="!token">
          <span class="views">
            <i />
            <span>{{ operateSummary.infoCount }}</span>
          </span>
          <span class="downloads">
            <i></i>
            <span>{{ operateSummary.downloadCount }}</span>
          </span>
        </div>
      </div>
      <div class="file-preview-box" :class="{ 'is-audio': currentInfo.type == 5 }">
        <FileViewer :record="currentInfo" v-if="currentInfo?.id" />
      </div>
      <div class="file-info-list">
        <div class="info-item">
          <span class="label">文件目录：</span>
          <span class="value">{{ currentInfo.folderPath ? currentInfo.folderPath.split(',').join(' > ') : '-' }}</span>
        </div>
        <div class="info-item">
          <span class="label">文件密级：</span>
          <span class="value">{{ currentInfo.securityName }}</span>
        </div>
        <div class="info-item">
          <span class="label">来源单位：</span>
          <span class="value">{{ currentInfo.deptName }}</span>
        </div>
        <div class="info-item">
          <span class="label">文件类型：</span>
          <span class="value">{{ getFileType(currentInfo.type)?.name || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="label">文件格式：</span>
          <span class="value">{{ currentInfo.format }}</span>
        </div>
        <div class="info-item">
          <span class="label">文件大小：</span>
          <span class="value">{{ ((currentInfo.totalSize || 0) / 1024 / 1024).toFixed(2) + 'MB' }}</span>
        </div>
        <div class="info-item">
          <span class="label">文件标签：</span>
          <span class="value keyword">{{ currentInfo.labels }}</span>
        </div>
        <div class="info-item">
          <span class="label">文件描述：</span>
          <span class="value">{{ currentInfo.desc }}</span>
        </div>
      </div>
      <div class="action-buttons" v-if="!token">
        <span class="action-button" @click="onOperate(operateSummary.isPraise ? 'unlike' : 'like')">
          <img v-if="operateSummary.isPraise" src="/@/assets/img/squareNew/icon_like_active.png"/>
          <img v-else src="/@/assets/img/squareNew/icon_like.png"/>
          <span>点赞</span>
        </span>
        <span class="action-button" @click="onOperate(operateSummary.isCollect ? 'unCollect' : 'collect')">
          <img v-if="operateSummary.isCollect" src="/@/assets/img/squareNew/icon_collect_active.png" />
          <img v-else src="/@/assets/img/squareNew/icon_collect.png"/>
          <span>收藏</span>
        </span>
        <span class="action-button" @click="shareModalVisible = true">
          <img src="/@/assets/img/squareNew/shareIcon.png" />
          <span>分享</span>
        </span>
      </div>
    </div>
    <RelatedResources v-if="!token && currentInfo.id" :currentInfo="currentInfo" :currentType="currentType" />
    <ShareInfo v-if="token && currentInfo.id" />
    <ShareModal
      v-if="shareModalVisible"
      v-model:visible="shareModalVisible"
      :tabName="route.query.tabName"
      :resourceId="route.params.id"
      @close="handleShareModalClose"
    />
    <ApplyModal
      v-model:visible="applyModalVisible"
      :tabName="route.query.tabName"
      :resourceId="route.params.id"
      @close="handleApplyModalClose"
    />
  </div>
</template>

<script setup>
import { ref } from "vue";
import RelatedResources from "/@/views/squareNew/main/resource/detail/components/RelatedResources.vue";
import PdfViewer from "/@/components/FilePreviewer/PdfViewer.vue";
import ShareModal from "/@/views/squareNew/main/resource/components/shareModal.vue";
import ShareInfo from "/@/views/squareNew/main/resource/detail/components/shareInfo.vue";
import { fetchList, getSummary, getObj, actionDownload } from '/@/api/resource/filelist/file';
import { getModelDataById } from "/@/api/resource/data/resource";
import { praise, unPraise, collect, unCollect, summary, getFlag } from "/@/api/square";
import { getShareLink } from "/@/api/squareNew";
import FileViewer from '/@/views/resourceManagement/resourceFile/detail/FileViewer.vue';
import ApplyModal from '/@/views/squareNew/main/resource/components/applyModal.vue'
import { getFileType } from '/@/config/resourceConfig';
import { ElMessage, ElMessageBox } from 'element-plus'
import noCoverImg from "/@/assets/img/squareNew/no_cover.png";
import { useRoute } from "vue-router";

const route = useRoute();
const isLoading = ref(false);
const previewType = ref("1"); // 1: 各类文件列表预览 2: 档案类
const currentInfo = ref({}); // 详情信息
const allFileList = ref([]); // 资源关联文件列表（全部类型）
const currentShowFileList = ref([]); // 当前展示的文件列表
const currentFileListType = ref("0"); // 当前选中文件列表类型
const tabbarList = ref([]); // 文件类型统计
const currentPreviewRecord = ref({}); // 当前预览文件信息
const operateSummary = ref({}); // 预览量、点赞量等
const currentType = ref(0); // 1: 文件资源 2: 文化遗产资源
const token = ref(""); // 分享页使用，有值时代表为分享页
const shareModalVisible = ref(false);
const applyModalVisible = ref(false);

onMounted(() => {
  currentType.value = route.query.type;
  token.value = route.query.token;
  if (token.value) {
    handleSharePasswordSubmit();
  } else {
    getDetailInfo();
    getOperateSummary();
  }
});

const handleShareModalClose = () => {
  shareModalVisible.value = false;
};

// 获取资源详情信息（区分文件类和文化遗产资源类）
const getDetailInfo = async () => {
  const id = route.params.id;
  const tabName = route.query.tabName;
  isLoading.value = true;
  if (currentType.value == 1) {
    let res = await getObj(id);
    isLoading.value = false;
    currentInfo.value = res?.data || {};
  } else if (currentType.value == 2) {
    // 资源关联的文件列表
    Promise.all([
      getSummary({ resourceId: id, tabName }),
      fetchList({ resourceId: id, tabName, current: 1, size: 10000 }),
    ]).then(res => {
      if (res[0]?.data?.map) {
        let arr = []
        for (let i in res[0].data.map) {
          arr.push({ count: res[0].data.map[i], label: getFileType(i)?.name, type: i })
        }
        arr.unshift({ count: res[0].data.total, label: '全部', type: '0' })
        tabbarList.value = arr;
      }
      allFileList.value = res[1]?.data?.records || [];
      swichFileType('0');
    })
    // 资源基本信息
    let res = await getModelDataById({ resourceId: id, tableName: tabName }, 0);
    isLoading.value = false;
    currentInfo.value = {
      ...res?.data.datas || {},
      ...res?.data.extraData || {},
      name: res?.data.datas?.assets_name || "",
      cover: res?.data.datas?.assets_cover || "",
      desc: res?.data.datas?.assets_remark || "",
    };
  }
};

// 为分享页面时，填写密码验证
const handleSharePasswordSubmit = () => {
  let shareParams = route.query.shareParams ? JSON.parse(route.query.shareParams) : {};
  if (shareParams.passwordEnabled) {
    ElMessageBox.prompt('请输入访问密码', '分享验证', {
      confirmButtonText: '确认',
      showCancelButton: false,
      inputPattern: /^.{1,}$/,
      inputErrorMessage: '请输入访问密码',
      beforeClose: (action, instance, done) => {
        if (action === 'confirm') {
          const inputValue = instance.inputValue;
          instance.confirmButtonLoading = true;
          getShareLink({
            resourceId: route.params.id,
            tabName: route.query.tabName,
            password: inputValue,
            token: token.value
          }).then(res => {
            instance.confirmButtonLoading = false;
            if (res?.code == 0) {
              done(); // 成功时关闭弹窗
              currentInfo.value = {
                ...res?.data || {},
                ...shareParams
              };
            } else {
              ElMessage({
                type: 'error',
                message: res.msg || '验证失败',
              });
            }
          }).catch(err => {
            instance.confirmButtonLoading = false;
            ElMessage({
              type: 'error',
              message: err.msg || '验证失败',
            });
          });
        } else {
          done(); // 取消时关闭弹窗
        }
      }
    });
  } else {
    getShareLink({
      resourceId: route.params.id,
      tabName: route.query.tabName,
      token: token.value
    }).then(res => {
      currentInfo.value = {
        ...res?.data || {},
        ...shareParams
      };
    })
  }
}
const onImgError = (e) => {
  e.target.src = noCoverImg;
  e.target.classList.add("no-cover");
};
// 点赞、收藏、下载量等统计
const getOperateSummary = () => {
  const id = route.params.id;
  const tabName = route.query.tabName;
  Promise.all([
    summary({ resourceId: id, tableName: tabName }),
    getFlag({ resourceId: id, tableName: tabName }),
  ]).then(res => {
    let collectType = (res[1]?.data || []).find((item) => item.type === 5).flag;
    let praiseType = (res[1]?.data || []).find((item) => item.type === 2).flag;
    operateSummary.value = {
      ...res[0]?.data|| {},
      isCollect: collectType == 1,
      isPraise: praiseType == 1,
    };
  });
};

// 切换预览类型
const swichFileType = (type) => {
  currentFileListType.value = type;
  if (type == "0") {
    currentShowFileList.value = allFileList.value;
  } else {
    currentShowFileList.value = allFileList.value.filter(item => item.type == type);
  }
  if (currentShowFileList.value.length == 0) return;
  switchFile(currentShowFileList.value[0]);
};

// 切换文件预览时，获取文件信息
const switchFile = (file) => {
  currentPreviewRecord.value = {};
  if (file.url) {
    currentPreviewRecord.value = file;
  } else {
    getObj(file.id).then(res => {
      currentPreviewRecord.value = res?.data || {};
    });
  }
};

// （取消）点赞、收藏
const onOperate = (type) => {
  let func = null;
  const id = route.params.id;
  const tabName = route.query.tabName;
  let data = { resourceId: id, tableName: tabName };
  switch (type) {
    case "like":
      func = praise;
      break;
    case "unlike":
      func = unPraise;
      break;
    case "collect":
      func = collect;
      break;
    case "unCollect":
      func = unCollect;
      data = [data];
      break;
  }
  func(data).then(() => {
    getOperateSummary();
  });
};
// 下载文件
const onDownload = async() => {
  // securityId 文件密级
  if (!token.value && currentInfo.value.downloaded == 0) {
    // 申请权限
    applyModalVisible.value = true;
    return;
  }
  let params = {
    materialId: route.params.id,
    tabName: route.query.tabName,
    resourceId: '',
  };
  await actionDownload(params);
  const link = document.createElement('a');
  link.href = currentInfo.value.downloadUrl;
  link.download = currentInfo.value.name + (currentInfo.value.format ? '.' + currentInfo.value.format : '');
  document.body.appendChild(link);
  link.click();
  getOperateSummary();
};

const handleApplyModalClose = () => {
  applyModalVisible.value = false;
}
</script>

<style lang="scss" scoped>
.detail-page {
  font-family: Source Han Sans CN;
  margin: 0 210px 28px 210px;
  min-width: 1400px;
  min-height: 600px;

  .detail-page-content {
    background-color: #fff;
    padding: 28px 50px;

    .header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding-bottom: 20px;
      margin-bottom: 20px;
      position: relative;

      .img-box {
        width: 200px;
        height: 200px;
        margin-right: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        .no-cover {
          width: 100px;
          height: auto;
        }
      }

      .title-section {
        flex-grow: 1;

        .resouce-name {
          margin-bottom: 28px;
          font-weight: 700;
          font-size: 24px;
          color: #000000;
        }

        .info-grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 10px 20px;
          font-size: 14px;
          color: rgba(38, 38, 38, 0.70);

          .info-item {
            display: flex;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0%;
            color: #a1a1a1;
            .keyword {
              color: var(--el-color-primary);
            }
          }
        }
      }

      .actions {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        justify-content: space-between;
        min-width: 200px;
        height: 100%;
      }
    }

    .stats {
      display: flex;
      gap: 15px;
      font-size: 14px;
      color: #999;
      margin-bottom: 10px;

      .views,
      .downloads {
        display: flex;
        align-items: center;
        i {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 30px;
          height: 30px;
          &::before {
            content: "";
            background-size: 100% 100%;
            display: inline-block;
            width: 22px;
            height: 13px;
          }
        }
        > span {
          color: rgba(134, 38, 38, 1);
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 14px;
          line-height: 24.61px;
          letter-spacing: 0%;
        }
      }
      .views {
        i {
          &::before {
            background-image: url("/@/assets/img/squareNew/eyePrimaryIcon.png");
          }
        }
      }
      .downloads {
        i {
          &::before {
            background-image: url("/@/assets/img/squareNew/downloadIcon.png");
            height: 15px;
            width: 24px;
          }
        }
      }
    }

    .action-buttons {
      position: absolute;
      bottom: 20px;
      display: flex;
      justify-content: space-between;
      .action-button {
        cursor: pointer;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
        margin-left: 20px;

        > img {
          width: 24px;
          height: 24px;
          margin-bottom: 3px;
        }
        > span {
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 14px;
          line-height: 20px;
          letter-spacing: 0%;
          color: #333333;
        }
      }
    }

    .content-section {
      margin-bottom: 30px;

      .content-title {
        color: var(--el-color-primary);
        margin-bottom: 15px;
        padding-left: 10px;
        border-left: 2px solid var(--el-color-primary);
        font-weight: 700;
        font-size: 16px;
        line-height: 24.61px;
        letter-spacing: 0%;
      }

      .content-desc {
        font-weight: 400;
        font-size: 14px;
        line-height: 30px;
        color: #393939;
      }
    }

    .main-content-area {
      display: flex;
      width: 100%;
      height: 644px;
      gap: 20px;

      .previer-content {
        flex: 1;
        height: 100%;
        position: relative;
      }

      .file-list-section {
        width: 460px;
        padding: 10px 18px;
        background-color: #F4F4F4;

        .list-title {
          margin-bottom: 20px;
          font-weight: 500;
          font-size: 20px;
          line-height: 30px;
          letter-spacing: 0%;
          text-align: justify;
          color: #393939;
        }

        .list-box {
          display: flex;
          height: calc(100% - 50px);
          .tabbar-box {
            width: 93px;
            margin-right: 12px;
            .tabbar-item {
              height: 40px;
              margin-bottom: 20px;
              line-height: 40px;
              text-align: center;
              font-weight: 400;
              font-size: 14px;
              text-align: center;
              color: #393939;
              cursor: pointer;
              &.active {
                font-weight: 600;
                color: var(--el-color-primary);
                border-bottom: 2px solid var(--el-color-primary);
              }
            }
          }
          .list-outbox {
            flex: 1;
            overflow-y: auto;
            padding-right: 5px;
            .item-box {
              width: 100%;
              height: 133px;
              position: relative;
              margin-bottom: 15px;
              cursor: pointer;
              display: flex;
              align-items: center;
              justify-content: center;
              background: #eeeeee;
              &.active {
                box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
              }
              .type-box {
                position: absolute;
                bottom: 7px;
                right: 9px;
                background: #dcb171;
                padding: 5px 6px;
                border-radius: 4px;
                font-size: 13px;
                line-height: 100%;
                color: #ffffff;
              }
              .cover-box {
                width: 100%;
                height: 100%;
                object-fit: cover;
                &.no-cover {
                  width: 30%;
                  height: auto;
                }
              }
              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }
      }
    }

    .preview-pdf {
      height: 850px;
    }
  }

  .file-detail-page {
    position: relative;
    padding-top: 0;
    padding-bottom: 80px;
    .file-name {
      color: #393939;
      font-size: 26px;
      font-weight: 700;
      border-bottom: 1px solid #DEDEDE;
      text-align: center;
      padding-bottom: 10px;
    }
    .file-base-info {
      padding: 8px 0;
      color: #262626B2;
      .copy-right {
        color: var(--el-color-primary);
      }
    }
    .action-buttons {
      right: 50px;
      bottom: 28px;
    }
    .file-preview-box {
      height: 644px;
      width: 900px;
      margin: 0 auto;
      &.is-audio {
        height: 300px;
      }
    }
    .file-info-list {
      width: 900px;
      margin: 20px auto 0 auto;
      color: #262626B2;
      .info-item {
        margin-top: 15px;
        display: flex;
        align-items: flex-start;
        .label {
          display: inline-block;
          width: 80px;
        }
        .value {
          flex: 1;
          &.keyword {
            color: var(--el-color-primary);
          }
        }
      }
    }
  }
}
</style>
