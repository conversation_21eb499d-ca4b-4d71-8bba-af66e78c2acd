<template>
  <div class="basic-info">
    <div class="info-content">
      <!-- 场景基本信息 -->
      <div class="info-section">
        <h3 class="section-title">场景信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <label class="info-label">场景名称：</label>
            <span class="info-value">{{ sceneInfo.name }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">状态：</label>
            <el-tag :type="sceneInfo.status === '1' ? 'success' : 'danger'" size="small">
              {{ sceneInfo.status === '1' ? '启用' : '禁用' }}
            </el-tag>
          </div>
          <div class="info-item">
            <label class="info-label">排序：</label>
            <span class="info-value">{{ sceneInfo.sort }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">创建时间：</label>
            <span class="info-value">{{ sceneInfo.createTime }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">更新时间：</label>
            <span class="info-value">{{ sceneInfo.updateTime }}</span>
          </div>
          <div class="info-item full-width">
            <label class="info-label">场景描述：</label>
            <span class="info-value">{{ sceneInfo.description || '暂无描述' }}</span>
          </div>
          <div class="info-item full-width">
            <label class="info-label">标签：</label>
            <div class="tag-list">
              <el-tag
                v-for="tag in sceneInfo.tags"
                :key="tag"
                size="small"
                class="tag-item"
              >
                {{ tag }}
              </el-tag>
              <span v-if="!sceneInfo.tags || sceneInfo.tags.length === 0" class="info-value">暂无标签</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 封面图片 -->
      <div class="info-section">
        <h3 class="section-title">封面图片</h3>
        <div class="cover-display">
          <img
            v-if="sceneInfo.coverImage"
            :src="sceneInfo.coverImage"
            alt="场景封面"
            class="cover-image"
          />
          <div v-else class="no-cover">
            <el-icon class="no-cover-icon"><Picture /></el-icon>
            <span>暂无封面图片</span>
          </div>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="info-section">
        <h3 class="section-title">统计信息</h3>
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon chapter-icon">
              <el-icon><Collection /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ sceneInfo.chapterCount || 0 }}</div>
              <div class="stat-label">章节数量</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon palace-icon">
              <el-icon><OfficeBuilding /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ sceneInfo.palaceCount || 0 }}</div>
              <div class="stat-label">宫观数量</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon node-icon">
              <el-icon><Location /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ sceneInfo.nodeCount || 0 }}</div>
              <div class="stat-label">信息节点</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 最近操作记录 -->
      <div class="info-section">
        <h3 class="section-title">最近操作记录</h3>
        <div class="operation-list">
          <div class="operation-item" v-for="operation in operationList" :key="operation.id">
            <div class="operation-time">{{ operation.time }}</div>
            <div class="operation-content">
              <span class="operation-user">{{ operation.user }}</span>
              <span class="operation-action">{{ operation.action }}</span>
            </div>
          </div>
          <el-empty v-if="operationList.length === 0" description="暂无操作记录" :image-size="80" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Picture, Collection, OfficeBuilding, Location } from '@element-plus/icons-vue';

interface Props {
  sceneInfo: any;
}

interface Emits {
  (e: 'refresh'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const operationList = ref<any[]>([]);

// 方法
const getOperationList = async () => {
  try {
    // TODO: 调用API获取操作记录
    // 模拟数据
    operationList.value = [
      {
        id: '1',
        time: '2023-12-01 15:30:00',
        user: '管理员',
        action: '更新了场景基本信息',
      },
      {
        id: '2',
        time: '2023-12-01 14:20:00',
        user: '编辑员',
        action: '添加了新的信息节点',
      },
      {
        id: '3',
        time: '2023-12-01 10:15:00',
        user: '管理员',
        action: '创建了场景',
      },
    ];
  } catch (err) {
    console.error('获取操作记录失败:', err);
  }
};

onMounted(() => {
  getOperationList();
});
</script>

<style scoped lang="scss">
.basic-info {
  height: 100%;
  overflow: auto;
}

.info-content {
  padding: 24px;
}

.info-section {
  margin-bottom: 32px;

  &:last-child {
    margin-bottom: 0;
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 2px solid #409eff;
    display: inline-block;
  }
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;

  .info-item {
    display: flex;
    align-items: flex-start;

    &.full-width {
      grid-column: 1 / -1;
    }

    .info-label {
      min-width: 100px;
      color: #606266;
      font-weight: 500;
      margin-right: 12px;
    }

    .info-value {
      color: #303133;
      flex: 1;
      word-break: break-all;
    }

    .tag-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .tag-item {
        margin: 0;
      }
    }
  }
}

.cover-display {
  .cover-image {
    max-width: 400px;
    max-height: 225px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .no-cover {
    width: 400px;
    height: 225px;
    border: 2px dashed #dcdfe6;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #909399;

    .no-cover-icon {
      font-size: 48px;
      margin-bottom: 12px;
    }
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;

  .stat-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 16px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    }

    .stat-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: white;

      &.chapter-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }

      &.palace-icon {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }

      &.node-icon {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }
    }

    .stat-content {
      .stat-number {
        font-size: 24px;
        font-weight: 700;
        color: #303133;
        line-height: 1;
        margin-bottom: 4px;
      }

      .stat-label {
        font-size: 14px;
        color: #606266;
      }
    }
  }
}

.operation-list {
  max-height: 300px;
  overflow-y: auto;

  .operation-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .operation-time {
      min-width: 150px;
      color: #909399;
      font-size: 13px;
    }

    .operation-content {
      flex: 1;
      font-size: 14px;

      .operation-user {
        color: #409eff;
        font-weight: 500;
        margin-right: 8px;
      }

      .operation-action {
        color: #606266;
      }
    }
  }
}
</style>
