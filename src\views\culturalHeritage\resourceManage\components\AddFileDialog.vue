<template>
  <el-dialog v-model="dialogVisible" title="新增关联文件" width="900px" :close-on-click-modal="false"
    :close-on-press-escape="false" destroy-on-close @close="handleClose" class="add-file-dialog">
    <div class="dialog-content">
      <div class="file-selector" v-loading="loading">
        <!-- 左侧文件树 -->
        <div class="file-tree-panel">
          <div class="panel-header">
            <div class="header-title">文件目录</div>
            <div class="search-box">
              <el-input v-model="searchKeyword" placeholder="搜索文件..." size="small" clearable @input="handleSearch">
                <template #prefix>
                  <el-icon>
                    <Search />
                  </el-icon>
                </template>
              </el-input>
            </div>
          </div>

          <div class="tree-container">
            <el-tree ref="fileTreeRef" :data="fileTreeData" :props="treeProps" :filter-node-method="filterNode"
              show-checkbox node-key="id" :default-expanded-keys="expandedKeys" @check="handleTreeCheck"
              :load="loadNode" lazy class="file-tree">
              <template #default="{ node, data }">
                <div class="tree-node">
                  <el-icon class="node-icon">
                    <Folder v-if="data.type === 'folder'" />
                    <Document v-else />
                  </el-icon>
                  <span class="node-label">{{ node.label }}</span>
                  <span v-if="data.type === 'file'" class="file-type">{{ getFileTypeLabel(data.fileTypeId) }}</span>
                  <el-icon v-if="node.loading" class="loading-icon">
                    <Loading />
                  </el-icon>
                </div>
              </template>
            </el-tree>
          </div>
        </div>

        <!-- 右侧已选文件列表 -->
        <div class="selected-files-panel">
          <div class="panel-header">
            <div class="header-title">已选择文件 ({{ selectedFiles.length }})</div>
            <el-button type="danger" size="small" :disabled="selectedFiles.length === 0" @click="clearAllSelected">
              清空全部
            </el-button>
          </div>

          <div class="selected-list">
            <div v-if="selectedFiles.length === 0" class="empty-state">
              <el-empty description="请从左侧选择要关联的文件" />
            </div>

            <div v-else class="file-list">
              <div v-for="file in selectedFiles" :key="file.id" class="selected-file-item">
                <div class="file-info">
                  <el-icon class="file-icon">
                    <Document />
                  </el-icon>
                  <div class="file-details">
                    <div class="file-name">{{ file.name }}</div>
                    <div class="file-meta">
                      <span class="file-size">{{ formatFileSize(file.size) }}</span>
                      <span class="file-type">{{ getFileTypeLabel(file.type) }}</span>
                    </div>
                  </div>
                </div>
                <el-button type="danger" link size="small" @click="removeSelectedFile(file.id)">
                  <el-icon>
                    <Close />
                  </el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <div class="footer-info">
          已选择 {{ selectedFiles.length }} 个文件
        </div>
        <div class="footer-actions">
          <el-button @click="handleClose" class="cancel-btn">取消</el-button>
          <el-button type="primary" @click="handleConfirm" :disabled="selectedFiles.length === 0" class="confirm-btn">
            确认关联
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useMessage } from '/@/hooks/message';
import { getFolderList } from '/@/api/resource/resourceFile';
import { updateResourceOfFile } from '/@/api/resource/data/resource';
import { getFileType } from '/@/config/resourceConfig';
import { Search, Folder, Document, Close, Loading } from '@element-plus/icons-vue';

interface Props {
  visible: boolean;
  editData?: any;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  editData: null,
});

const emit = defineEmits<{
  'update:visible': [value: boolean];
  confirm: [data: any[]];
}>();

// 响应式数据
const fileTreeRef = ref();
const loading = ref(false);
const searchKeyword = ref('');
const selectedFiles = ref<any[]>([]);
const fileTreeData = ref<any[]>([]);
const expandedKeys = ref<string[]>([]);

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

// 树形组件配置
const treeProps = {
  children: 'children',
  label: (data: any) => {
    // 文件夹显示 catalogName，文件显示 name
    return data.type === 'folder' ? data.catalogName : data.name;
  },
  disabled: (data: any) => data.type === 'folder', // 文件夹不可选择
  isLeaf: (data: any) => data.type === 'file', // 文件节点为叶子节点
};

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    selectedFiles.value = [];
    // 懒加载模式下不需要预加载数据，树组件会自动调用 loadNode
  }
});

// 懒加载节点数据
const loadNode = async (node: any, resolve: any) => {
  try {
    // 确定父级ID：根节点使用0，子节点使用节点ID
    const parentId = node.level === 0 ? 0 : (node.data.id || node.data.catalogNo);

    const response = await getFolderList({
      catalogParentId: parentId,
      type: '',
      fileName: '',
      createBy: '',
    });

    if ((response.code === 0 || response.code === 200) && response.data) {
      const childNodes: any[] = [];

      // 添加文件夹节点
      if (response.data.folders && response.data.folders.length > 0) {
        response.data.folders.forEach((folder: any) => {
          childNodes.push({
            id: folder.id,
            catalogName: folder.catalogName, // 文件夹使用 catalogName
            type: 'folder',
            level: node.level + 1,
            catalogNo: folder.catalogNo || folder.id,
            hasChildren: true, // 假设文件夹可能有子项
          });
        });
      }

      // 添加文件节点
      if (response.data.files && response.data.files.length > 0) {
        response.data.files.forEach((file: any) => {
          childNodes.push({
            id: file.id,
            name: file.name, // 文件使用 name
            type: 'file',
            size: file.totalSize, // 文件大小使用 totalSize
            url: file.downloadUrl, // 下载链接使用 downloadUrl
            fileTypeId: file.type, // 使用 API 返回的 type 字段作为文件类型ID
            level: node.level + 1,
          });
        });
      }

      resolve(childNodes);
    } else {
      resolve([]);
    }
  } catch (error) {
    useMessage().error('加载文件数据失败');
    resolve([]);
  }
};



// 处理树节点勾选
const handleTreeCheck = (_data: any, checked: any) => {
  const { checkedNodes } = checked;

  // 只保留文件节点，过滤掉文件夹
  const fileNodes = checkedNodes.filter((node: any) => node.type === 'file');

  selectedFiles.value = fileNodes.map((node: any) => ({
    id: node.id,
    name: node.name,
    size: node.size,
    url: node.url,
    type: node.fileTypeId,
    file_name: node.name,
    file_url: node.url,
    file_size: node.size,
    file_type: node.fileTypeId,
    file_remark: '',
  }));
};

// 搜索文件
const handleSearch = (keyword: string) => {
  fileTreeRef.value?.filter(keyword);
};

// 过滤节点
const filterNode = (value: string, data: any) => {
  if (!value) return true;
  // 文件夹使用 catalogName，文件使用 name
  const displayName = data.type === 'folder' ? data.catalogName : data.name;
  return displayName && displayName.toLowerCase().includes(value.toLowerCase());
};

// 移除已选文件
const removeSelectedFile = (fileId: string) => {
  selectedFiles.value = selectedFiles.value.filter(file => file.id !== fileId);

  // 同时取消树节点的勾选状态
  const checkedKeys = fileTreeRef.value?.getCheckedKeys() || [];
  const newCheckedKeys = checkedKeys.filter((key: string) => key !== fileId);
  fileTreeRef.value?.setCheckedKeys(newCheckedKeys);
};

// 清空所有已选文件
const clearAllSelected = () => {
  selectedFiles.value = [];
  fileTreeRef.value?.setCheckedKeys([]);
};

// 格式化文件大小
const formatFileSize = (size: number) => {
  if (!size) return '--';
  const units = ['B', 'KB', 'MB', 'GB'];
  let index = 0;
  let fileSize = size;

  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024;
    index++;
  }

  return `${fileSize.toFixed(1)} ${units[index]}`;
};

// 获取文件类型标签
const getFileTypeLabel = (typeId: string) => {
  if (!typeId) return '其他文件';

  const fileTypeConfig = getFileType(typeId);
  return fileTypeConfig ? fileTypeConfig.name : '其他文件';
};

// 确认关联
const handleConfirm = async () => {
  if (selectedFiles.value.length === 0) {
    useMessage().wraning('请选择要关联的文件');
    return;
  }

  try {
    // 构建接口参数
    const requestData = selectedFiles.value.map(file => ({
      dataId: props.editData?.id || '',
      materialId: file.id,
      tabName: props.editData?.tableName || '',
    }));

    // 调用接口
    const response = await updateResourceOfFile(requestData);

    if ((response.code === 0 || response.code === 200)) {
      useMessage().success('文件关联成功');
      emit('confirm', selectedFiles.value);
      handleClose();
    } else {
      useMessage().error(response.message || '文件关联失败');
    }
  } catch (error) {
    useMessage().error('文件关联失败');
  }
};

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false;
  selectedFiles.value = [];
  searchKeyword.value = '';
};
</script>

<style lang="scss">
.add-file-dialog {
  .el-dialog {
    border-radius: 8px;

    .el-dialog__header {
      border-bottom: 1px solid #e4e7ed;
      background: linear-gradient(135deg, #f5f7fa 0%, #ffffff 100%);

      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }
    }

    .el-dialog__body {
      padding: 20px 24px;
    }

    .el-dialog__footer {
      border-top: 1px solid #e4e7ed;
      background: linear-gradient(135deg, #f5f7fa 0%, #ffffff 100%);
    }
  }
}

.dialog-content {
  .file-selector {
    display: flex;
    height: 500px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    overflow: hidden;
  }

  // 左侧文件树面板
  .file-tree-panel {
    width: 50%;
    border-right: 1px solid #e4e7ed;
    display: flex;
    flex-direction: column;

    .panel-header {
      padding: 16px;
      border-bottom: 1px solid #e4e7ed;
      background-color: #fafafa;

      .header-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 12px;
      }

      .search-box {
        width: 100%;
      }
    }

    .tree-container {
      flex: 1;
      padding: 12px;
      overflow-y: auto;

      .file-tree {
        :deep(.el-tree-node__content) {
          height: 32px;

          &:hover {
            background-color: #f5f7fa;
          }
        }

        .tree-node {
          display: flex;
          align-items: center;
          flex: 1;
          padding-right: 8px;

          .node-icon {
            margin-right: 6px;
            color: #606266;
          }

          .node-label {
            flex: 1;
            font-size: 14px;
            color: #303133;
          }

          .file-type {
            font-size: 12px;
            color: #909399;
            background-color: #f0f2f5;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 500;
          }

          .loading-icon {
            margin-left: 8px;
            color: #409eff;
            animation: rotate 1s linear infinite;
          }
        }
      }
    }
  }

  // 右侧已选文件面板
  .selected-files-panel {
    width: 50%;
    display: flex;
    flex-direction: column;

    .panel-header {
      padding: 16px;
      border-bottom: 1px solid #e4e7ed;
      background-color: #fafafa;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }

    .selected-list {
      flex: 1;
      overflow-y: auto;

      .empty-state {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .file-list {
        padding: 12px;

        .selected-file-item {
          display: flex;
          align-items: center;
          padding: 12px;
          margin-bottom: 8px;
          background-color: #f8f9fa;
          border-radius: 6px;
          border: 1px solid #e9ecef;
          transition: all 0.3s;

          &:hover {
            background-color: #e9ecef;
            border-color: #dee2e6;
          }

          &:last-child {
            margin-bottom: 0;
          }

          .file-info {
            display: flex;
            align-items: center;
            flex: 1;

            .file-icon {
              color: #409eff;
              margin-right: 12px;
              font-size: 18px;
            }

            .file-details {
              flex: 1;

              .file-name {
                font-size: 14px;
                color: #303133;
                font-weight: 500;
                margin-bottom: 4px;
                word-break: break-all;
              }

              .file-meta {
                display: flex;
                gap: 12px;

                .file-size,
                .file-type {
                  font-size: 12px;
                  color: #909399;
                }

                .file-type {
                  &::before {
                    content: '•';
                    margin-right: 4px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .footer-info {
    font-size: 14px;
    color: #606266;
  }

  .footer-actions {
    display: flex;
    gap: 12px;

    .cancel-btn,
    .confirm-btn {
      padding: 10px 20px;
      border-radius: 6px;
      font-weight: 500;
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
</style>
