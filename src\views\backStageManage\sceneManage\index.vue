<template>
  <div class="scene-manage">
    

    <!-- 新增/编辑场景弹窗 -->
    <AddSceneDialog v-model:visible="showAddDialog" :edit-data="editData" @confirm="handleSceneConfirm" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineAsyncComponent } from 'vue';
import { useRouter } from 'vue-router';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { getSceneList, delScene, updateSceneStatus } from '/@/api/backStageManage/scene';

// 异步组件
const AddSceneDialog = defineAsyncComponent(() => import('./components/AddSceneDialog.vue'));

const router = useRouter();
const { success, error } = useMessage();
const { confirm } = useMessageBox();

// 响应式数据
const showAddDialog = ref(false);
const editData = ref<any>(null);
const sceneList = ref<any[]>([]);
const total = ref(0);

const searchForm = reactive({
  sceneName: '',
  status: '',
});

const pagination = reactive({
  current: 1,
  size: 12,
});

// 方法
const handleSearch = () => {
  pagination.current = 1;
  getSceneList();
};

const handleReset = () => {
  searchForm.sceneName = '';
  searchForm.status = '';
  pagination.current = 1;
  getSceneList();
};

const handleSizeChange = (size: number) => {
  pagination.size = size;
  pagination.current = 1;
  getSceneList();
};

const handleCurrentChange = (current: number) => {
  pagination.current = current;
  getSceneList();
};

const goToDetail = (id: string) => {
  router.push(`/backStageManage/sceneManage/detail/${id}`);
};

const goToConfig = (id: string) => {
  router.push(`/backStageManage/sceneConfig/${id}`);
};

const editScene = (scene: any) => {
  editData.value = { ...scene };
  showAddDialog.value = true;
};

const deleteScene = async (scene: any) => {
  try {
    await confirm(`确认删除场景"${scene.sceneName}"吗？删除后不可恢复！`);
    await delScene(scene.id);
    success('删除成功');
    getSceneList();
  } catch {
    // 用户取消删除
  }
};

const handleSceneConfirm = () => {
  showAddDialog.value = false;
  editData.value = null;
  getSceneList();
};

const getSceneList = async () => {
  try {
    sceneList.value = mockData;
    total.value = mockData.length;
  } catch (err) {
    error('获取场景列表失败');
  }
};

onMounted(() => {
  getSceneList();
});
</script>

<style scoped lang="scss">
.scene-manage {
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
