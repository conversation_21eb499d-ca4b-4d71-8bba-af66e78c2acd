<template>
  <div class="scene-manage">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <div class="tab-buttons">
          <el-button type="primary" class="tab-active" @click="addScene">新增场景</el-button>
        </div>
      </div>
      <div class="header-right">
        <!-- <el-select v-model="searchForm.status" placeholder="场景状态" clearable
          style="width: 120px; margin-right: 10px;" @change="handleSearch">
          <el-option label="草稿" value="1" />
          <el-option label="已发布" value="2" />
        </el-select> -->
        <el-input
          v-model="searchForm.sceneName"
          placeholder="请输入场景名称"
          clearable
          style="width: 250px"
          @keyup.enter="handleSearch"
          @clear="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 场景卡片列表 -->
    <div class="scene-list" v-loading="loading">
      <el-row :gutter="24">
        <el-col :span="8" v-for="scene in sceneList" :key="scene.id">
          <div class="scene-card">
            <!-- 场景封面 -->
            <div class="scene-cover">
              <img v-if="scene.cover"
                :src="scene.cover"
                alt="场景封面"
              />
              <div class="no-cover-img" v-else>暂无封面</div>
              <div class="cover-status">
                <div
                  :class="'status-tag status-tag-' + scene.status"
                >
                  {{ scene.status === 1 ? '草稿' : '已发布' }}
                </div>
              </div>
            </div>

            <!-- 场景信息 -->
            <div class="scene-info">
              <h3 class="scene-title">{{ scene.sceneName }}</h3>
              <div :class="'scene-tag scene-tag-' + scene.sceneType">{{ scene.sceneType === 1 ? '主线场景' : '专题场景' }}</div>
              <p class="scene-desc">{{ scene.description || '暂无描述' }}</p>

              <!-- 操作按钮 -->
              <div class="scene-actions">
                <span>
                  <el-button type="primary" link size="small" @click="editScene(scene)" :disabled="scene.status == 2">
                    编辑
                  </el-button>
                  <el-button type="primary" link size="small" @click="goToConfig(scene.sceneId)">
                    详情
                  </el-button>
                </span>
                <el-dropdown @command="(command: any) => handleDropdownCommand(command, scene)">
                  <el-button type="primary" link size="small">
                    <el-icon class="more-icon"><MoreFilled /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="publish">{{ scene.status === 1 ? '发布' : '取消发布' }}</el-dropdown-item>
                      <el-dropdown-item command="copy">复制</el-dropdown-item>
                      <el-dropdown-item command="delete" class="danger-item">删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 空状态 -->
      <el-empty v-if="sceneList.length === 0 && !loading" description="暂无数据">
      </el-empty>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper" v-if="total > 0">
      <el-pagination
        background
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :page-sizes="[6, 9, 18, 36, 72, 120]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑场景弹窗 -->
    <AddSceneDialog v-model:visible="showAddDialog" :edit-data="editData" @confirm="handleSceneConfirm" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineAsyncComponent } from 'vue';
import { useRouter } from 'vue-router';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { getSceneList as getSceneListApi, delScene, updateSceneStatus } from '/@/api/backStageManage/scene';
import { Search, MoreFilled } from '@element-plus/icons-vue';

// 异步组件
const AddSceneDialog = defineAsyncComponent(() => import('./components/AddSceneDialog.vue'));

const router = useRouter();
const { success, error } = useMessage();
const { confirm } = useMessageBox();

// 响应式数据
const loading = ref(false);
const showAddDialog = ref(false);
const editData = ref<any>(null);
const sceneList = ref<any[]>([]);
const total = ref(0);

const searchForm = reactive({
  sceneName: '',
  status: '',
});

const pagination = reactive({
  current: 1,
  size: 9,
});

// 方法
const handleSearch = () => {
  pagination.current = 1;
  getSceneList();
};

const handleSizeChange = (size: number) => {
  pagination.size = size;
  pagination.current = 1;
  getSceneList();
};

const handleCurrentChange = (current: number) => {
  pagination.current = current;
  getSceneList();
};

const goToConfig = (sceneId: string) => {
  router.push(`/backStageManage/sceneConfig/${sceneId}`);
};

const addScene = () => {
  editData.value = null;
  showAddDialog.value = true;
};

const editScene = (scene: any) => {
  editData.value = { ...scene };
  showAddDialog.value = true;
};

const deleteScene = async (scene: any) => {
  try {
    await confirm(`确认删除场景"${scene.sceneName}"吗？删除后不可恢复！`);
  } catch {
    // 用户取消删除
    return;
  }
  try {
    await delScene(scene.sceneId);
    success('删除成功');
    getSceneList();
  } catch (err: any) {
    error(err?.msg || '删除失败');
  }
};

// 发布/取消发布场景
const publishScene = async (scene: any) => {
  try {
    await confirm(`确认${scene.status === 1 ? '发布' : '取消发布'}场景"${scene.sceneName}"吗？`);
  } catch {
    // 用户取消操作
    return;
  }
  try {
    await updateSceneStatus({
      sceneId: scene.sceneId,
      status: scene.status === 1 ? 2 : 1,
    });
    success(`${scene.status === 1 ? '发布' : '取消发布'}成功`);
    getSceneList();
  } catch (err: any) {
    error(err?.msg || '操作失败');
  }
};

const handleDropdownCommand = (command: string, scene: any) => {
  switch (command) {
    case 'publish':
      publishScene(scene);
      break;
    case 'copy':
      copyScene(scene);
      break;
    case 'delete':
      deleteScene(scene);
      break;
  }
};

const copyScene = (scene: any) => {
  editData.value = {
    ...scene,
    sceneId: undefined,
    sceneName: `${scene.sceneName}_副本`
  };
  showAddDialog.value = true;
};

const handleSceneConfirm = () => {
  showAddDialog.value = false;
  editData.value = null;
  getSceneList();
};

const getSceneList = async () => {
  try {
    loading.value = true;
    const params = {
      current: pagination.current,
      size: pagination.size,
      sceneName: searchForm.sceneName || undefined,
      status: searchForm.status || undefined,
    };
    const response = await getSceneListApi(params);
    sceneList.value = response.data?.records || [];
    total.value = response.data?.total || 0;
  } catch (err) {
    error('获取场景列表失败');
    sceneList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  getSceneList();
});
</script>

<style scoped lang="scss">
.scene-manage {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  background-color: #fff;
  border-radius: 6px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;

  .header-left {
    .tab-buttons {
      display: flex;
      gap: 8px;

      .tab-active {
        background-color: var(--el-color-primary);
        border-color: var(--el-color-primary);
        color: white;
      }

      .tab-normal {
        background-color: white;
        border-color: #dcdfe6;
        color: #606266;

        &:hover {
          color: var(--el-color-primary);
          border-color: var(--el-color-primary);
        }
      }
    }
  }
}

.scene-list {
  padding: 10px;
  flex: 1;
  margin-bottom: 20px;
  overflow-y: auto;
  overflow-x: hidden;

  .el-row {
    margin-bottom: 24px;
  }
}

.scene-card {
  background: white;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  margin-bottom: 24px;
  padding: 18px 20px;
  cursor: pointer;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }

  .scene-cover {
    position: relative;
    height: 200px;
    overflow: hidden;
    border-radius: 5px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }
             
    .no-cover-img {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #b3b4b4;
      border: 1px solid #f2f2f2;
      border-radius: 5px;
    }

    &:hover img {
      transform: scale(1.05);
    }

    .cover-status {
      position: absolute;
      top: 12px;
      right: 12px;

      .status-tag {
        background: #2BA471;
        color: rgba(255, 255, 255, 0.90);
        border-radius: 3px;
        padding: 2px 8px;
      }

      .status-tag-1 {
        background: #F6685D;
      }
    }
  }

  .scene-info {
    margin-top: 10px;

    .scene-title {
      font-size: 16px;
      color: #303133;
      line-height: 1.4;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .scene-tag {
      border-radius: 3px;
      padding: 2px 8px;
      background: #92DAB2;
      color: #008858;
      display: inline-block;
      margin: 8px 0;
    }

    .scene-tag-1 {
      background: #B5C7FF;
      color: #0052D9;
    }

    .scene-desc {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.5);
      margin: 0 0 16px 0;
      line-height: 1.5;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      height: 38px;
      -webkit-box-orient: vertical;
    }

    .scene-actions {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .el-button {
        padding: 0;
        font-size: 14px;

        &:hover {
          color: var(--el-color-primary-light-3);
        }
      }

      .more-icon {
        transform: rotate(90deg);
        color: #333;
      }
    }
  }
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
}

// 下拉菜单危险项样式
:deep(.danger-item) {
  color: #f56c6c !important;

  &:hover {
    background-color: #fef0f0 !important;
    color: #f56c6c !important;
  }
}

// 空状态样式
.el-empty {
  padding: 60px 0;
}
</style>
