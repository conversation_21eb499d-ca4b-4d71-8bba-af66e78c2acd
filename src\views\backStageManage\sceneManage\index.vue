<template>
  <div class="scene-manage">
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">场景管理</h2>
        <p class="page-desc">管理全景视频场景，包括章节、宫观和信息节点的配置</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showAddDialog = true">
          <el-icon>
            <Plus />
          </el-icon>
          新增场景
        </el-button>
      </div>
    </div>

    <div class="content-area">
      <!-- 搜索筛选区域 -->
      <div class="search-section">
        <el-form :model="searchForm" inline class="search-form">
          <el-form-item label="场景名称">
            <el-input v-model="searchForm.name" placeholder="请输入场景名称" clearable style="width: 200px" />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 120px">
              <el-option label="启用" value="1" />
              <el-option label="禁用" value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon>
                <Search />
              </el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 场景列表 -->
      <div class="scene-list">
        <el-row :gutter="24">
          <el-col :span="8" v-for="scene in sceneList" :key="scene.id">
            <div class="scene-card">
              <div class="card-header">
                <div class="scene-cover">
                  <img :src="scene.coverImage || '/src/assets/default-scene.jpg'" alt="场景封面" />
                  <div class="cover-overlay">
                    <el-button type="primary" size="small" @click="goToDetail(scene.id)">
                      查看详情
                    </el-button>
                  </div>
                </div>
              </div>
              <div class="card-body">
                <h3 class="scene-title">{{ scene.name }}</h3>
                <p class="scene-desc">{{ scene.description || '暂无描述' }}</p>
                <div class="scene-stats">
                  <span class="stat-item">
                    <el-icon>
                      <Collection />
                    </el-icon>
                    章节: {{ scene.chapterCount || 0 }}
                  </span>
                  <span class="stat-item">
                    <el-icon>
                      <OfficeBuilding />
                    </el-icon>
                    宫观: {{ scene.palaceCount || 0 }}
                  </span>
                  <span class="stat-item">
                    <el-icon>
                      <Location />
                    </el-icon>
                    节点: {{ scene.nodeCount || 0 }}
                  </span>
                </div>
              </div>
              <div class="card-footer">
                <div class="footer-left">
                  <el-tag :type="scene.status === '1' ? 'success' : 'danger'" size="small">
                    {{ scene.status === '1' ? '启用' : '禁用' }}
                  </el-tag>
                </div>
                <div class="footer-right">
                  <el-button type="primary" link size="small" @click="goToConfig(scene.id)">
                    场景配置
                  </el-button>
                  <el-button type="primary" link size="small" @click="editScene(scene)">
                    编辑
                  </el-button>
                  <el-button type="danger" link size="small" @click="deleteScene(scene)">
                    删除
                  </el-button>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 空状态 -->
        <el-empty v-if="sceneList.length === 0" description="暂无场景数据">
          <el-button type="primary" @click="showAddDialog = true">创建第一个场景</el-button>
        </el-empty>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper" v-if="total > 0">
        <el-pagination v-model:current-page="pagination.current" v-model:page-size="pagination.size"
          :page-sizes="[12, 24, 48]" :total="total" layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </div>

    <!-- 新增/编辑场景弹窗 -->
    <AddSceneDialog v-model:visible="showAddDialog" :edit-data="editData" @confirm="handleSceneConfirm" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineAsyncComponent } from 'vue';
import { useRouter } from 'vue-router';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { Plus, Search, Collection, OfficeBuilding, Location } from '@element-plus/icons-vue';

// 异步组件
const AddSceneDialog = defineAsyncComponent(() => import('./components/AddSceneDialog.vue'));

const router = useRouter();
const { success, error } = useMessage();
const { confirm } = useMessageBox();

// 响应式数据
const showAddDialog = ref(false);
const editData = ref<any>(null);
const sceneList = ref<any[]>([]);
const total = ref(0);

const searchForm = reactive({
  name: '',
  status: '',
});

const pagination = reactive({
  current: 1,
  size: 12,
});

// 方法
const handleSearch = () => {
  pagination.current = 1;
  getSceneList();
};

const handleReset = () => {
  searchForm.name = '';
  searchForm.status = '';
  pagination.current = 1;
  getSceneList();
};

const handleSizeChange = (size: number) => {
  pagination.size = size;
  pagination.current = 1;
  getSceneList();
};

const handleCurrentChange = (current: number) => {
  pagination.current = current;
  getSceneList();
};

const goToDetail = (id: string) => {
  router.push(`/backStageManage/sceneManage/detail/${id}`);
};

const goToConfig = (id: string) => {
  router.push(`/backStageManage/sceneConfig/${id}`);
};

const editScene = (scene: any) => {
  editData.value = { ...scene };
  showAddDialog.value = true;
};

const deleteScene = async (scene: any) => {
  try {
    await confirm(`确认删除场景"${scene.name}"吗？删除后不可恢复！`);
    // TODO: 调用删除API
    success('删除成功');
    getSceneList();
  } catch {
    // 用户取消删除
  }
};

const handleSceneConfirm = () => {
  showAddDialog.value = false;
  editData.value = null;
  getSceneList();
};

const getSceneList = async () => {
  try {
    // TODO: 调用API获取场景列表
    // 模拟数据
    const mockData = [
      {
        id: '1',
        name: '武当山主峰天柱峰',
        description: '武当山最高峰，海拔1612米，是道教圣地的核心区域',
        coverImage: '',
        status: '1',
        chapterCount: 3,
        palaceCount: 8,
        nodeCount: 25,
        createTime: '2023-12-01 10:00:00',
      },
      {
        id: '2',
        name: '紫霄宫景区',
        description: '武当山著名宫观建筑群，明代皇家建筑的典型代表',
        coverImage: '',
        status: '1',
        chapterCount: 2,
        palaceCount: 5,
        nodeCount: 18,
        createTime: '2023-12-02 14:30:00',
      },
    ];

    sceneList.value = mockData;
    total.value = mockData.length;
  } catch (err) {
    error('获取场景列表失败');
  }
};

onMounted(() => {
  getSceneList();
});
</script>

<style scoped lang="scss">
.scene-manage {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  .header-left {
    .page-title {
      font-size: 24px;
      font-weight: 600;
      color: #303133;
      margin: 0 0 8px 0;
    }

    .page-desc {
      color: #909399;
      margin: 0;
      font-size: 14px;
    }
  }

  .header-right {
    .el-button {
      font-size: 14px;
    }
  }
}

.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.search-section {
  background: white;
  padding: 20px 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  .search-form {
    .el-form-item {
      margin-bottom: 0;
    }
  }
}

.scene-list {
  flex: 1;
  margin-bottom: 16px;
}

.scene-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  margin-bottom: 24px;
  height: 420px;
  display: flex;
  flex-direction: column;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  }

  .card-header {
    .scene-cover {
      position: relative;
      height: 220px;
      overflow: hidden;
      background: #f5f7fa;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }

      .cover-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.6);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover {
        img {
          transform: scale(1.05);
        }

        .cover-overlay {
          opacity: 1;
        }
      }
    }
  }

  .card-body {
    flex: 1;
    padding: 16px;

    .scene-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin: 0 0 8px 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .scene-desc {
      color: #606266;
      font-size: 13px;
      line-height: 1.5;
      margin: 0 0 12px 0;
      height: 40px;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .scene-stats {
      display: flex;
      flex-direction: column;
      gap: 6px;

      .stat-item {
        display: flex;
        align-items: center;
        font-size: 12px;
        color: #909399;

        .el-icon {
          margin-right: 4px;
          font-size: 14px;
        }
      }
    }
  }

  .card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-top: 1px solid #f0f0f0;
    background: #fafafa;

    .footer-right {
      display: flex;
      gap: 8px;

      .el-button {
        padding: 4px 8px;
        font-size: 12px;
      }
    }
  }
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding: 20px 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
</style>
