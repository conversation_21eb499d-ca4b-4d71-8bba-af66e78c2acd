<template>
	<div class="layout-padding">
		<div class="analysis-box" v-show="!isShowDetail">
			<div class="analysis-left">
				<div class="chat-btn-item add-item" @click="addNewDialogue"><span style="margin-right: 15px; font-size: 16px">+</span>创建新对话</div>
				<div class="chat-item-list">
					<div
						v-for="item in chatList"
						:key="item.sessionid"
						class="chat-btn-item dialogue-item"
						:title="item.name"
						:class="{ active: item.sessionid === activeChatId }"
						@click="switchDialogue(item)"
					>
						<span>{{ item.name }}</span>
						<el-icon class="delete-icon" @click.stop="deleteDialogue(item)"><Delete /></el-icon>
					</div>
				</div>
			</div>
			<div class="analysis-right">
				<div class="question-home">
					<div class="chat-list-box" ref="chatBoxRef">
						<div v-for="(item, index) of chatHistoryList" :key="index" class="chat-item" :class="item.isBot ? 'chat-item-left' : 'chat-item-right'">
							<img v-if="item.isBot" class="user-image" src="/@/assets/img/archives/avatar_ai.png" />
							<img v-else class="user-image" src="/@/assets/img/archives/avatar_user.png" />
							<div className="user-content">
								<div v-if="item.isLoading">
									<span>请等待回答...</span>
									<img class="answer-loading" src="/@/assets/img/archives/icon_loading.png" />
								</div>
								<div v-if="!item.isBot" class="user-content-md" v-html="item.content"></div>
								<div v-else class="user-content-md" v-html="marked(item.content || '')"></div>
								<template v-if="item.fileList?.length">
									<div v-for="file in item.fileList" :key="file.business_id" class="file-item" @click="onOpenFileDetail(file)">
										<span>【{{ getFileTypeName(file.file_type || file.type) }}】</span>
										<span>{{ file.assets_name || file.name }}</span>
									</div>
								</template>
								<div class="file-tip" v-if="!item.isBot && item.fileUrl">
									<el-image :src="item.fileUrl" fit="cover" :preview-src-list="[item.fileUrl]" />
								</div>
							</div>
						</div>
						<el-empty style="margin-top: 100px" v-if="!chatHistoryList.length" description="暂无对话记录" />
					</div>
					<div class="chat-input-box">
						<div class="ai-search-toolbar">
							<div class="ai-search-file flex items-center">
								<el-upload
									ref="uploadPicRef"
									:auto-upload="false"
									:multiple="false"
									:limit="1"
									:on-change="handleFileChange"
									:show-file-list="false"
									:accept="'.png,.jpg,.jpeg'"
								>
									<div class="upload-pic-btn flex items-center">
										<img src="/@/assets/img/archives/icon_ai_upload.png" />
									</div>
								</el-upload>
								<div
									class="upload-pic-btn ml-2 flex items-center"
									:class="{ active: isAiSearchResource }"
									@click="isAiSearchResource = !isAiSearchResource"
								>
									<img src="/@/assets/img/archives/icon_file_search.png" />
									<span class="ml-2">资源检索</span>
								</div>
								<div v-if="selectFile?.uid" class="upload-pic-btn ml-2 flex items-center active cursor-default">
									<span>已选附件：{{ selectFile?.name }}</span>
									<el-icon class="ml-2 cursor-pointer" @click="selectFile = null"><delete /></el-icon>
								</div>
							</div>
						</div>
						<el-input
							:disabled="isAnswerLoading"
							v-model="inputValue"
							placeholder="请输入提问内容"
							:rows="3"
							type="textarea"
							resize="none"
							@keyup.enter.native.stop="onSendQuestion()"
							maxlength="5000"
							show-word-limit
						/>
						<el-button :disabled="isAnswerLoading" type="primary" class="btn-send" @click="onSendQuestion()">
							<img class="icon-send" src="/@/assets/img/archives/icon_send.png" />
						</el-button>
					</div>
				</div>
			</div>
		</div>
		<FileDetail ref="fileDetailRef" v-if="isShowDetail" @close="isShowDetail = false" />
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useResuorceSearchStore } from '/@/stores/resourceSearch';
import { fileTypeConfig } from '/@/config/resourceConfig';
import { ElMessage } from 'element-plus';
import { aiSearch, saveConversationData, queryConversationList, queryConversationHistory, deleteConversation } from '/@/api/resource/resourceFile';
import FileDetail from '/@/views/resourceManagement/resourceFile/detail/FileDetail.vue';
import { generateUUID } from "/@/utils/other";
import { marked } from "marked";

const fileDetailRef = ref();
const isShowDetail = ref<boolean>(false);
const resourceSearchStore = useResuorceSearchStore();
const chatBoxRef = ref<any>({});
const inputValue = ref(''); // 对话框
const isAnswerLoading = ref(false);
const activeChatId = ref('');
const chatList = ref<any>([]);
const chatHistoryList = ref(<any>[]);
const selectFile = ref<any>();
const uploadPicRef = ref();
const isAiSearchResource = ref(false);

onMounted(() => {
	activeChatId.value = generateUUID();
	getChatList();
	// 若从AI检索页跳转过来，带入信息
	if (resourceSearchStore.aiSearchConfig.aiQuestion) {
		inputValue.value = resourceSearchStore.aiSearchConfig.aiQuestion;
		isAiSearchResource.value = resourceSearchStore.aiSearchConfig.isSearchFile;
		selectFile.value = resourceSearchStore.aiSearchConfig.aiFile;
		onSendQuestion();
	}
});

onBeforeUnmount(() => {
	// Revoke any object URLs to prevent memory leaks
	revokeObjectURLs();
	// 关闭智能分析页面时重置全局搜索参数
	resourceSearchStore.setAiSearchConfig({
		aiQuestion: '',
		aiFile: null,
		isSearchFile: false,
	});
});

// Add this to your component's methods
const revokeObjectURLs = () => {
	chatHistoryList.value.forEach((item: any) => {
		if (item.fileUrl && item.fileUrl.startsWith('blob:')) {
			URL.revokeObjectURL(item.fileUrl);
		}
	});
};

// 创建新对话，跳转到首页
const addNewDialogue = () => {
	activeChatId.value = generateUUID();
	inputValue.value = '';
	chatHistoryList.value = [];
};

// 切换对话
const switchDialogue = (item: any) => {
	revokeObjectURLs();
	activeChatId.value = item.sessionid;
	isAnswerLoading.value = false;
	chatHistoryList.value = [];
	getChatHistory();
};

const getFileTypeName = (type: string) => {
	return fileTypeConfig.find((item: any) => item.id == type)?.name || '未知类型';
};

const scrollToBottom = () => {
	nextTick(() => {
		chatBoxRef.value.scrollTo({ top: chatBoxRef.value.scrollHeight, behavior: 'smooth' });
	});
};
// 发送问题
const onSendQuestion = () => {
	if (!inputValue.value.trim()) {
		return useMessage().wraning('请输入您的问题');
	}
	const isNew = chatHistoryList.value.length == 0;
	chatHistoryList.value.push(
		{
			isBot: false,
			content: inputValue.value,
			file: selectFile.value?.uid ? selectFile.value : null,
			fileUrl: selectFile.value?.uid ? URL.createObjectURL(selectFile.value) : null,
		},
		{
			isBot: true,
			isLoading: true,
			content: '',
			fileList: [],
		}
	);
	isAnswerLoading.value = true;
	scrollToBottom();
	let question = inputValue.value;
	inputValue.value = '';
	// 接口方式
	const data = new FormData();
	data.append('keyword', question);
	data.append('askType', isAiSearchResource.value ? '0' : '1');
	if (selectFile.value?.uid) {
		data.append('file', selectFile.value);
	}
	aiSearch(data).then(res => {
	  if (res?.data) {
		let isFileArray = Array.isArray(res.data.data);
		let answerContent = isFileArray ? (res.data.data?.length ? '' : '暂未找到相关资源。') : (res.data.data || '暂时无法回答您的问题。');
	    chatHistoryList.value.pop();
	    chatHistoryList.value.push({
	      isBot: true,
		  isLoading: false,
	      content: answerContent,
		  fileList: isFileArray ? (res.data.data || []) : [],
	    });
	    setTimeout(() => {
	      chatBoxRef.value.scrollTo({ top: chatBoxRef.value.scrollHeight, behavior: 'smooth' })
	    }, 200);
		// 保存对话记录
		let name = '';
		if (!isNew) {
			name = chatList.value.find((item: any) => item.sessionid === activeChatId.value)?.name;
		} else {
			name = question;
		}
		const conversationData = new FormData();
		conversationData.append('name', name);
		conversationData.append('question', question);
		conversationData.append('sessionId', activeChatId.value || '');
		conversationData.append('result', answerContent);
		if (selectFile.value?.uid) {
			conversationData.append('file', selectFile.value);
		}
		if (isFileArray && res.data.data) {
			conversationData.append('fileList', JSON.stringify(res.data.data || []));
		}
		selectFile.value = null;
		saveConversationData(conversationData).then(() => {
			if (isNew) {
				getChatList();
			}
		})
	  }
	}).finally(() => {
	  isAnswerLoading.value = false;
	})
};

// 获取对话列表
const getChatList = () => {
	queryConversationList().then(res => {
		chatList.value = res.data || [];
	});
};

// 获取对话历史记录
const getChatHistory = () => {
	if (!activeChatId.value) return;
	queryConversationHistory(activeChatId.value).then(res => {
		let arr: any = [];
		(res.data || []).forEach((item: any) => {
			arr.push(
				{
					content: item.question,
					isBot: false,
					fileUrl: item.fileUrl || '',
				},
				{
					content: item.result,
					isBot: true,
					fileList: item.fileList ? JSON.parse(item.fileList) : [],
				}
			);
		});
		chatHistoryList.value = arr;
		scrollToBottom();
	});
};

// 删除会话
const deleteDialogue = async (item: any) => {
	try {
		await useMessageBox().confirm('确认删除该对话吗？');
	} catch {
		return;
	}
	try {
		await deleteConversation(item.sessionid);
		useMessage().success('删除成功');
		getChatList();
		activeChatId.value === generateUUID();
		chatHistoryList.value = [];
	} catch (err: any) {
		useMessage().error(err.msg || err.error);
	}
};
// AI检索，上传图片文件
const handleFileChange = (file: any) => {
	let ext = file.name.split('.').pop();
	uploadPicRef.value.clearFiles();
	if (!['png', 'jpg', 'jpeg'].includes(ext)) {
		ElMessage.warning('请上传png、jpg、jpeg格式的图片');
		return;
	}
	selectFile.value = file.raw;
};
// 查看文件详情
const onOpenFileDetail = (item: any) => {
	if  (!item.business_id && !item.id) return useMessage().wraning('该文件已失效，无法预览（缺少参数）');
	isShowDetail.value = true;
	nextTick(() => {
		fileDetailRef.value.initInfo({ id: item.business_id || item.id });
	});
};
</script>

<style scoped lang="scss">
:deep(.user-content-md) {
	margin: 0;
	padding: 0;
	word-break: break-all;
	p {
		margin-bottom: -1.6rem;
	}
	code,
	.language-python {
		width: 100%;
		display: block;
		white-space: pre-wrap;
		word-break: break-all;
	}
}
.analysis-box {
	display: flex;
	height: 100%;

	.analysis-left,
	.analysis-right {
		border-radius: 5px;
		background: #fff;
	}

	.analysis-left {
		width: 300px;
		padding: 30px 0;

		.chat-btn-item {
			margin: auto 20px;
			height: 48px;
			line-height: 48px;
			border-radius: 5px;
			padding: 0 35px 0 20px;
			background: var(--el-color-primary);
			color: #fff;
			cursor: pointer;
			text-align: center;
		}

		.chat-item-list {
			height: calc(100vh - 260px);
			overflow-y: auto;
		}

		.add-item {
			&:hover {
				filter: brightness(1.2);
			}
		}

		.dialogue-item {
			margin-top: 20px;
			color: #1d1d1d66;
			background: #eaeaea;
			text-overflow: ellipsis;
			overflow: hidden;
			white-space: nowrap;
			position: relative;

			&.active,
			&:hover {
				background: var(--el-color-primary-light-9);
				color: var(--el-color-primary);
			}
			.delete-icon {
				position: absolute;
				right: 10px;
				top: 16px;
				cursor: pointer;
				&:hover {
					color: red;
				}
			}
		}
	}

	.analysis-right {
		margin-left: 10px;
		flex: 1;
		padding: 16px;

		.question-home {
			height: 100%;
		}

		.chat-list-box {
			width: 100%;
			height: calc(100% - 180px);
			overflow-y: auto;
			font-size: 16px;
			margin-bottom: 20px;
			padding: 10px 40px;

			.chat-item {
				display: flex;
				align-items: flex-start;
				margin-bottom: 20px;

				&-left {
					.user-image {
						margin-right: 15px;
					}
					.user-content {
						border-radius: 0px 20px 20px 20px;
					}
				}

				&-right {
					flex-direction: row-reverse;

					.user-image {
						margin-left: 15px;
					}
					.user-content {
						border-radius: 20px 0px 20px 20px;
					}
				}

				.user-image {
					border-radius: 50%;
					width: 80px;
					height: 80px;
					margin-top: -9px;
				}

				.user-content {
					min-width: 300px;
					max-width: 700px;
					padding: 17px 19px;
					white-space: pre-line;
					word-break: break-all;
					color: #554242;
					background: #fff;
					box-shadow: 0 2px 10px 0 rgba(177, 177, 177, 0.4);

					@keyframes loadingRotate {
						0% {
							transform: rotate(0deg);
						}

						100% {
							transform: rotate(360deg);
						}
					}

					.answer-loading {
						width: 20px;
						display: inline-block;
						margin-left: 10px;
						animation: loadingRotate 1.2s linear infinite;
					}

					.file-item {
						margin-top: 5px;
						cursor: pointer;
						&:hover {
							color: var(--el-color-primary);
						}
					}

					.file-tip {
						border-top: 1px solid #eee;
						padding-top: 5px;
						margin-top: 5px;
						color: #999;
						font-size: 12px;
						.el-image {
							width: 60px;
							height: 60px;
							border-radius: 5px;
						}
					}
				}
			}
		}

		.chat-input-box {
			position: relative;
			padding: 0 40px;

			.ai-search-toolbar {
				display: flex;
				justify-content: space-between;
				align-items: center;
				width: 100%;
				margin-bottom: 10px;
				.el-button {
					border-radius: 5px;
					height: 30px;
				}
				.upload-pic-btn {
					height: 30px;
					cursor: pointer;
					border-radius: 5px;
					border: 1px solid #dadada;
					background: #fff;
					padding: 5px;
					color: rgba(0, 0, 0, 0.6);
					transition: all 0.3s;
					img {
						width: 16px;
					}
					&:hover,
					&.active {
						background: var(--el-color-primary-light-9);
						border-color: var(--el-color-primary);
						color: var(--el-color-primary);
					}
				}
			}
			:deep(.el-textarea) {
				box-shadow: 0 2px 20px 0 rgba(177, 177, 177, 0.4);
				border-radius: 24px;

				.el-textarea__inner {
					height: 112px !important;
					padding: 17px 150px 17px 17px;
					font-size: 16px;
					border-radius: 10px;
					box-shadow: none;
				}
				.el-input__count {
					top: 50px;
					right: 80px;
					color: rgba(29, 29, 29, 0.6);
				}
			}

			.btn-send {
				position: absolute;
				right: 55px;
				top: 70px;
				width: 50px;
				height: 50px;
				line-height: 50px;
				text-align: center;
				border-radius: 5px;
				padding: 0;
				.icon-send {
					width: 24px;
				}
			}
		}
	}
}
</style>
