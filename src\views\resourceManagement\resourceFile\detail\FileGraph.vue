<template>
	<div class="file-detail-graph">
		<div class="file-graph-box">
			<NodeGraph ref="graphRef" :data="graphData" />
		</div>
		<div class="file-info-box">
			<div class="file-info-title">基础信息</div>
			<div class="file-info-item" v-for="item in fileInfoData" :key="item.key">
				<div class="info-label">{{ item.label }}</div>
				<div class="info-value">{{ item.value || '-' }}</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { getFileGraph } from '/@/api/resource/resourceFile';
import NodeGraph from '/@/components/NodeGraph/index.vue';
const props = defineProps({
	record: {
		type: Object,
		default: null,
	},
});
const graphRef = ref<any>(null);
const graphData = ref<any>({});

onMounted(() => {
	getFileGraphData();
});

const fileInfoData = computed(() => {
	return [
		{ label: '资源名称', value: props.record.name, key: 'name', type: 'text' },
		{ label: '上传人员', value: props.record.createUser, key: 'createUser', type: 'text' },
		{ label: '上传人员所属部门', value: '', key: '', type: 'text' },
		{ label: '公开状态', value: props.record.securityName, key: 'securityName', type: 'text' },
		{ label: '来源单位', value: props.record.deptName, key: 'totalSize', type: 'text' },
		{ label: '关键词', value: props.record.labels, key: 'labels', type: 'tags' },
		{ label: '描述', value: props.record.desc, key: 'desc', type: 'text' },
	];
});

// 获取知识图谱数据
const getFileGraphData = async () => {
	const res = await getFileGraph(props.record.id);
	graphData.value = res.data || {};
	nextTick(() => {
		graphRef.value.clearGraph();
		graphRef.value.setGraph();
	});
};
</script>

<style lang="scss" scoped>
.file-detail-graph {
	padding: 20px;
	height: 100%;
	display: flex;
	align-items: start;
	.file-info-box {
		width: 400px;
		height: 100%;
		overflow-y: auto;
		background: #f3f3f3;
		.file-info-title {
			padding: 10px 16px;
			color: rgba(0, 0, 0, 0.4);
			border-bottom: 1px solid #e7e7e7;
		}
		.file-info-item {
			padding: 10px 16px;
			border-bottom: 1px solid #e7e7e7;
			display: flex;
			align-items: start;
			.info-label {
				width: 50%;
			}
			.info-value {
				width: 50%;
			}
		}
	}
	.file-graph-box {
		width: 100%;
		height: 100%;
		flex: 1;
		.file-graph {
			width: 100%;
			height: 100%;
		}
	}
}
</style>
