import request from '/@/utils/request';

/**
 * 档案数量分布
 *
 */
export function getResourceDistribution(data: any) {
    return request({
        url: '/datacenter/data/resource/resourceDistribution',
        method: 'post',
        data
    });
}

/**
 * 部门排行
 *
 */
export function getResourceDeptRange(data: any) {
    return request({
        url: '/datacenter/data/resource/deptRange',
        method: 'post',
        data
    });
}

/**
 * 借阅统计
 *
 */
export function getSummaryBorrow(params: any) {
    return request({
        url: '/datacenter/archive/summary/borrow',
        method: 'get',
        params
    });
}

/**
 * 档案数量趋势
 *
 */
export function getNumberTrend(data: any) {
    return request({
        url: '/datacenter/archive/numberTrend',
        method: 'post',
        data
    });
}

/**
 * 档案入库趋势
 *
 */
export function getColAndArchiveTrend(data: any) {
    return request({
        url: '/datacenter/archive/colAndArchiveTrend',
        method: 'post',
        data
    });
}


/**
 * 档案统计-第一排的卡片
 *
 */
export function getCardCount(data: any) {
    return request({
        url: '/datacenter/archive/cardCount',
        method: 'post',
        data
    });
}

