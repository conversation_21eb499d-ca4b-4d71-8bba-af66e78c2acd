<template>
  <div class="exhibition-config">
    <div class="config-content">
      <div class="config-section">
        <h4 class="section-title">展览基本信息</h4>
        <el-form :model="formData" label-width="120px">
          <el-form-item label="展览主题">
            <el-input v-model="formData.theme" placeholder="请输入展览主题" />
          </el-form-item>
          <el-form-item label="展览时间">
            <el-date-picker v-model="formData.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期" style="width: 100%" />
          </el-form-item>
          <el-form-item label="展览地点">
            <el-input v-model="formData.location" placeholder="请输入展览地点" />
          </el-form-item>
          <el-form-item label="展览描述">
            <el-input v-model="formData.description" type="textarea" :rows="4" placeholder="请输入展览描述" />
          </el-form-item>
        </el-form>
      </div>

      <div class="config-section">
        <h4 class="section-title">
          展品配置
          <el-button type="primary" size="small" @click="showAddExhibitDialog = true" style="margin-left: 16px">
            <el-icon>
              <Plus />
            </el-icon>
            添加展品
          </el-button>
        </h4>
        <div class="exhibit-list">
          <div v-for="exhibit in exhibitList" :key="exhibit.id" class="exhibit-item">
            <div class="exhibit-image">
              <img :src="exhibit.image || '/src/assets/default-exhibit.jpg'" alt="展品图片" />
            </div>
            <div class="exhibit-info">
              <h5 class="exhibit-name">{{ exhibit.name }}</h5>
              <p class="exhibit-desc">{{ exhibit.description }}</p>
              <div class="exhibit-meta">
                <span class="exhibit-category">{{ exhibit.category }}</span>
                <span class="exhibit-period">{{ exhibit.period }}</span>
              </div>
            </div>
            <div class="exhibit-actions">
              <el-button type="primary" link size="small" @click="editExhibit(exhibit)">编辑</el-button>
              <el-button type="danger" link size="small" @click="deleteExhibit(exhibit)">删除</el-button>
            </div>
          </div>
          <el-empty v-if="exhibitList.length === 0" description="暂无展品" :image-size="80" />
        </div>
      </div>

      <div class="config-section">
        <h4 class="section-title">展览统计</h4>
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">
              <el-icon>
                <Collection />
              </el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ exhibitList.length }}</div>
              <div class="stat-label">展品数量</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <el-icon>
                <View />
              </el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ formData.viewCount || 0 }}</div>
              <div class="stat-label">浏览次数</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <el-icon>
                <Star />
              </el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ formData.favoriteCount || 0 }}</div>
              <div class="stat-label">收藏次数</div>
            </div>
          </div>
        </div>
      </div>

      <div class="action-buttons">
        <el-button @click="resetForm">重置</el-button>
        <el-button type="primary" @click="saveConfig" :loading="saving">保存配置</el-button>
      </div>
    </div>

    <!-- 添加展品弹窗 -->
    <el-dialog v-model="showAddExhibitDialog" title="添加展品" width="600px" :close-on-click-modal="false">
      <el-form :model="exhibitForm" label-width="100px">
        <el-form-item label="展品名称">
          <el-input v-model="exhibitForm.name" placeholder="请输入展品名称" />
        </el-form-item>
        <el-form-item label="展品描述">
          <el-input v-model="exhibitForm.description" type="textarea" :rows="3" placeholder="请输入展品描述" />
        </el-form-item>
        <el-form-item label="展品分类">
          <el-select v-model="exhibitForm.category" placeholder="请选择分类">
            <el-option label="文物" value="文物" />
            <el-option label="艺术品" value="艺术品" />
            <el-option label="历史资料" value="历史资料" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
        <el-form-item label="历史时期">
          <el-input v-model="exhibitForm.period" placeholder="请输入历史时期" />
        </el-form-item>
        <el-form-item label="展品图片">
          <el-upload class="exhibit-uploader" :show-file-list="false" :on-success="handleExhibitImageSuccess"
            :before-upload="beforeExhibitImageUpload" action="/api/upload">
            <img v-if="exhibitForm.image" :src="exhibitForm.image" class="exhibit-upload-image" />
            <div v-else class="exhibit-upload-placeholder">
              <el-icon class="upload-icon">
                <Plus />
              </el-icon>
              <div class="upload-text">上传图片</div>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddExhibitDialog = false">取消</el-button>
        <el-button type="primary" @click="addExhibit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { Plus, Collection, View, Star } from '@element-plus/icons-vue';

interface Props {
  sceneId: string;
}

const props = defineProps<Props>();
const { success, error } = useMessage();
const { confirm } = useMessageBox();

// 响应式数据
const saving = ref(false);
const showAddExhibitDialog = ref(false);

const formData = reactive({
  theme: '',
  dateRange: [],
  location: '',
  description: '',
  viewCount: 0,
  favoriteCount: 0,
});

const exhibitForm = reactive({
  name: '',
  description: '',
  category: '',
  period: '',
  image: '',
});

const exhibitList = ref<any[]>([]);

// 方法
const handleExhibitImageSuccess = (response: any) => {
  if (response.code === 200) {
    exhibitForm.image = response.data.url;
    success('图片上传成功');
  }
};

const beforeExhibitImageUpload = (file: File) => {
  const isImage = file.type.startsWith('image/');
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isImage) {
    error('只能上传图片文件!');
    return false;
  }
  if (!isLt2M) {
    error('图片大小不能超过 2MB!');
    return false;
  }
  return true;
};

const addExhibit = () => {
  if (!exhibitForm.name) {
    error('请输入展品名称');
    return;
  }

  const newExhibit = {
    id: Date.now().toString(),
    ...exhibitForm,
  };

  exhibitList.value.push(newExhibit);

  // 重置表单
  Object.assign(exhibitForm, {
    name: '',
    description: '',
    category: '',
    period: '',
    image: '',
  });

  showAddExhibitDialog.value = false;
  success('展品添加成功');
};

const editExhibit = (exhibit: any) => {
  // TODO: 实现编辑功能
};

const deleteExhibit = async (exhibit: any) => {
  try {
    await confirm(`确认删除展品"${exhibit.name}"吗？`);
    const index = exhibitList.value.findIndex(item => item.id === exhibit.id);
    if (index > -1) {
      exhibitList.value.splice(index, 1);
      success('删除成功');
    }
  } catch {
    // 用户取消删除
  }
};

const resetForm = () => {
  Object.assign(formData, {
    theme: '',
    dateRange: [],
    location: '',
    description: '',
    viewCount: 0,
    favoriteCount: 0,
  });
  exhibitList.value = [];
  loadData();
};

const saveConfig = async () => {
  try {
    saving.value = true;
    // TODO: 调用API保存配置
    await new Promise(resolve => setTimeout(resolve, 1000));
    success('保存成功');
  } catch (err) {
    error('保存失败');
  } finally {
    saving.value = false;
  }
};

const loadData = async () => {
  try {
    // TODO: 调用API获取数据
    // 模拟数据
    Object.assign(formData, {
      theme: '武当山道教文化展',
      dateRange: [],
      location: '武当山博物馆',
      description: '展示武当山深厚的道教文化底蕴和历史传承',
      viewCount: 1250,
      favoriteCount: 89,
    });

    exhibitList.value = [
      {
        id: '1',
        name: '明代道教法器',
        description: '明代武当山道观使用的珍贵法器',
        category: '文物',
        period: '明代',
        image: '',
      },
      {
        id: '2',
        name: '道教经典古籍',
        description: '珍贵的道教经典手抄本',
        category: '历史资料',
        period: '清代',
        image: '',
      },
    ];
  } catch (err) {
    error('获取数据失败');
  }
};

onMounted(() => {
  loadData();
});
</script>

<style scoped lang="scss">
.exhibition-config {
  height: 100%;
  overflow: auto;
}

.config-content {
  padding: 24px;
}

.config-section {
  margin-bottom: 32px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 2px solid #409eff;
    display: inline-block;
  }
}

.exhibit-list {
  .exhibit-item {
    display: flex;
    align-items: center;
    padding: 16px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    margin-bottom: 12px;
    transition: all 0.3s;

    &:hover {
      border-color: #409eff;
      background: #f0f9ff;
    }

    .exhibit-image {
      width: 80px;
      height: 80px;
      margin-right: 16px;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 6px;
      }
    }

    .exhibit-info {
      flex: 1;

      .exhibit-name {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
        margin: 0 0 8px 0;
      }

      .exhibit-desc {
        font-size: 14px;
        color: #606266;
        margin: 0 0 8px 0;
        line-height: 1.4;
      }

      .exhibit-meta {
        display: flex;
        gap: 12px;

        span {
          font-size: 12px;
          color: #909399;
          background: #f5f7fa;
          padding: 2px 8px;
          border-radius: 4px;
        }
      }
    }

    .exhibit-actions {
      display: flex;
      gap: 8px;

      .el-button {
        padding: 4px 8px;
        font-size: 12px;
      }
    }
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;

  .stat-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 16px;

    .stat-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: white;
    }

    .stat-content {
      .stat-number {
        font-size: 24px;
        font-weight: 700;
        color: #303133;
        line-height: 1;
        margin-bottom: 4px;
      }

      .stat-label {
        font-size: 14px;
        color: #606266;
      }
    }
  }
}

.exhibit-uploader {
  :deep(.el-upload) {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s;
    width: 150px;
    height: 150px;

    &:hover {
      border-color: #409eff;
    }
  }
}

.exhibit-upload-image {
  width: 150px;
  height: 150px;
  object-fit: cover;
  display: block;
}

.exhibit-upload-placeholder {
  width: 150px;
  height: 150px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #8c939d;

  .upload-icon {
    font-size: 28px;
    margin-bottom: 8px;
  }

  .upload-text {
    font-size: 14px;
  }
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}
</style>
