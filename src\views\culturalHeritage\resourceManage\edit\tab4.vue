<template>
  <div style="width: 100%; height: 100%; position: relative">
    <div class="layout-padding-auto layout-padding-view" style="padding: 15px 25px">
      <el-row class="mb0">
        <el-form
          :model="state.queryForm"
          ref="queryRef"
          :inline="true"
          @keyup.enter="getDataList"
          class="custom-form-inline"
          style="height: 45px"
        >
          <el-form-item label="操作类型" prop="state" style="width: 320px">
            <el-select
              v-model="state.queryForm.state"
              clearable
              placeholder="请选择申请权限"
            >
              <el-option label="查看" value="1" />
              <el-option label="修改" value="23" />
              <el-option label="删除" value="24" />
              <el-option label="评论" value="3" />
              <el-option label="统计" value="28" />
            </el-select>
          </el-form-item>
          <el-form-item label="操作人员" prop="userId" style="width: 320px">
            <el-select placeholder="请输入操作人员" v-model="state.queryForm.userId">
              <el-option :label="item.name" :value="item.userId" v-for="item in userList" />
            </el-select>
          </el-form-item>
          <el-form-item style="width: 320px">
            <el-button icon="search" type="primary" @click="getDataList">查询</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-row>
      <el-row class="mb10" style="width: 100%">
        <el-button
          @click="onOperation('DoExport')"
          :disabled="selectDatas.length === 0"
          type="primary"
          >批量导出</el-button
        >
      </el-row>
      <div style="display: flex; flex-direction: column; height: calc(100% - 130px)">
        <el-table
          :data="state.dataList || []"
          v-loading="state.loading"
          show-overflow-tooltip
          border
          :cell-style="tableStyle.cellStyle"
          :header-cell-style="tableStyle.headerCellStyle"
          @selection-change="selectionChangHandle"
          class="custom-table-control"
        >
          <el-table-column type="selection" width="40" align="center" />
          <el-table-column
            v-for="(item, k) in tableColumn"
            align="center"
            :key="k"
            :prop="item.key"
            :label="item.title"
            :width="item.width || 'auto'"
          >
            <template #default="scope">
              <div v-if="item.key === 'czdx'" style="height: 35px; line-height: 35px">
                <span
                  >【{{ props.editRecord.assets_num || "暂无编码" }}】{{
                    props.editRecord.assets_name
                  }}</span
                >
              </div>
              <div v-else style="height: 35px; line-height: 35px">
                <span>{{ scope.row[item.key] || "--" }}</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
          v-bind="state.pagination"
        />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from "vue";
import { operateLogList, getListAllUser } from "/@/api/resource/filelist/file";
import { BasicTableProps, useTable } from "/@/hooks/table";
const emit = defineEmits(["backTo"]);
const props = defineProps({
  editRecord: {
    type: Object,
    default: null,
  },
});
const userList = ref([]);
const backTo = () => {
  emit("backTo");
};
const loading = ref(false);
const queryRef = ref();
const selectDatas = ref([]);
const tableColumn: any = ref([
  { title: "操作对象", key: "name", width: "200px" },
  { title: "操作类型", key: "operateTypeName", width: "200px" },
  { title: "操作内容", key: "title" },
  { title: "操作人员", key: "createUser" },
  { title: "操作时间", key: "createTime" },
]);
const state: BasicTableProps = reactive<BasicTableProps>({
  queryForm: {
    tableName: undefined,
    businessId: "",
    userId: undefined,
    state: undefined,
  },
  // descs: ["gmt_create"],
  pageList: operateLogList,
  createdIsNeed: false,
  props: {
    item: "records",
    totalCount: "total",
  },
  isPage: true,
});
const {
  tableStyle,
  getDataList,
  currentChangeHandle,
  sizeChangeHandle,
  downBlobFile,
} = useTable(state);
// 清空搜索条件
const resetQuery = () => {
  queryRef.value?.resetFields();
  state.queryForm.state = null;
  getDataList();
};
// 多选事件
const selectionChangHandle = (objs: any) => {
  selectDatas.value = objs;
};
const onOperation = (type: string, record?: any) => {
  switch (type) {
    case "DoExport":
      let ids = selectDatas.value.map((obj: any) => {
        return obj.id;
      });
      let params = {
        tableName: state.queryForm.tableName,
        businessId: props.editRecord.resourceId,
        ids: ids.join(","),
      };
      downBlobFile("/datacenter/operate/log/export", params, "operateLog.xlsx");
      break;
  }
};

const queryListAllUser = () => {
  getListAllUser({}).then((res) => {
    if (res.ok) {
      userList.value = res.data || [];
    }
  });
};
onMounted(() => {
  state.queryForm.tableName = props.editRecord.tabName;
  state.queryForm.businessId = props.editRecord.resourceId;
  getDataList();
  queryListAllUser();
  // 重置表单数据
  nextTick(() => {});
});
</script>

<style scoped lang="scss">
.group-header {
  margin-bottom: 10px;

  .group-title {
    font-size: 16px;
    font-weight: 600;
    padding-left: 15px;
    position: relative;
    color: #554242;
    letter-spacing: 0.1em;

    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 15%;
      width: 3px;
      height: 70%;
      border-radius: 6px;
      background: var(--el-color-primary);
    }
  }

  .group-add {
    cursor: pointer;
    color: var(--el-color-primary);
  }
}
.group-list {
  height: calc(100% - 70px);
  overflow-y: auto;

  .group-item {
    cursor: pointer;
    padding: 8px 10px;
    border-radius: 6px;

    &.active {
      background: var(--el-color-primary-light-9);
    }

    .group-name {
      width: 200px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      span {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .group-icon {
        margin-right: 10px;
        width: 20px;
      }
    }
    &:hover {
      background-color: #cccccc65;
    }
  }
}

.operation-btns {
  button.el-button.el-button--primary {
    --el-button-text-color: #409eff;
  }
}
</style>
