<template>
  <div class="files-previewer flex-all-center">
    <!-- TXT预览 -->
    <txt-viewer v-if="showType === 1" :fileUrl="fileUrl" />
    <!-- office系列预览 -->
    <div class="flex-col flex-all-center" v-else-if="showType === 2">
      <div class="file-no-preview">该类型文件暂不支持预览</div>
      <el-button class="mt8" type="primary" @click="handleDownloadView">下载预览</el-button>
      <!-- <el-button class="mt8" type="primary" @click="handleViewDoc">跳转预览</el-button> -->
    </div>
    <!-- PDF预览 -->
    <pdf-viewer v-else-if="showType === 3" :fileUrl="fileUrl" />
    <!-- 图片预览 -->
    <!-- <el-image-viewer v-else-if="showType === 4" :url-list="[fileUrl]" /> -->
    <el-image class="previewer-image" v-else-if="showType === 4" :preview-src-list="[fileUrl]" :src="fileUrl" fit="contain" />
    <!-- 音频预览 -->
    <video-viewer v-else-if="showType === 5" :src="fileUrl" :poster="icon_poster_audio" width="100%" />
    <!-- 视频预览 -->
    <video-viewer v-else-if="showType === 6" :src="fileUrl" :poster="icon_poster_video" width="100%" />
    <div class="flv-video-viewer" v-else-if="showType === 7">
      <flv-viewer :flvUrl="fileUrl" />
    </div>
    <!-- 其他：不可预览 -->
    <div v-else class="file-no-preview">
      <div>该类型文件暂不支持预览</div>
      <el-button class="mt8" type="primary" @click="handleDownloadView">下载预览</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import icon_poster_audio from '/@/assets/img/archives/icon_poster_audio.png'
import icon_poster_video from '/@/assets/img/archives/icon_poster_video.png'

const TxtViewer = defineAsyncComponent(() => import('/@/components/FilePreviewer/TxtViewer.vue'));
const PdfViewer = defineAsyncComponent(() => import('/@/components/FilePreviewer/PdfViewer.vue'));
const VideoViewer = defineAsyncComponent(() => import('/@/components/VideoPlayer/index.vue'));
const FlvViewer = defineAsyncComponent(() => import('/@/components/FilePreviewer/FlvViewer.vue'));

const props = defineProps({
  fileUrl: {
    type: String,
    default: () => '',
  },
  fileName: {
    type: String,
    default: () => '',
  }
})
const filePreviewType = ref([
  { type: 1, fileTypes: ['txt'] },
  { type: 2, fileTypes: ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'] },
  { type: 3, fileTypes: ['pdf'] },
  { type: 4, fileTypes: ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'] },
  { type: 5, fileTypes: ['mp3', 'wav', 'wma', 'aac', 'ogg', 'flac'] },
  { type: 6, fileTypes: ['mp4', 'm4v', 'wmv', 'rmvb'] },
  { type: 7, fileTypes: ['flv'] },
])
const showType = ref(0)

onMounted(() => {
  getFileType()
})

// 获取文件类型
const getFileType = () => {
  if (props.fileName) {
    let lastDotIndex = props.fileName.lastIndexOf('.');
    let affix = props.fileName.substring(lastDotIndex + 1);
    let type = filePreviewType.value.find(item => item.fileTypes.includes(affix?.toLowerCase()))?.type || 0;
    showType.value = type;
  }
}

const handleViewDoc = () => {
  window.open(`https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(props.fileUrl)}`)
}

// 下载预览
const handleDownloadView = () => {
  fetch(props.fileUrl).then(res => res.blob()).then(blob => {
    const a = document.createElement('a')
    a.href = URL.createObjectURL(blob)
    a.download = props.fileName || ''
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
  })
}
</script>

<style scoped lang="scss">
.flex-all-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.files-previewer {
  width: 100%;
  height: 100%;
  position: relative;

  .file-no-preview {
    color: #909399;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }

  .flv-video-viewer {
    width: 100%;
    min-height: 450px;
  }
  :deep(.previewer-image) {
    height: 95%;
    max-width: 70%;
    .el-image__inner {
      max-width: 100% !important;
      max-height: 100% !important;
    }
  }
}
</style>