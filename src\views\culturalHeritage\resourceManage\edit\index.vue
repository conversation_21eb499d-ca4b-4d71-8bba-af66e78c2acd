<template>
  <div class="layout-padding w100" v-if="editCode">
    <div class="layout-padding-view layout-border-radius" :style="{ backgroundColor: 'transparent' }" style="border: none;">
      <div class="editor-header flex" :style="{ backgroundColor: '#ffffff', borderRadius: activeName == '关联文件' ? '24px' : '0px' }">
        <el-tabs v-model="activeName" @tab-click="handleClick" class="editor-header-title tabs" :style="{ pointerEvents: 'auto' }">
          <el-tab-pane label="基本信息" name="基本信息"></el-tab-pane>
          <el-tab-pane :label="'关联文件（' + file_total + '）'" name="关联文件"></el-tab-pane>
          <el-tab-pane label="操作日志" name="操作日志"></el-tab-pane>
          <!-- <el-tab-pane v-if="props.editCode != 'add'" label="质量检测" name="质量检测"></el-tab-pane> -->
        </el-tabs>
      </div>
      <editBaseInfo v-if="activeName == '基本信息' && editCode == 'edit'" :editRecord="editRecord" :editCode="editCode" @backTo="backTo" />
      <tab2 v-if="activeName == '基本信息' && editCode != 'edit'" :editRecord="editRecord" :editCode="editCode" @backTo="backTo" />
      <tab3 v-if="activeName == '关联文件'" :editRecord="editRecord" :editCode="editCode" @backTo="backTo" @fileRefresh="getFileFolderData()" />
      <tab4 v-if="activeName == '操作日志'" :editRecord="editRecord" :editCode="editCode" @backTo="backTo" />
      <!-- <tab5 v-show="ctiveName == '质量检测'" :editRecord="props.editRecord" :editCode="props.editCode" @backTo="backTo" /> -->
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useMessage } from "/@/hooks/message";
  import { useRoute } from "vue-router";
  const route = useRoute();
  import { fetchList as fetchList_file } from "/@/api/resource/filelist/file";
  const emit = defineEmits(["backTo"]);
  // 引入组件
  const tab2 = defineAsyncComponent(() => import("./tab2.vue"));
  const tab3 = defineAsyncComponent(() => import("./tab3.vue"));
  const tab4 = defineAsyncComponent(() => import("./tab4.vue"));
  const editBaseInfo = defineAsyncComponent(() => import("./editBaseInfo.vue"));
  // const tab5 = defineAsyncComponent(() => import("./tab5.vue"));

  const editCode = ref();
  const editRecord: any = ref({});
  const activeName = ref("基本信息");
  const file_total = ref(0);
  const backTo = () => {
    emit("backTo");
  };
  const handleClick = (tab: any, event: any) => {};
  onMounted(() => {
    let ops = (route.query as any).ops.split(",");
    if (ops.length < 2) {
      useMessage().wraning("当前数据无详细信息");
      return;
    }
    editRecord.value.tabName = ops[0];
    editRecord.value.tableName = ops[0];
    editRecord.value.resourceId = ops[1];
    if (ops[2] == 1) {
      editCode.value = "edit";
    } else {
      editCode.value = "view";
    }
    getFileFolderData();
  });
  const getFileFolderData = async () => {
    let res = await fetchList_file({
      resourceId: editRecord.value.resourceId,
      tabName: editRecord.value.tabName,
    });
    file_total.value = res.data.total;
  };
</script>

<style lang="scss" scoped>
  .editor-header {
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    margin-bottom: -25px;
    .editor-header-title {
      font-size: 18px;
      width: 100%;
    }
  }

  ::v-deep(.form-subTitle) {
    width: 100%;
    font-size: 16px;
    position: relative;
    text-indent: 35px;
    color: #554242;
    .split-line {
      position: absolute;
      left: 25px;
      height: 18px;
      width: 3px;
      background-color: #695858;
      margin-top: 2px;
    }
  }
  ::v-deep(.dialog-footer) {
    position: absolute;
    right: 20px;
    top: -40px;
  }
  ::v-deep(.el-dialog__header) {
    padding-bottom: 0px;
    margin-bottom: -5px;
  }
  .tabs {
    :deep(.el-tabs__nav) {
      padding: 0 20px;
      .el-tabs__item {
        font-size: 16px;
        font-weight: bold;
      }
    }
  }
</style>
