<template>
  <div style="width: 100%; height: 100%; position: relative;">
    <div class="layout-padding-auto layout-padding-view"  style="padding: 15px 25px;">
      <el-table :data="state.dataList || []" v-loading="state.loading" show-overflow-tooltip border :cell-style="tableStyle.cellStyle" :header-cell-style="tableStyle.headerCellStyle">
        <el-table-column v-for="(item, k) in tableColumn" align="center" :key="k" :prop="item.key" :label="item.title" :width="'auto'">
          <template #default="scope">
            <span>
              <span>{{ scope.row[item.key] }}</span>
            </span>
          </template>
        </el-table-column>
      </el-table>
      <pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from "vue";
  import { useMessage, useMessageBox } from "/@/hooks/message";
  import { BasicTableProps, useTable } from "/@/hooks/table";
  const emit = defineEmits(["backTo"]);
  const props = defineProps({
    resuorceData: {
      type: Array,
      default: [],
    },
    deptData: {
      type: Array,
      default: [],
    },
    editCode: {
      type: String,
      default: null,
    },
    editRecord: {
      type: Object,
      default: null,
    },
  });
  const loading = ref(false);
  const tableColumn: any = ref([]);
  const backTo = () => {
    emit("backTo");
  };
  const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: {},
  });
  const { tableStyle, getDataList, currentChangeHandle, sizeChangeHandle } = useTable(state);
  onMounted(() => {
    tableColumn.value = [
      { title: "字段1", key: "field1" },
      { title: "字段2", key: "field2" },
      { title: "字段3", key: "field3" },
      { title: "字段4", key: "field4" },
      { title: "字段5", key: "field5" },
      { title: "字段6", key: "field6" },
    ];
    state.dataList = [
      { field1: "测试1", field2: "测试1", field3: "测试1", field4: "测试1", field5: "测试1", field6: "测试1" },
      { field1: "测试1", field2: "测试1", field3: "测试1", field4: "测试1", field5: "测试1", field6: "测试1" },
      { field1: "测试1", field2: "测试1", field3: "测试1", field4: "测试1", field5: "测试1", field6: "测试1" },
      { field1: "测试1", field2: "测试1", field3: "测试1", field4: "测试1", field5: "测试1", field6: "测试1" },
      { field1: "测试1", field2: "测试1", field3: "测试1", field4: "测试1", field5: "测试1", field6: "测试1" },
      { field1: "测试1", field2: "测试1", field3: "测试1", field4: "测试1", field5: "测试1", field6: "测试1" },
      { field1: "测试1", field2: "测试1", field3: "测试1", field4: "测试1", field5: "测试1", field6: "测试1" },
      { field1: "测试1", field2: "测试1", field3: "测试1", field4: "测试1", field5: "测试1", field6: "测试1" },
      { field1: "测试1", field2: "测试1", field3: "测试1", field4: "测试1", field5: "测试1", field6: "测试1" },
      { field1: "测试1", field2: "测试1", field3: "测试1", field4: "测试1", field5: "测试1", field6: "测试1" },
      { field1: "测试1", field2: "测试1", field3: "测试1", field4: "测试1", field5: "测试1", field6: "测试1" },
    ];
    // 重置表单数据
    nextTick(() => {});
  });
</script>

<style scoped lang="scss">
  .group-header {
    margin-bottom: 10px;

    .group-title {
      font-size: 16px;
      font-weight: 600;
      padding-left: 15px;
      position: relative;
      color: #554242;
      letter-spacing: 0.1em;

      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 15%;
        width: 3px;
        height: 70%;
        border-radius: 6px;
        background: var(--el-color-primary);
      }
    }

    .group-add {
      cursor: pointer;
      color: var(--el-color-primary);
    }
  }
  .group-list {
    height: calc(100% - 70px);
    overflow-y: auto;

    .group-item {
      cursor: pointer;
      padding: 8px 10px;
      border-radius: 6px;

      &.active {
        background: var(--el-color-primary-light-9);
      }

      .group-name {
        width: 200px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        span {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .group-icon {
          margin-right: 10px;
          width: 20px;
        }
      }
      &:hover {
        background-color: #cccccc65;
      }
    }
  }

  .operation-btns {
    button.el-button.el-button--primary {
      --el-button-text-color: #409eff;
    }
  }
</style>
