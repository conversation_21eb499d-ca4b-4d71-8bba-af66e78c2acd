<template>
  <div class="mt-box">
    <div class="layout-padding-auto resource-catalog">
      <div class="layout-model-title">资源目录</div>
      <el-scrollbar>
        <query-tree ref="queryTreeRef" placeholder="目录搜索" :query="treeData.queryList" :props="{
          label: 'catalogName',
          children: 'childCatalogs',
          value: 'catalogNo',
        }" :show-expand="true" @node-click="handleNodeClick" @treeDataLoaded="handleTreeDataLoaded">
          <template #default="{ node, data }">
            <el-tooltip v-if="data.isLock" class="item" effect="dark" content="无数据权限" placement="right-start">
              <span>{{ node.label }}
                <SvgIcon name="ele-Lock" />
              </span>
            </el-tooltip>
            <span v-if="!data.isLock">{{ node.label }}</span>
          </template>
        </query-tree>
      </el-scrollbar>
    </div>
    <div class="mtc-right">
      <div class="resource-header">
        <div class="info-box">
          <div class="info-right">
            <CP0004 :data="infoData.list" class="stat-cards" />
          </div>
        </div>
      </div>
      <div class="layout-padding-auto resource-content">
        <el-row style="height: 45px;">
          <el-form :model="state.queryForm" ref="queryRef" :inline="true" class="search-form" label-width="0">
            <el-row :gutter="20">
              <el-col :span="10">
                <el-row :gutter="15">
                  <el-col :span="6">
                    <el-button @click="handleAddItem" type="primary" size="default" :icon="Plus">新增数据项</el-button>
                  </el-col>
                  <!-- <el-col :span="1">
                    <el-button @click="onOperation('DoAdd')" type="primary" size="default">复制表</el-button>
                  </el-col> -->
                </el-row>
              </el-col>
              <el-col :span="4">
                <el-form-item label="" prop="publishState" style="width: 100%">
                  <el-select v-model="state.queryForm.publishState" clearable placeholder="请选择发布状态"
                    @change="getDataList">
                    <el-option label="已发布" :value="1" />
                    <el-option label="未发布" :value="0" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-form-item label="" prop="useState" style="width: 100%">
                  <el-select v-model="state.queryForm.useState" clearable placeholder="请选择启用状态" @change="getDataList">
                    <el-option label="启用" :value="1" />
                    <el-option label="禁用" :value="0" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-form-item label="" style="width: 100%" prop="assetsDepId">
                  <el-tree-select :disabled="props.deptData.length == 0" :data="props.deptData"
                    :props="{ value: 'id', label: 'name', children: 'children' }" check-strictly class="w100" clearable
                    placeholder="请选择所属部门" v-model="state.queryForm.assetsDepId" @change="getDataList">
                  </el-tree-select>
                </el-form-item>
              </el-col>
              <el-col :span="2" :offset="0">
                <el-form-item>
                  <el-button @click="resetQuery" size="default" :icon="Refresh">重置</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-row>

        <el-table ref="tableRef" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" row-key="sid"
          :data="state.dataList" v-loading="state.loading" show-overflow-tooltip :cell-style="tableStyle.cellStyle"
          :header-cell-style="tableStyle.headerCellStyle" @selection-change="selectionChangHandle" :show-header="false"
          :row-style="{ height: '124px' }" :row-class-name="'table-row'" class="resource-table">
          <el-table-column align="center" prop="tab1" label="图片" :width="'130px'">
            <template #default="scope">
              <div class="cl-item" @click.stop="onOperation('DoView', scope.row)">
                <el-image style="width: 90px; height: 90px; border-radius: 4px;" :src="icon_bg" fit="cover">
                  <template #error>
                    <div class="image-slot">
                      <el-icon>
                        <PictureFilled />
                      </el-icon>
                    </div>
                  </template>
                </el-image>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="left" prop="tab2" label="信息1" :width="'auto'">
            <template #default="scope">
              <div class="info-box" @click.stop="onOperation('DoView', scope.row)">
                <div class="info-item">
                  <span class="ib-key">名称：</span>
                  <span class="ib-value" style="font-weight: 500">{{
                    scope.row.name
                  }}</span>
                </div>
                <div class="info-item">
                  <span class="ib-key">资源数量：</span>
                  <span class="ib-value" style="color: var(--el-color-primary, #A12F2F); font-weight: 500">{{
                    scope.row.resourceNum || 0
                  }}<span style="margin-left: 5px">条</span></span>
                </div>
                <div class="info-item">
                  <span class="ib-key">创建人：</span>
                  <span class="ib-value">{{ scope.row.creatorName }}</span>
                </div>


              </div>
            </template>
          </el-table-column>
          <el-table-column align="left" prop="tab3" label="信息2" :width="'auto'">
            <template #default="scope">
              <div class="info-box" @click.stop="onOperation('DoView', scope.row)">
                <div class="info-item">
                  <span class="ib-key">所属类目：</span>
                  <el-tooltip effect="dark" :content="scope.row.catalogName" placement="top">
                    <span class="ib-value">{{ scope.row.catalogName }}</span>
                  </el-tooltip>
                </div>
                <div class="info-item">
                  <span class="ib-key">启用状态：</span>
                  <span class="ib-value" style="font-weight: 500"
                    :style="{ color: scope.row.useState == 1 ? '#2BA471' : '#CD2626' }">{{ scope.row.useState == 1 ?
                      "启用" : "禁用" }}</span>
                </div>
                <div class="info-item">
                  <span class="ib-key">创建时间：</span>
                  <span class="ib-value">{{ scope.row.createTime }}</span>
                </div>

              </div>
            </template>
          </el-table-column>
          <el-table-column align="left" prop="tab4" label="信息3" :width="'auto'">
            <template #default="scope">
              <div class="info-box" @click.stop="onOperation('DoView', scope.row)">
                <div class="info-item">
                  <span class="ib-key">来源单位：</span>
                  <span class="ib-value">{{ scope.row.deptName }}</span>
                </div>
                <div class="info-item">
                  <span class="ib-key">发布状态：</span>
                  <span class="ib-value" :style="{
                    color: scope.row.publishState == 1 ? '#008858' : '#D54941',
                  }">{{ scope.row.publishState == 1 ? "已发布" : "未发布" }}</span>
                </div>
                <div class="info-item">
                  <span class="ib-key">描述：</span>
                  <el-tooltip effect="dark" :content="scope.row.remark" placement="top">
                    <span class="ib-value">{{ scope.row.remark }}</span>
                  </el-tooltip>
                </div>

              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="operation" label="操作" :width="'250px'">
            <template #default="scope">
              <div class="operation-btns">
                <el-button @click.stop="handleEditItem(scope.row)" size="small" text type="primary"
                  class="op-button">编辑</el-button>
                <el-button @click.stop="onOperation('DoEdit', scope.row)" size="small" text type="primary"
                  class="op-button">字段管理</el-button>
                <el-dropdown>
                  <el-button size="small" text type="primary" class="op-button more-button">更多<el-icon>
                      <ArrowDown />
                    </el-icon></el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click.stop="onOperation('DoPublish', scope.row)"
                        v-if="scope.row.publishState != 1" :style="{
                          color: scope.row.useState == 1 ? '#CD2626' : '#63A103',
                        }">发布</el-dropdown-item>
                      <el-dropdown-item @click.stop="onOperation('DoCopy', scope.row)"
                        style="color: #CD2626">复制表</el-dropdown-item>
                      <el-dropdown-item @click.stop="onOperation('DoEnable', scope.row)" :style="{
                        color: scope.row.useState == 1 ? '#CD2626' : '#63A103',
                      }">{{ scope.row.useState == 1 ? "禁用" : "启用" }}</el-dropdown-item>
                      <el-dropdown-item @click.stop="onOperation('DoDelete', scope.row)"
                        style="color: #0052D9">删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
      </div>
    </div>
  </div>

  <!-- 新增数据项弹窗 -->
  <el-dialog v-model="dialogVisible" title="新增数据项" width="30%" :close-on-click-modal="false"
    :close-on-press-escape="false" class="add-item-dialog" append-to-body destroy-on-close>
    <div class="dialog-content">
      <el-form ref="dataFormRef" :model="form" :rules="dataRules" label-width="95px" v-loading="loading"
        class="dialog-form">
        <div class="form-subTitle mb5">
          <div class="split-line"></div>
          基础信息
        </div>
        <div>
          <el-form-item label="数据项名称" prop="name">
            <el-input v-model="form.name" maxlength="20" placeholder="请输入数据项名称" clearable />
          </el-form-item>
          <el-form-item label="所属类目" prop="catalogNo">
            <el-tree-select :disabled="resuorceData.length == 0" :data="resuorceData"
              :props="{ label: 'catalogName', value: 'catalogNo', children: 'childCatalogs' }" check-strictly
              class="resource-tree-select" clearable placeholder="请选择资源目录" v-model="form.catalogNo"
              popper-class="resource-select-dropdown" default-expand-all node-key="catalogNo">
              <template #default="{ data }">
                <el-icon v-if="data.catalogLevel == 3" style="margin-right: 3px; margin-top: -2px;">
                  <Document />
                </el-icon>
                <el-icon v-if="data.catalogLevel != 3" style="margin-right: 3px; margin-top: -2px;">
                  <FolderOpened />
                </el-icon>
                <span> {{ data.catalogName }}</span>
              </template>
            </el-tree-select>
          </el-form-item>

          <el-form-item label="启用状态" prop="useState">
            <el-select placeholder="请选择启用状态" clearable v-model="form.useState">
              <el-option label="启用" :value="1" />
              <el-option label="禁用" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item label="描述" prop="remark">
            <el-input type="textarea" maxlength="250" :rows="3" v-model="form.remark" placeholder="请输入描述"
              show-word-limit />
          </el-form-item>
        </div>
        <div class="form-subTitle mb5">
          <div class="split-line"></div>
          管理信息
        </div>
        <div>
          <el-form-item label="来源单位" prop="assetsDepId">
            <el-tree-select :disabled="props.deptData.length == 0" :data="props.deptData"
              :props="{ value: 'id', label: 'name', children: 'children' }" check-strictly class="dept-tree-select"
              clearable placeholder="请选择所属部门" v-model="form.assetsDepId" popper-class="dept-select-dropdown"
              node-key="id">
            </el-tree-select>
          </el-form-item>

          <el-form-item label="接入方式" prop="reportMode">
            <el-select placeholder="请选择接入方式" clearable v-model="form.reportMode">
              <el-option label="数据填报" :value="1" />
              <el-option label="数据对接" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item label="联系人" prop="adminName">
            <el-input v-model="form.adminName" maxlength="20" placeholder="请输入联系人" clearable />
          </el-form-item>

          <el-form-item label="联系方式" prop="adminTel">
            <el-input v-model="form.adminTel" maxlength="20" placeholder="请输入联系方式" clearable />
          </el-form-item>

          <el-form-item label="填报说明" prop="reportRemark">
            <el-input v-model="form.reportRemark" maxlength="250" placeholder="请输入填报说明" clearable />
          </el-form-item>
        </div>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSubmitForm" :loading="loading">提交</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { defineAsyncComponent, ref, reactive, nextTick } from "vue";
import SvgIcon from "/@/components/SvgIcon/index.vue";
import { Plus, Refresh, PictureFilled } from "@element-plus/icons-vue";
import { useMessage, useMessageBox } from '/@/hooks/message';
import { BasicTableProps, useTable } from "/@/hooks/table";
import icon_bg from "/@/assets/img/custom/bg_img.png";
import { fetchListForRecursive } from "/@/api/resource/catalog/catalog";
import {
  pageMetaData,
  countMetaData,
  addMetaData,
  editMetaData,
} from "/@/api/resource/catalog/dataAssets";

// 引入组件
const QueryTree = defineAsyncComponent(() => import("/@/components/QueryTree/index.vue"));
const CP0004 = defineAsyncComponent(() => import("/@/components/Custom/CP0004.vue"));

const props = defineProps({
  deptData: {
    type: Array,
    default: () => [],
  },
});
const emit = defineEmits(["openEditor"]);
const state: BasicTableProps = reactive<BasicTableProps>({
  queryForm: {
    name: "", //数据项名称
    catalogNo: "", //所属目录编号
    useState: "", //启用状态1启用0禁用
    publishState: "", //发布状态1启用0禁用
    assetsDepId: "", //所属部门id
  },
  createdIsNeed: false,
  pageList: pageMetaData,
  props: {
    item: "data",
    totalCount: "total",
  },
  isPage: true,
});
const { tableStyle, getDataList, currentChangeHandle, sizeChangeHandle } = useTable(
  state
);

// 自定义变量
const queryTreeRef = ref();
const queryRef = ref();
const selectDatas = ref([]);
const selectCatalog: any = ref({});
// 清空搜索条件
const resetQuery = () => {
  queryRef.value?.resetFields();
  getDataList(1);
};

// 资源树使用的数据
const treeData = reactive({
  queryList: (name: String) => {
    return fetchListForRecursive({ catalogName: name, catalogState: 1 });
  },
});

// 状态信息数据
const infoData: any = ref({
  title: "",
  list: [
    { title: "资源项", num: 0, unit: "", tip: "总共有多少个数据项类型" },
    { title: "资源数量", num: 0, unit: "条", tip: "总共发布了多少个资源数" },
    { title: "近一月发布", num: 0, unit: "", tip: "近一个月内总共发布了多少个资源数" },
    { title: "资源数据量", num: 0, unit: "GB", tip: "所有资源的数据存储内存有多大" },
  ],
});
const getInfoData = async () => {
  if (!selectCatalog.value) return;
  let res = await countMetaData({ catalogNo: selectCatalog.value.catalogNo });
  let obj_c: any = {};
  res.data.forEach((obj: any) => {
    obj_c[obj.itemName] = {
      title: obj.itemName,
      num: (obj.itemCount + "").replace(/\B(?=(\d{3})+(?!\d))/g, ","),
      unit: obj.unit,
    };
  });
  infoData.value = {
    title: selectCatalog.value.catalogName,
    list: Object.values(obj_c),
  };
};
// 点击树
const handleNodeClick = (item: any, node: any) => {
  node.isCurrent = false;
  // if (item.catalogLevel == 3) {
  node.isCurrent = true;
  selectCatalog.value = item;
  getInfoData();
  state.queryForm.catalogNo = item.catalogNo;
  getDataList();
  // }
};
const handleTreeDataLoaded = (treeData: any, treeRef: any) => {
  return;
  let sel = null;
  for (let i in treeData) {
    for (let j in treeData[i].childCatalogs) {
      if (treeData[i].childCatalogs[j].childCatalogs?.length > 0) {
        sel = treeData[i].childCatalogs[j].childCatalogs[0];
        break;
      }
    }
    if (sel) break;
  }
  selectCatalog.value = sel;
  getInfoData();
  state.queryForm.catalogNo = selectCatalog.value.catalogNo;
  getDataList();
  nextTick(() => {
    treeRef!.setCurrentKey(selectCatalog.value.catalogNo);
  });
};
const onOperation = (type: string, record?: any) => {
  switch (type) {
    case "DoAdd":
      // 使用弹窗方式添加数据项
      handleAddItem();
      break;
    case "DoView":
      emit("openEditor", record, "view", selectCatalog.value);
      break;
    case "DoEdit":
      emit("openEditor", record, "edit", selectCatalog.value);
      break;
    case "DoDelete":
      onDelete(record);
      break;
    case "DoPublish":
      onPublish(record);
      break;
    case "DoEnable":
      onEnable(record);
      break;
    case "DoCopy":
      break;
  }
};
// 删除
const onDelete = async (obj: any) => {
  try {
    await useMessageBox().confirm("确认删除数据吗？");
  } catch {
    return;
  }
  try {
    obj.useState = 2;
    await editMetaData(obj);
    useMessage().success("删除成功");
    getDataList(1);
    getInfoData();
  } catch (err) {
    useMessage().error((err as any).msg);
  }
};
// 发布
const onPublish = async (obj: any) => {
  try {
    await useMessageBox().confirm("确认发布数据吗？");
  } catch {
    return;
  }
  try {
    obj.publishState = 1;
    await editMetaData(obj);
    useMessage().success("发布成功");
    getDataList(1);
    getInfoData();
  } catch (err) {
    useMessage().error((err as any).msg);
  }
};
// 启用/禁用
const onEnable = async (obj: any) => {
  let title = obj.useState == 1 ? "禁用" : "启用";
  try {
    await useMessageBox().confirm("确认" + title + "数据吗？");
  } catch {
    return;
  }
  try {
    obj.useState = obj.useState == 1 ? 0 : 1;
    await editMetaData(obj);
    useMessage().success(title + "成功");
    getDataList(1);
  } catch (err) {
    useMessage().error((err as any).msg);
  }
};
// 多选事件
const selectionChangHandle = (objs: any) => {
  selectDatas.value = objs;
};
defineExpose({
  getDataList,
  getInfoData,
});

// 新增数据项弹窗相关变量和函数
const dialogVisible = ref(false);
const dataFormRef = ref();
const loading = ref(false);
// 存储资源目录树数据
const resuorceData = ref([]);

// 表单数据
const form = reactive({
  id: "",
  assetsType: null, // 1元数据，2表数据
  name: "",
  catalogNo: "" as any,
  useState: 1,
  remark: "",
  assetsDepId: "",
  reportMode: 1,
  adminName: "",
  adminTel: "",
  reportRemark: "",
});

// 定义校验规则
const dataRules = ref({
  name: [{ required: true, message: "数据项名称不能为空", trigger: "blur" }],
  catalogNo: [{ required: true, message: "所属类目不能为空", trigger: "change" }],
  useState: [{ required: true, message: "启用状态不能为空", trigger: "blur" }],
  assetsDepId: [{ required: true, message: "来源单位不能为空", trigger: "blur" }],
  reportMode: [{ required: true, message: "接入方式不能为空", trigger: "blur" }],
});

// 点击新增数据项按钮
const handleAddItem = async () => {
  try {
    // 获取资源目录树数据
    loading.value = true;
    const res = await fetchListForRecursive({ catalogName: "", catalogState: 1 });
    resuorceData.value = res.data || [];

    // 重置表单数据
    form.catalogNo = selectCatalog.value?.catalogNo || "";
    form.name = "";
    form.remark = "";
    form.assetsDepId = "";
    form.adminName = "";
    form.adminTel = "";
    form.reportRemark = "";

    // 重置表单并打开对话框
    nextTick(() => {
      dataFormRef.value?.resetFields();
      dialogVisible.value = true;
    });
  } catch (err) {
    useMessage().error((err as any).msg || "获取资源目录失败");
  } finally {
    loading.value = false;
  }
};

const handleEditItem = async (row: any) => {
  try {
    // 获取资源目录树数据
    loading.value = true;
    const res = await fetchListForRecursive({ catalogName: "", catalogState: 1 });
    resuorceData.value = res.data || [];
    form.id = row.id;
    form.catalogNo = row.catalogNo;
    form.name = row.name;
    form.remark = row.remark;
    form.assetsDepId = row.assetsDepId;
    form.adminName = row.adminName;
    form.adminTel = row.adminTel;
    form.reportRemark = row.reportRemark;
    dialogVisible.value = true;
  } catch (err) {
    useMessage().error((err as any).msg || "获取资源目录失败");
  } finally {
    loading.value = false;
  }
}

// 提交表单
const onSubmitForm = async () => {
  // 表单验证
  const valid = await dataFormRef.value.validate().catch(() => { });
  if (!valid) return false;

  try {
    loading.value = true;
    const isEdit = form.id ? true : false;
    await isEdit ? editMetaData(form) : addMetaData(form);
    useMessage().success(isEdit ? '编辑成功' : '新增成功');
    dialogVisible.value = false;
    nextTick(() => {
      getDataList(); // 刷新数据列表
      getInfoData();
    });

  } catch (err) {
    useMessage().error((err as any).msg);
  } finally {
    loading.value = false;
  }
};
</script>

<style lang="scss">
/* 全局样式，确保弹窗样式能正确应用 */
.add-item-dialog {
  border-radius: 8px;

  .el-dialog__header {
    border-bottom: 1px solid rgba(240, 240, 240, 0.62);
    padding: 20px;
    margin-right: 0;
    position: relative;
  }

  .el-dialog__title {
    font-size: 16px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
    padding-left: 12px;
  }

  .el-dialog__body {
    padding: 20px 30px;
    max-height: calc(100vh - 200px);
    overflow-y: auto;
  }

  .el-dialog__footer {
    border-top: 1px solid #f0f0f0;
    padding: 15px 20px;
    display: flex;
    justify-content: right;
  }

  .form-subTitle {
    position: relative;
    font-size: 16px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    padding-left: 12px;
    display: flex;
    align-items: center;
    margin: 0 0 15px 0;
    line-height: 32px;

    .split-line {
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 16px;
      background-color: var(--el-color-primary, #A12F2F);
      border-radius: 2px;
    }
  }

  .dialog-form .el-form-item {
    margin-bottom: 18px;
  }

  .dialog-footer .el-button {
    font-size: 14px;
    padding: 9px 20px;
    border-radius: 2px;
    min-width: 80px;
    margin: 0 10px;

    &--primary {
      background-color: var(--el-color-primary, #A12F2F);
      border-color: var(--el-color-primary, #A12F2F);
    }
  }
}
</style>

<style scoped lang="scss">
/* 布局相关样式 */
.mtc-right {
  height: 100%;
  display: grid;
  grid-template-columns: 100%;
  grid-template-rows: 110px calc(100% - 115px);
  grid-column-gap: 10px;
  grid-row-gap: 5px;
}

.group-header {
  margin-bottom: 10px;

  .group-title {
    font-size: 16px;
    font-weight: 600;
    padding-left: 15px;
    position: relative;
    color: #554242;
    letter-spacing: 0.1em;
  }

  .group-add {
    cursor: pointer;
    color: var(--el-color-primary);
  }
}

.info-box {
  width: 100%;
  height: 100%;
  position: relative;

  .info-item {
    margin-bottom: 10px;
    line-height: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .info-left {
    width: 250px;
    height: 24px;
    margin-top: -2px;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
  }

  .catalog-label {
    font-weight: 400;
    font-size: 13px;
    color: rgba(0, 0, 0, 0.45);
    margin-right: 4px;
    line-height: 24px;
  }

  .catalog-path {
    font-weight: 500;
    font-size: 13px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 24px;
  }

  .info-right {
    width: 100%;
    height: 100%;
    padding: 0;
  }

  .stat-cards {
    :deep(.el-row) {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
      margin-bottom: 0;
    }

    :deep(.el-col) {
      padding: 0;
      margin-right: 0;
    }

    :deep(.el-card) {
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      border: 1px solid #f0f0f0;
      height: 80px;
      margin: 0;

      .el-card__body {
        padding: 12px 15px;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
    }

    .card-title {
      font-size: 13px;
      color: rgba(0, 0, 0, 0.45);
      margin-bottom: 8px;
    }

    .card-value {
      font-size: 20px;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
    }
  }

  .ib-key {
    width: 70px;
    text-align: left;
    color: rgba(0, 0, 0, 0.45);
    font-size: 13px;
    font-weight: normal;
  }

  .ib-value {
    max-width: calc(100% - 70px);
    text-align: left;
    font-size: 13px;
    color: rgba(0, 0, 0, 0.85);
    display: inline-block;
  }
}

.cl-item {
  cursor: pointer;
  display: contents;
  width: 90px;
  height: 90px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  border-radius: 4px;

  .image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: #ececec;
    color: #7a7a7a;
    font-size: 30px;

    .el-icon {
      font-size: 30px;
    }
  }

  .cli-tag {
    position: absolute;
    top: 12px;
    left: 20px;
    width: 100%;
    padding: 4px 0;
    text-align: center;
    color: white;
    font-size: 15px;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
  }
}

.operation-btns {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 1px;

  .op-button {
    margin: 0;
    padding: 0px;
    height: 24px;
    font-size: 13px;
    font-weight: normal;
    color: var(--el-color-primary, #A12F2F);
    width: 100%;
  }

  .more-button {
    margin-top: 2px;
  }

  .el-dropdown {
    margin: 0;
    width: 100%;

    .el-button {
      font-size: 13px;
      padding: 0;
      height: 24px;
      line-height: 24px;
      width: 100%;
      justify-content: center;
    }
  }

  .el-dropdown-item {
    font-size: 13px;
    padding: 5px 16px;
  }
}

.mt-box {
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: calc(20% - 5px) calc(80% - 5px);
  grid-template-rows: 100%;
  grid-column-gap: 5px;

  .resource-catalog {
    padding: 15px;
    height: 100%;
    width: 300px;
    background-color: #fff;
  }

  .resource-header {
    margin-bottom: 10px;
  }

  .layout-model-title {
    font-size: 16px;
    font-weight: 400;
    color: #1D1D1D;
    padding-left: 5px;
    margin-bottom: 0px;

  }

  .resource-content {
    background-color: #fff;
    border-radius: 5px;
  }

  .search-form {
    width: 100%;
    margin-bottom: 15px;
  }

  .resource-table {
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 8px;
    overflow: hidden;

    :deep(.el-table__inner-wrapper) {
      border-radius: 8px;
    }

    :deep(.el-table__row) {
      border-bottom: 1px solid #f0f0f0;

      &:hover {
        background-color: #F9F9F9;
      }

      &.table-row {
        cursor: pointer;
      }

      &:last-child {
        border-bottom: none;
      }
    }

    :deep(.el-table__cell) {
      vertical-align: middle;
    }

    :deep(.el-table__row .el-table__cell:not(:first-child):not(:last-child)) {
      text-align: left;

      .cell {
        text-align: left;
      }
    }

    :deep(.el-table__header-wrapper),
    :deep(.el-table__footer-wrapper) {
      border-radius: 8px 8px 0 0;
      overflow: hidden;
    }

    :deep(.cell) {
      padding: 0 10px;
    }
  }

  /* 表格中的cl-item已合并到上方的全局cl-item定义中 */
}

.add-item-dialog {
  :deep(.el-dialog__header) {
    border-bottom: 1px solid rgba(240, 240, 240, 0.62);
    padding: 20px;
    margin-right: 0;
    position: relative;

    &:before {
      content: "";
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 16px;
      background-color: var(--el-color-primary, #A12F2F);
    }
  }

  :deep(.el-dialog__title) {
    font-size: 16px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
    padding-left: 12px;
  }

  :deep(.el-dialog__body) {
    padding: 20px 30px;
    max-height: calc(100vh - 200px);
    overflow-y: auto;
  }

  :deep(.el-dialog__footer) {
    border-top: 1px solid #f0f0f0;
    padding: 15px 20px;
    display: flex;
    justify-content: center;
  }

  .form-subTitle {
    position: relative;
    font-size: 16px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    padding-left: 12px;
    display: flex;
    align-items: center;
    margin: 0 0 15px 0;
    line-height: 32px;

    .split-line {
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 16px;
      background-color: var(--el-color-primary, #A12F2F);
      border-radius: 2px;
    }
  }

  .layout-form-view {
    padding: 20px 15px 5px;
    background-color: #fafafa;
    border-radius: 4px;
    margin-bottom: 20px;
  }

  .dialog-footer {
    .el-button {
      font-size: 14px;
      padding: 10px 20px;
      border-radius: 4px;
      min-width: 100px;
      margin: 0 15px;

      &--primary {
        background-color: var(--el-color-primary, #A12F2F);
        border-color: var(--el-color-primary, #A12F2F);

        &:hover,
        &:focus {
          background-color: var(--el-color-primary-light-3, rgba(161, 47, 47, 0.9));
          border-color: var(--el-color-primary-light-3, rgba(161, 47, 47, 0.9));
        }
      }
    }
  }

  :deep(.el-form-item__label) {
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
  }

  .dialog-form {

    :deep(.el-input__wrapper),
    :deep(.el-select),
    :deep(.el-tree-select) {
      width: 100%;
      border-radius: 2px;
      box-shadow: 0 0 0 1px #d9d9d9 inset;
    }

    :deep(.el-input__wrapper.is-focus),
    :deep(.el-select .is-focus),
    :deep(.el-tree-select:focus-within .el-input__wrapper) {
      box-shadow: 0 0 0 1px var(--el-color-primary, #A12F2F) inset;
    }

    :deep(.el-input__wrapper:hover),
    :deep(.el-select:hover .el-input__wrapper),
    :deep(.el-tree-select:hover .el-input__wrapper) {
      box-shadow: 0 0 0 1px var(--el-color-primary, #A12F2F) inset;
    }

    :deep(.el-textarea__inner) {
      min-height: 80px;
      border-radius: 2px;
      padding: 8px 12px;

      &:focus {
        box-shadow: 0 0 0 1px var(--el-color-primary, #A12F2F) inset;
        border-color: var(--el-color-primary, #A12F2F);
      }

      &:hover {
        border-color: var(--el-color-primary, #A12F2F);
      }
    }

    :deep(.el-select__tags) {
      background-color: transparent;
    }
  }

  :deep(.resource-select-dropdown),
  :deep(.dept-select-dropdown) {
    .el-tree-node__content:hover {
      background-color: rgba(161, 47, 47, 0.1);
    }

    .el-tree-node.is-current>.el-tree-node__content {
      background-color: rgba(161, 47, 47, 0.1);
      color: var(--el-color-primary, #A12F2F);
    }
  }

  :deep(.el-form-item.is-required > .el-form-item__label::before) {
    color: var(--el-color-primary, #A12F2F);
  }

  :deep(.mb20) {
    margin-bottom: 22px;
  }
}
</style>
