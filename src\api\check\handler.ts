import request from '/@/utils/request'
export function getOverviewPass(query?: Object) {
    return request({
        url: '/check/patrolEventProcessor/page',
        method: 'get',
        params: query
    })
}
export function getUserPage(params?: Object) {
    return request({
        url: '/admin/user/page',
        method: 'get',
        params,
    });
}

export function putPatrolObj(obj: any) {
    return request({
        url: '/check/patrolEventProcessor',
        method: 'put',
        data: obj,
    })
}