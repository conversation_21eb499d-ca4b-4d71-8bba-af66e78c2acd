import request from '/@/utils/request'

export function fetchList(query?: Object) {
	return request({
		url: '/business/wb/hand/page',
		method: 'get',
		params: query,
	})
}

export function addObj(obj?: Object) {
	return request({
		url: '/business/wb/hand',
		method: 'post',
		data: obj,
	})
}

export function getObj(id?: string) {
	return request({
		url: '/business/wb/hand/' + id,
		method: 'get',
	})
}

export function delObjs(ids?: Object) {
	return request({
		url: '/business/wb/hand',
		method: 'delete',
		data: ids,
	})
}

export function putObj(obj?: Object) {
	return request({
		url: '/business/wb/hand',
		method: 'put',
		data: obj,
	})
}
