<template>
  <div class="commentary-container">
    <div class="form-content">
      <el-form ref="formRef" :model="formData" label-width="120px" class="commentary-form">
        <!-- 解说词内容 -->
        <div class="form-section">
          <el-form-item label="解说词">
            <el-input v-model="formData.commentary" type="textarea" :rows="15" maxlength="500" show-word-limit placeholder="请输入解说词内容" />
          </el-form-item>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'

interface Props {
  data: any
}

interface Emits {
  (e: 'update', field: string, value: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单数据
const formData = reactive({
  commentary: ''
})

// 表单引用
const formRef = ref()

// 监听props数据变化
watch(() => props.data, (newData) => {
  if (newData) {
    Object.assign(formData, newData)
  }
}, { immediate: true, deep: true })

// 监听表单数据变化，向父组件发送更新事件
// watch(formData, (newData) => {
//   Object.keys(newData).forEach(key => {
//     emit('update', key, (newData as any)[key])
//   })
// }, { deep: true })

// 获取表单数据
const getData = async () => {
  return { ...formData }
}

// 暴露方法给父组件
defineExpose({
  getData
})
</script>

<style lang="scss" scoped>
.commentary-container {
  height: 100%;
  overflow: auto;

  .form-content {
    padding: 20px;
  }

  .commentary-form {
    max-width: 1200px;
  }

  .form-section {
    margin-bottom: 32px;

    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e4e7ed;
    }
  }
}
</style>