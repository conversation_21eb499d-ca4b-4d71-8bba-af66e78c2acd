<template>
  <div class="layout-padding building-space">
    <!-- 页面头部 -->
    <div class="page-header">
      <el-button type="primary" @click="expandAll">
        <el-icon>
          <Expand />
        </el-icon>
        展开所有
      </el-button>
      <el-button type="primary" @click="collapseAll">
        <el-icon>
          <Fold />
        </el-icon>
        折叠所有
      </el-button>
      <div class="filter-area">
        <el-input v-model="state.queryForm.buildingName" placeholder="请输入建筑名称搜索" clearable
          style="width: 250px; margin-right: 13px;" @keyup.enter="getDataList" />
        <el-button @click="getDataList" type="primary" :icon="ZoomIn">查询</el-button>
        <el-button @click="resetQuery" :icon="Refresh">重置</el-button>
      </div>
    </div>

    <!-- 建筑空间表格树 -->
    <el-table ref="tableRef" :data="state.dataList" style="width: 100%" row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" v-loading="state.loading"
      header-cell-class-name="custom-table-header">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column prop="name" label="建筑名称" show-overflow-tooltip>
        <template #default="scope">
          <span style="display: inline-flex; align-items: center;">
            <span>{{ scope.row.name }}</span>
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="labels" label="标签" align="left" show-overflow-tooltip>
        <template #default="scope">
          <span v-if="scope.row.labels">{{ scope.row.labels }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="left">
        <template #default="scope">
          <el-button link type="primary" @click.stop="onOperation('DoAdd', scope.row)" v-if="scope.row.parentId === '0'"
            style="color:#4659D3;">
            新增
          </el-button>
          <el-button v-if="scope.row.parentId !== '0'" link type="primary"
            @click.stop="onOperation('DoEdit', scope.row)" style="color:#4659D3;">
            编辑
          </el-button>
          <el-button link v-if="scope.row.parentId !== '0'" type="danger"
            @click.stop="onOperation('DoDelete', scope.row)" :disabled="scope.row.children?.length > 0">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <BuildingDialog @refresh="getDataList()" ref="editorDialogRef" />
  </div>
</template>

<script setup lang="ts" name="buildingSpace">
import { ref, reactive, onMounted } from 'vue';
import { getBuildingTree, deleteBuilding } from '/@/api/backStageManage/building';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { ZoomIn, Refresh, Expand, Fold } from '@element-plus/icons-vue';
import BuildingDialog from './form.vue';

const { success, error } = useMessage();
const { confirm } = useMessageBox();

// 定义变量内容
const tableRef = ref();
const queryRef = ref();
const editorDialogRef = ref();

// 定义需要的数据
const state = reactive({
  loading: false,
  queryForm: {
    buildingType: '',
    buildingName: '',
  },
  dataList: [] as any[],
  buildingTypes: [] as any[], // 建筑类型列表
});


// 获取数据列表
const getDataList = async () => {
  state.loading = true;
  try {
    const res = await getBuildingTree(state.queryForm);
    state.dataList = res.data || [];
  } catch (err) {
    error('获取建筑空间数据失败');
  } finally {
    state.loading = false;
  }
};

// 重置查询
const resetQuery = () => {
  state.queryForm.buildingName = "";
  getDataList();
};

// 展开所有行
const expandAll = () => {
  // 遍历所有数据，展开有子节点的行
  const expandRows = (data: any[]) => {
    data.forEach(row => {
      if (row.children && row.children.length > 0) {
        tableRef.value?.toggleRowExpansion(row, true);
        expandRows(row.children);
      }
    });
  };
  expandRows(state.dataList);
};

// 折叠所有行
const collapseAll = () => {
  // 遍历所有数据，折叠有子节点的行
  const collapseRows = (data: any[]) => {
    data.forEach(row => {
      if (row.children && row.children.length > 0) {
        tableRef.value?.toggleRowExpansion(row, false);
        collapseRows(row.children);
      }
    });
  };
  collapseRows(state.dataList);
};

// 操作处理
const onOperation = async (type: string, row?: any) => {
  if (type === 'DoAdd') {
    editorDialogRef.value.openDialog(type, row);
  } else if (type === 'DoEdit') {
    editorDialogRef.value.openDialog(type, row);
  } else if (type === 'DoDelete') {
    try {
      await confirm(`确认删除"${row.name}"吗？删除后不可恢复！`);
      await deleteBuilding(row.id);
      success('删除成功');
      getDataList();
    } catch (err: any) {
      if (err !== 'cancel') {
        error(err.msg || '删除失败');
      }
    }
  }
};

// 页面加载时
onMounted(() => {
  getDataList();
});
</script>

<style scoped lang="scss">
.building-space {
  padding: 20px;
  background-color: white;
  height: calc(100%);
  width: calc(100%) !important;
  box-sizing: border-box;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    .filter-area {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      margin-left: 20px;
      margin-right: 10px;

      .el-form {
        margin: 0;
      }
    }
  }

  // 表格样式
  .el-table {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
  }

  // 自定义表头样式
  :deep(.custom-table-header) {
    background-color: var(--next-bg-main-color, #f5f7fa);
    color: rgba(0, 0, 0, 0.4);
    font-weight: 400;
    font-size: 14px;
    height: 50px;
  }

  // 表格内容样式
  :deep(.el-table .cell) {
    display: flex;
    align-items: center;

    .el-icon {
      margin-right: 5px;
      font-size: 16px;
      color: #606266;
    }

    .el-button--link {
      padding: 5px 8px;

      &.el-button--primary {
        color: var(--el-color-primary);
      }

      &.el-button--danger {
        color: var(--el-color-danger);
      }

      &:disabled {
        color: #c0c4cc;
        cursor: not-allowed;
        pointer-events: none;
      }
    }
  }

  // 修复居中对齐问题
  :deep(.el-table) {

    th.is-center .cell,
    td.is-center .cell {
      justify-content: center !important;
    }

    th.is-right .cell,
    td.is-right .cell {
      justify-content: flex-end !important;
    }

    th.is-left .cell,
    td.is-left .cell {
      justify-content: flex-start !important;
    }
  }

  // 状态标签样式
  :deep(.el-tag) {
    border-radius: 4px;
    padding: 0 10px;
    height: 28px;
    line-height: 28px;
    font-size: 12px;

    &.el-tag--success {
      background-color: var(--next-color-success-lighter, #f0f9eb);
      border-color: #2BA471;
      color: #2BA471;
    }

    &.el-tag--info {
      background-color: #f4f4f5;
      border-color: #0052D9;
      color: #0052D9;
    }

    &.el-tag--warning {
      background-color: #fdf6ec;
      border-color: #e6a23c;
      color: #e6a23c;
    }

    &.el-tag--danger {
      background-color: #fef0f0;
      border-color: #f56c6c;
      color: #f56c6c;
    }

    &.el-tag--primary {
      background-color: #ecf5ff;
      border-color: #409eff;
      color: #409eff;
    }
  }
}
</style>