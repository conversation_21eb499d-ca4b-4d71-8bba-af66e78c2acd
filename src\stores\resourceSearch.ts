import { defineStore } from 'pinia';

export const useResuorceSearchStore = defineStore({
  id: 'resuorceSearchStore',
  state: () => {
    return <any>{
      aiSearchConfig: {
        aiQuestion: '', // 检索-AI问答输入内容
        aiFile: null, // 检索-AI问答上传文件
        isSearchFile: false, // 检索-AI问答是否为文件检索
      }
    };
  },
  actions: {
    setAiSearchConfig(config: any) {
      this.aiSearchConfig = config;
    },
  },
  getters: {}
});
