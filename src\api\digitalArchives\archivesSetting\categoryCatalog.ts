import request from '/@/utils/request';

/**
 * 删除档案分类
 *
 */
export function removeCatalog(data: any) {
  return request({
    url: '/datacenter/catalog/remove',
    method: 'post',
    data
  });
}

/**
 * 添加分类
 *
 */
export function addCatalog(data: any) {
  return request({
    url: '/datacenter/catalog/add',
    method: 'post',
    data
  });
}

/**
 * 编辑分类
 *
 */
export function editCatalog(data: any) {
  return request({
    url: '/datacenter/catalog/edit',
    method: 'post',
    data
  });
}
/**
 * 获取列表
 *
 */
export function getListColumns(data: any) {
  return request({
    url: '/datacenter/catalog/list',
    method: 'post',
    data
  });
}
