<template>
  <div class="userInfo-container" v-loading="isLoading">
    <div class="info-display__title">我的信息</div>

    <div class="account-profile">
      <div class="title-box">账户资料</div>
      <div class="account-profile__item">
        <span class="label">账户头像：</span>
        <ImageUpload disabled v-model:imageUrl="avatarUrl" width="110px" height="110px" borderRadius="0%" @update:imageUrl="handleAvatarChange">
          <template #empty>
            <el-icon><Avatar /></el-icon>
            <span>请上传头像</span>
          </template>
        </ImageUpload>
        <span class="account-profile-change-avatar" @click="reUploadAvatar">修改头像</span>
      </div>
      <div class="account-profile__item">
        <span class="label">用户名：</span>
        <span class="value">{{ userInfo?.username || '暂无' }}</span>
        <span class="label">手机号码：</span>
        <span class="value">{{ userInfo?.phone || '暂无' }}</span>
      </div>
      <div class="account-profile__item">
        <span class="label">角色：</span>
        <span class="value">{{ getRoleName }}</span>
        <span class="label">邮箱：</span>
        <span class="value">{{ userInfo?.email || '-' }}</span>
      </div>
    </div>

    <div class="basic-info">
      <div class="title-box">
        <span>基本信息</span>
        <div style="margin-right: 60px">
          <el-button 
            type="primary" 
            link 
            class="edit-button" 
            @click="toggleEditMode"
            :icon="isEditing ? 'Close' : 'Edit'"
          >
            {{ isEditing ? '取消' : '编辑' }}
          </el-button>
          <el-button 
            v-if="isEditing" 
            type="primary" 
            link 
            class="save-button" 
            @click="saveUserInfo"
            :loading="saving"
            icon="Check"
          >
            保存
          </el-button>
        </div>
      </div>
      <div class="basic-info__item">
        <span class="label">姓名：</span>
        <span v-if="!isEditing" class="value">{{ userInfo?.name || '-' }}</span>
        <el-input v-else v-model="editForm.name" placeholder="请输入姓名" class="edit-input" />
        <span class="label">昵称:</span>
        <span v-if="!isEditing" class="value">{{ userInfo?.nickname || '-' }}</span>
        <el-input v-else v-model="editForm.nickname" placeholder="请输入昵称" class="edit-input" />
      </div>
      <div class="basic-info__item">
        <span class="label">部门：</span>
        <span class="value">{{ userInfo?.deptName || '-' }}</span>
        <span class="label">岗位：</span>
        <span class="value">{{ getPostName }}</span>
      </div>
      <div class="basic-info__item">
        <span class="label">备注：</span>
        <span v-if="!isEditing" class="value">{{ userInfo?.remark || '-' }}</span>
        <el-input 
          v-else 
          v-model="editForm.remark" 
          type="textarea" 
          :rows="2" 
          placeholder="请输入备注" 
          class="edit-input remark-input" 
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, defineEmits, ref, defineAsyncComponent, watch, reactive } from 'vue';
import { editInfo } from '/@/api/admin/user';
import { useMessage } from '/@/hooks/message';
import { useUserInfo } from '/@/stores/userInfo';

const ImageUpload = defineAsyncComponent(() => import('/@/components/Upload/Image.vue'));

const props = defineProps({
  userInfo: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['refresh']);
const isLoading = ref(false);

// Avatar URL for the ImageUpload component
const avatarUrl = ref('');

// Update avatarUrl when userInfo changes
watch(() => props.userInfo, (newVal) => {
  if (newVal?.avatar) {
    avatarUrl.value = newVal.avatar;
  }
}, { immediate: true });

// Handle avatar change
const handleAvatarChange = async (newAvatarUrl: string) => {
  if (!props.userInfo?.userId) {
    useMessage().error('用户信息不完整，无法更新头像');
    return;
  }

  try {
    // Only send the userId and avatar fields
    const params = {
      userId: props.userInfo.userId,
      avatar: newAvatarUrl
    };
    
    isLoading.value = true;
    await editInfo(params);
    isLoading.value = false;
    useMessage().success('头像更新成功');
    
    // Update user info in the store
    useUserInfo().setUserInfos();
    
    // Notify parent component to refresh
    emit('refresh');
  } catch (err: any) {
    useMessage().error(err.msg || '头像更新失败');
    // Revert to original avatar on error
    avatarUrl.value = props.userInfo.avatar || '';
  }
};

// Re-upload avatar
const reUploadAvatar = () => {
  document.querySelector('.el-upload input[type="file"]')!.click();
};

// 获取角色名称
const getRoleName = computed(() => {
  if (!props.userInfo?.roleList?.length) return '暂无';
  return props.userInfo.roleList.map((role: any) => role.roleName).join(', ');
});

// 获取岗位名称
const getPostName = computed(() => {
  if (!props.userInfo?.postList?.length) return '暂无';
  return props.userInfo.postList.map((post: any) => post.postName).join(', ');
});

// 编辑模式状态
const isEditing = ref(false);
const saving = ref(false);

// 编辑表单数据
const editForm = reactive({
  name: '',
  nickname: '',
  remark: ''
});

// 切换编辑模式
const toggleEditMode = () => {
  if (isEditing.value) {
    // 取消编辑，重置表单
    isEditing.value = false;
  } else {
    // 开始编辑，初始化表单数据
    editForm.name = props.userInfo?.name || '';
    editForm.nickname = props.userInfo?.nickname || '';
    editForm.remark = props.userInfo?.remark || '';
    isEditing.value = true;
  }
};

// 保存用户信息
const saveUserInfo = async () => {
  if (!props.userInfo?.userId) {
    useMessage().error('用户信息不完整，无法更新');
    return;
  }

  try {
    saving.value = true;
    
    // 构建更新参数
    const params = {
      userId: props.userInfo.userId,
      name: editForm.name,
      nickname: editForm.nickname,
      remark: editForm.remark
    };
    isLoading.value = true;
    await editInfo(params);
    isLoading.value = false;
    useMessage().success('信息更新成功');
    
    // 更新用户信息
    useUserInfo().setUserInfos();
    
    // 通知父组件刷新
    emit('refresh');
    
    // 退出编辑模式
    isEditing.value = false;
  } catch (err: any) {
    useMessage().error(err.msg || '信息更新失败');
  } finally {
    saving.value = false;
  }
};
</script>

<style scoped lang="scss">
.userInfo-container {
  .info-display__title {
    margin-bottom: 10px;
    text-align: center;
    position: relative;
    font-weight: 900;
    font-size: 24px;
    line-height: 24.61px;
    letter-spacing: 0%;
    text-align: center;
    vertical-align: middle;
    color: #393939;
  }

  .account-profile,
  .basic-info {
    .title-box {
      margin: 20px 0;
      font-weight: 700;
      font-size: 16px;
      line-height: 24.61px;
      color: #862626;
      position: relative;
      padding-left: 12px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      &::before {
        content: "";
        position: absolute;
        width: 2px;
        height: 20px;
        background: #862626;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
      }
      
      .edit-button, .save-button {
        margin-left: 15px;
        font-size: 14px;
        color: #CBAC6B;
      }
    }

    .account-profile-change-avatar {
      margin-left: 15px;
      cursor: pointer;
      color: #CBAC6B;
    }

    &__item {
      display: flex;
      flex-wrap: wrap;
      align-items: start;
      margin-bottom: 15px;

      .label {
        width: 70px;
        text-align: left;
        margin-right: 10px;
        font-weight: 500;
        font-size: 14px;
        line-height: 20px;
        letter-spacing: 0%;
        color: #a1a1a1;
      }

      .value {
        flex: 1;
        min-width: 150px;
        font-weight: 500;
        font-size: 14px;
        line-height: 20px;
        letter-spacing: 0%;
        color: #393939;
      }
      
      .edit-input {
        flex: 1;
        margin-right: 60px;
      }
      
      .remark-input {
        margin-top: 5px;
      }
    }
  }
}
</style>
